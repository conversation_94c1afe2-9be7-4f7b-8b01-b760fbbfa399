import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import { Router } from '@angular/router';
import {
  FlightSearchRequest,
  FlightSearchResponse,
  FlightSearchForm,
  LatestSearch,
  Location
} from '../models/flight.models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class FlightService {
  private readonly API_URL = `${environment.apiUrl}/product`;
  private readonly LATEST_SEARCHES_KEY = 'flight_latest_searches';
  private readonly MAX_LATEST_SEARCHES = 6;

  private latestSearchesSubject = new BehaviorSubject<LatestSearch[]>(this.getLatestSearches());
  public latestSearches$ = this.latestSearchesSubject.asObservable();

  constructor(
    private http: HttpClient,
    private router: Router
  ) {}

  /**
   * Search for one-way flights
   */
  searchOneWayFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {
    const request = this.buildOneWayRequest(searchForm);
    return this.searchFlights(request);
  }

  /**
   * Search for round-trip flights
   */
  searchRoundTripFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {
    const request = this.buildRoundTripRequest(searchForm);
    return this.searchFlights(request);
  }

  /**
   * Search for multi-city flights
   */
  searchMultiCityFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {
    const request = this.buildMultiCityRequest(searchForm);
    return this.searchFlights(request);
  }

  /**
   * Generic flight search method
   */
  private searchFlights(request: FlightSearchRequest): Observable<FlightSearchResponse> {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return throwError('Authentication token not found');
    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    // Debug logging
    console.log('Flight search request:', JSON.stringify(request, null, 2));
    console.log('API URL:', `${this.API_URL}/pricesearch`);
    console.log('Headers:', headers);

    return this.http.post<FlightSearchResponse>(`${this.API_URL}/pricesearch`, request, { headers })
      .pipe(
        tap(response => {
          console.log('Flight search response:', response);
          if (response.header.success) {
            console.log('Flight search successful:', response);
          } else {
            console.error('Flight search failed:', response.header.messages);
          }
        }),
        catchError(error => {
          console.error('Flight search error details:', {
            status: error.status,
            statusText: error.statusText,
            message: error.message,
            error: error.error,
            url: error.url
          });

          // Handle token expiry (401 Unauthorized)
          if (error.status === 401) {
            console.warn('Authentication failed - token may be expired');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('token_expiry');
            localStorage.removeItem('user_data');
            this.router.navigate(['/signin']);
            return throwError('Authentication token expired. Please login again.');
          }

          return throwError(error);
        })
      );
  }

  /**
   * Build one-way flight request according to Paximum API documentation
   */
  private buildOneWayRequest(searchForm: FlightSearchForm): FlightSearchRequest {
    const request: FlightSearchRequest = {
      ProductType: 3, // Flight product type (3 according to documentation)
      ServiceTypes: ['1'], // One-way service type (1 = OneWay)
      CheckIn: this.formatDateForAPI(searchForm.departureDate),
      DepartureLocations: [this.parseLocation(searchForm.from)],
      ArrivalLocations: [this.parseLocation(searchForm.to)],
      Passengers: this.buildPassengers(searchForm.passengers),
      showOnlyNonStopFlight: searchForm.directFlights,
      acceptPendingProviders: false,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: true, // Set to true as per documentation
      calculateFlightFees: true, // Enable to get service fees
      flightClasses: [this.getFlightClassCode(searchForm.class)],
      Culture: 'en-US',
      Currency: 'TND'
    };

    // Add optional additionalParameters
    request.additionalParameters = {
      getOptionsParameters: {
        flightBaggageGetOption: searchForm.baggage === 'all' ? 0 : 1
      }
    };

    // Add corporate codes if needed (optional)
    if (searchForm.preferredAirline) {
      request.additionalParameters.CorporateCodes = [{
        Code: '123456',
        Rule: {
          Airline: searchForm.preferredAirline,
          Supplier: 'PXMTK'
        }
      }];
    }

    return request;
  }

  /**
   * Build round-trip flight request
   */
  private buildRoundTripRequest(searchForm: FlightSearchForm): FlightSearchRequest {
    const request = this.buildOneWayRequest(searchForm);
    request.ServiceTypes = ['2']; // Round-trip service type
    request.ReturnDate = this.formatDateForAPI(searchForm.returnDate!);
    request.Night = this.calculateNights(searchForm.departureDate, searchForm.returnDate!);
    return request;
  }

  /**
   * Build multi-city flight request
   */
  private buildMultiCityRequest(searchForm: FlightSearchForm): FlightSearchRequest {
    const request = this.buildOneWayRequest(searchForm);
    request.ServiceTypes = ['3']; // Multi-city service type
    return request;
  }

  /**
   * Parse location string to Location object
   */
  private parseLocation(locationString: string): Location {
    // Extract airport code from the location string
    // Handle formats like "IST - Istanbul Airport" or just "IST"
    let code = locationString.trim().toUpperCase();

    // If the string contains " - ", take the part before it as the code
    if (code.includes(' - ')) {
      code = code.split(' - ')[0].trim();
    }

    // If the string contains spaces, take the first part as the code
    if (code.includes(' ')) {
      code = code.split(' ')[0].trim();
    }

    return {
      type: 2, // Airport type (2 = Airport according to Paximum documentation)
      id: code,
      name: locationString,
      code: code
    };
  }

  /**
   * Build passengers array from form data
   */
  private buildPassengers(passengers: { adults: number; children: number; infants: number }): any[] {
    const passengerArray = [];

    if (passengers.adults > 0) {
      passengerArray.push({ type: 1, count: passengers.adults }); // Adult
    }

    if (passengers.children > 0) {
      passengerArray.push({ type: 2, count: passengers.children }); // Child
    }

    if (passengers.infants > 0) {
      passengerArray.push({ type: 3, count: passengers.infants }); // Infant
    }

    return passengerArray;
  }

  /**
   * Get flight class code
   */
  private getFlightClassCode(flightClass: string): number {
    switch (flightClass) {
      case 'economy': return 1;
      case 'business': return 2;
      case 'first': return 3;
      default: return 1;
    }
  }

  /**
   * Calculate nights between dates
   */
  private calculateNights(checkIn: string, returnDate: string): number {
    const checkInDate = new Date(checkIn);
    const returnDateObj = new Date(returnDate);
    const timeDiff = returnDateObj.getTime() - checkInDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  /**
   * Format date for API (YYYY-MM-DD format)
   */
  private formatDateForAPI(dateString: string): string {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  /**
   * Save search to latest searches
   */
  saveLatestSearch(searchForm: FlightSearchForm): void {
    const search: LatestSearch = {
      id: Date.now().toString(),
      from: searchForm.from,
      to: searchForm.to,
      date: searchForm.departureDate,
      passengers: searchForm.passengers.adults + searchForm.passengers.children + searchForm.passengers.infants,
      searchDate: new Date()
    };

    const searches = this.getLatestSearches();
    searches.unshift(search);

    // Keep only the latest searches
    const limitedSearches = searches.slice(0, this.MAX_LATEST_SEARCHES);

    localStorage.setItem(this.LATEST_SEARCHES_KEY, JSON.stringify(limitedSearches));
    this.latestSearchesSubject.next(limitedSearches);
  }

  /**
   * Get latest searches from localStorage
   */
  private getLatestSearches(): LatestSearch[] {
    try {
      const searches = localStorage.getItem(this.LATEST_SEARCHES_KEY);
      return searches ? JSON.parse(searches) : [];
    } catch (error) {
      console.error('Error parsing latest searches:', error);
      return [];
    }
  }

  /**
   * Clear latest searches
   */
  clearLatestSearches(): void {
    localStorage.removeItem(this.LATEST_SEARCHES_KEY);
    this.latestSearchesSubject.next([]);
  }

  /**
   * Get popular destinations (mock data for now)
   */
  getPopularDestinations(): Observable<Location[]> {
    const destinations: Location[] = [
      { type: 1, id: 'TUN', name: 'Tunis, Tunisia', code: 'TUN' },
      { type: 1, id: 'IST', name: 'Istanbul, Turkey', code: 'IST' },
      { type: 1, id: 'CDG', name: 'Paris, France', code: 'CDG' },
      { type: 1, id: 'LHR', name: 'London, UK', code: 'LHR' },
      { type: 1, id: 'DXB', name: 'Dubai, UAE', code: 'DXB' },
      { type: 1, id: 'JFK', name: 'New York, USA', code: 'JFK' }
    ];

    return new Observable(observer => {
      observer.next(destinations);
      observer.complete();
    });
  }

  /**
   * Search locations (mock implementation)
   */
  searchLocations(query: string): Observable<Location[]> {
    return this.getPopularDestinations().pipe(
      map(destinations =>
        destinations.filter(dest =>
          dest.name.toLowerCase().includes(query.toLowerCase()) ||
          dest.code.toLowerCase().includes(query.toLowerCase())
        )
      )
    );
  }
}
