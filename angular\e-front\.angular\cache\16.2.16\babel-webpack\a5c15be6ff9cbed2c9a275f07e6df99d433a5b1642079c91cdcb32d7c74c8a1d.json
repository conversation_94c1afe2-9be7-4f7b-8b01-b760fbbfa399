{"ast": null, "code": "import { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthInterceptor {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isRefreshing = false;\n    this.refreshTokenSubject = new BehaviorSubject(null);\n  }\n  intercept(req, next) {\n    // Add auth header if token exists\n    const authReq = this.addTokenHeader(req);\n    return next.handle(authReq).pipe(catchError(error => {\n      if (error.status === 401) {\n        return this.handle401Error(authReq, next);\n      }\n      return throwError(error);\n    }));\n  }\n  addTokenHeader(request) {\n    const token = this.authService.getToken();\n    if (token && !request.url.includes('/auth/login')) {\n      return request.clone({\n        headers: request.headers.set('Authorization', `Bearer ${token}`)\n      });\n    }\n    return request;\n  }\n  handle401Error(request, next) {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n      // Check if token is expired\n      if (this.authService.isAuthenticated()) {\n        console.warn('Token expired, redirecting to login');\n        this.authService.logout();\n        this.router.navigate(['/signin']);\n        return throwError('Token expired');\n      } else {\n        // Token is already invalid, redirect to login\n        this.authService.logout();\n        this.router.navigate(['/signin']);\n        return throwError('Authentication required');\n      }\n    }\n    return this.refreshTokenSubject.pipe(filter(token => token !== null), take(1), switchMap(() => next.handle(this.addTokenHeader(request))));\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "BehaviorSubject", "catchError", "filter", "take", "switchMap", "AuthInterceptor", "constructor", "authService", "router", "isRefreshing", "refreshTokenSubject", "intercept", "req", "next", "authReq", "addTokenHeader", "handle", "pipe", "error", "status", "handle401Error", "request", "token", "getToken", "url", "includes", "clone", "headers", "set", "isAuthenticated", "console", "warn", "logout", "navigate", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  private isRefreshing = false;\n  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Add auth header if token exists\n    const authReq = this.addTokenHeader(req);\n\n    return next.handle(authReq).pipe(\n      catchError((error: HttpErrorResponse) => {\n        if (error.status === 401) {\n          return this.handle401Error(authReq, next);\n        }\n        return throwError(error);\n      })\n    );\n  }\n\n  private addTokenHeader(request: HttpRequest<any>): HttpRequest<any> {\n    const token = this.authService.getToken();\n    if (token && !request.url.includes('/auth/login')) {\n      return request.clone({\n        headers: request.headers.set('Authorization', `Bearer ${token}`)\n      });\n    }\n    return request;\n  }\n\n  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    if (!this.isRefreshing) {\n      this.isRefreshing = true;\n      this.refreshTokenSubject.next(null);\n\n      // Check if token is expired\n      if (this.authService.isAuthenticated()) {\n        console.warn('Token expired, redirecting to login');\n        this.authService.logout();\n        this.router.navigate(['/signin']);\n        return throwError('Token expired');\n      } else {\n        // Token is already invalid, redirect to login\n        this.authService.logout();\n        this.router.navigate(['/signin']);\n        return throwError('Authentication required');\n      }\n    }\n\n    return this.refreshTokenSubject.pipe(\n      filter(token => token !== null),\n      take(1),\n      switchMap(() => next.handle(this.addTokenHeader(request)))\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,EAAEC,eAAe,QAAQ,MAAM;AAC9D,SAASC,UAAU,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,QAAQ,gBAAgB;;;;AAKpE,OAAM,MAAOC,eAAe;EAI1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IALR,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,mBAAmB,GAAyB,IAAIV,eAAe,CAAM,IAAI,CAAC;EAK/E;EAEHW,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD;IACA,MAAMC,OAAO,GAAG,IAAI,CAACC,cAAc,CAACH,GAAG,CAAC;IAExC,OAAOC,IAAI,CAACG,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC9BhB,UAAU,CAAEiB,KAAwB,IAAI;MACtC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACxB,OAAO,IAAI,CAACC,cAAc,CAACN,OAAO,EAAED,IAAI,CAAC;;MAE3C,OAAOd,UAAU,CAACmB,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACH;EAEQH,cAAcA,CAACM,OAAyB;IAC9C,MAAMC,KAAK,GAAG,IAAI,CAACf,WAAW,CAACgB,QAAQ,EAAE;IACzC,IAAID,KAAK,IAAI,CAACD,OAAO,CAACG,GAAG,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;MACjD,OAAOJ,OAAO,CAACK,KAAK,CAAC;QACnBC,OAAO,EAAEN,OAAO,CAACM,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUN,KAAK,EAAE;OAChE,CAAC;;IAEJ,OAAOD,OAAO;EAChB;EAEQD,cAAcA,CAACC,OAAyB,EAAER,IAAiB;IACjE,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;MACtB,IAAI,CAACA,YAAY,GAAG,IAAI;MACxB,IAAI,CAACC,mBAAmB,CAACG,IAAI,CAAC,IAAI,CAAC;MAEnC;MACA,IAAI,IAAI,CAACN,WAAW,CAACsB,eAAe,EAAE,EAAE;QACtCC,OAAO,CAACC,IAAI,CAAC,qCAAqC,CAAC;QACnD,IAAI,CAACxB,WAAW,CAACyB,MAAM,EAAE;QACzB,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC,OAAOlC,UAAU,CAAC,eAAe,CAAC;OACnC,MAAM;QACL;QACA,IAAI,CAACQ,WAAW,CAACyB,MAAM,EAAE;QACzB,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;QACjC,OAAOlC,UAAU,CAAC,yBAAyB,CAAC;;;IAIhD,OAAO,IAAI,CAACW,mBAAmB,CAACO,IAAI,CAClCf,MAAM,CAACoB,KAAK,IAAIA,KAAK,KAAK,IAAI,CAAC,EAC/BnB,IAAI,CAAC,CAAC,CAAC,EACPC,SAAS,CAAC,MAAMS,IAAI,CAACG,MAAM,CAAC,IAAI,CAACD,cAAc,CAACM,OAAO,CAAC,CAAC,CAAC,CAC3D;EACH;;;uBAzDWhB,eAAe,EAAA6B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAflC,eAAe;MAAAmC,OAAA,EAAfnC,eAAe,CAAAoC;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}