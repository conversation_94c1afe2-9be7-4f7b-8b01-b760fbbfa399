{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DashboardComponent_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx_r0.currentUser.name, \"\");\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"span\", 54);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 55);\n    i0.ɵɵtext(5, \"Balance: 8500.48 TND\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 56);\n    i0.ɵɵtext(7, \"Due: -1399.52 TND\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.agencyCode, \" - Workspace for Demo\");\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 103,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"brand-logo\"], [1, \"logo-text\"], [\"class\", \"dashboard-subtitle\", 4, \"ngIf\"], [1, \"header-right\"], [1, \"top-nav\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [1, \"fas\", \"fa-bell\"], [1, \"fas\", \"fa-wrench\"], [1, \"fas\", \"fa-globe\"], [1, \"logout-button\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"dashboard-main\"], [1, \"navigation-grid\"], [1, \"row\"], [1, \"nav-card\", \"booking-queue\"], [1, \"card-icon\"], [1, \"fas\", \"fa-clipboard-list\"], [1, \"card-title\"], [1, \"nav-card\", \"commissions\"], [1, \"fas\", \"fa-percentage\"], [1, \"nav-card\", \"sub-agent\"], [1, \"fas\", \"fa-users\"], [1, \"nav-card\", \"helpdesk\"], [1, \"fas\", \"fa-headset\"], [1, \"nav-card\", \"profile\"], [1, \"fas\", \"fa-user\"], [1, \"nav-card\", \"finance\"], [1, \"fas\", \"fa-money-bill-wave\"], [1, \"nav-card\", \"agency-profile\"], [1, \"fas\", \"fa-building\"], [1, \"row\", \"featured-row\"], [1, \"nav-card\", \"featured\", \"flights\"], [1, \"fas\", \"fa-plane\"], [1, \"nav-card\", \"featured\", \"hotels\"], [1, \"fas\", \"fa-hotel\"], [1, \"nav-card\", \"news\"], [1, \"fas\", \"fa-newspaper\"], [1, \"nav-card\", \"flight-info\"], [1, \"fas\", \"fa-info-circle\"], [1, \"nav-card\", \"featured\", \"charter\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"nav-card\", \"passengers\"], [1, \"fas\", \"fa-user-friends\"], [1, \"nav-card\", \"credit-request\"], [1, \"fas\", \"fa-credit-card\"], [1, \"dashboard-subtitle\"], [1, \"user-info\"], [1, \"agency-info\"], [1, \"agency-id\"], [1, \"balance\"], [1, \"due-info\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n          i0.ɵɵtext(6, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, DashboardComponent_p_7_Template, 2, 1, \"p\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"nav\", 8)(10, \"a\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵtext(12, \" Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"a\", 9);\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵtext(15, \" News\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"a\", 9);\n          i0.ɵɵelement(17, \"i\", 12);\n          i0.ɵɵtext(18, \" Tools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"a\", 9);\n          i0.ɵɵelement(20, \"i\", 13);\n          i0.ɵɵtext(21, \" Languages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_22_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(23, \"i\", 15);\n          i0.ɵɵtext(24, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 8, 1, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"main\", 17)(27, \"div\", 18)(28, \"div\", 19)(29, \"div\", 20)(30, \"div\", 21);\n          i0.ɵɵelement(31, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\", 23);\n          i0.ɵɵtext(33, \"Booking Queue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 24)(35, \"div\", 21);\n          i0.ɵɵelement(36, \"i\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 23);\n          i0.ɵɵtext(38, \"Commissions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 26)(40, \"div\", 21);\n          i0.ɵɵelement(41, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 23);\n          i0.ɵɵtext(43, \"Sub Agent\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 19)(45, \"div\", 28)(46, \"div\", 21);\n          i0.ɵɵelement(47, \"i\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\", 23);\n          i0.ɵɵtext(49, \"Helpdesk\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 30)(51, \"div\", 21);\n          i0.ɵɵelement(52, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 23);\n          i0.ɵɵtext(54, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 32)(56, \"div\", 21);\n          i0.ɵɵelement(57, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 23);\n          i0.ɵɵtext(59, \"Finance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\", 21);\n          i0.ɵɵelement(62, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 23);\n          i0.ɵɵtext(64, \"Agency Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 36)(66, \"div\", 37)(67, \"div\", 21);\n          i0.ɵɵelement(68, \"i\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"span\", 23);\n          i0.ɵɵtext(70, \"Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 39)(72, \"div\", 21);\n          i0.ɵɵelement(73, \"i\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\", 23);\n          i0.ɵɵtext(75, \"Hotels\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(76, \"div\", 19)(77, \"div\", 41)(78, \"div\", 21);\n          i0.ɵɵelement(79, \"i\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"span\", 23);\n          i0.ɵɵtext(81, \"News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 43)(83, \"div\", 21);\n          i0.ɵɵelement(84, \"i\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 23);\n          i0.ɵɵtext(86, \"Flight Info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 45)(88, \"div\", 21);\n          i0.ɵɵelement(89, \"i\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"span\", 23);\n          i0.ɵɵtext(91, \"Charter\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"div\", 19)(93, \"div\", 47)(94, \"div\", 21);\n          i0.ɵɵelement(95, \"i\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"span\", 23);\n          i0.ɵɵtext(97, \"Passengers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 49)(99, \"div\", 21);\n          i0.ɵɵelement(100, \"i\", 50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"span\", 23);\n          i0.ɵɵtext(102, \"Credit Request\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  background-color: #f8f9fa;\\n  font-family: \\\"Segoe UI\\\", Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #4a63a2, #3a4e7f);\\n  color: #fff;\\n  padding: 15px 20px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: bold;\\n  margin-right: 20px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  opacity: 0.9;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: #fff;\\n  text-decoration: none;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  transition: background-color 0.3s;\\n  font-size: 14px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: #fff;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: all 0.3s;\\n  font-size: 14px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  padding: 10px 20px;\\n  margin-top: 15px;\\n  border-radius: 4px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 25px;\\n  align-items: center;\\n  font-size: 14px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .agency-id[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%] {\\n  color: #3cc7b7;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .due-info[_ngcontent-%COMP%] {\\n  color: #ff9f43;\\n}\\n\\n.dashboard-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n.navigation-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n.navigation-grid[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  flex-wrap: wrap;\\n}\\n.navigation-grid[_ngcontent-%COMP%]   .row.featured-row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 180px;\\n}\\n\\n.nav-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 150px;\\n  flex: 1;\\n  transition: all 0.3s;\\n  min-height: 140px;\\n  text-align: center;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.nav-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 5px;\\n  background: linear-gradient(90deg, #4a63a2, #5b9bd5);\\n  opacity: 0.7;\\n}\\n.nav-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\\n}\\n.nav-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n.nav-card[_ngcontent-%COMP%]:hover   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  margin-bottom: 15px;\\n  color: #4a63a2;\\n  height: 50px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s;\\n}\\n.nav-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.nav-card.booking-queue[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #4a63a2;\\n}\\n.nav-card.commissions[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #5b9bd5;\\n}\\n.nav-card.support[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #3cc7b7;\\n}\\n.nav-card.sub-agent[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #ff9f43;\\n}\\n.nav-card.helpdesk[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.nav-card.profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.nav-card.finance[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #28c76f;\\n}\\n.nav-card.agency-profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #7367f0;\\n}\\n.nav-card.flights[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #4a63a2;\\n}\\n.nav-card.hotels[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #5b9bd5;\\n}\\n.nav-card.charter[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #ff5f5f;\\n}\\n.nav-card.news[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #ff9f43;\\n}\\n.nav-card.flight-info[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #5b9bd5;\\n}\\n.nav-card.passengers[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #7367f0;\\n}\\n.nav-card.credit-request[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #28c76f;\\n}\\n.nav-card.featured[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\n}\\n.nav-card.featured[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n}\\n.nav-card.featured[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n\\n@media (max-width: 1200px) {\\n  .row[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: calc(33.333% - 20px);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  .dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: calc(50% - 10px);\\n  }\\n}\\n@media (max-width: 576px) {\\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "currentUser", "name", "ctx_r1", "agencyCode", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_p_7_Template", "ɵɵelement", "ɵɵlistener", "DashboardComponent_Template_button_click_22_listener", "DashboardComponent_div_25_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any = null;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header with Block to Book branding -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <div class=\"brand-logo\">\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <p class=\"dashboard-subtitle\" *ngIf=\"currentUser\">Welcome back, {{ currentUser.name }}</p>\n      </div>\n      <div class=\"header-right\">\n        <nav class=\"top-nav\">\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-home\"></i> Home</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-bell\"></i> News</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-wrench\"></i> Tools</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-globe\"></i> Languages</a>\n          <button class=\"logout-button\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i> Logout\n          </button>\n        </nav>\n      </div>\n    </div>\n    <div class=\"user-info\" *ngIf=\"currentUser\">\n      <div class=\"agency-info\">\n        <span class=\"agency-id\">{{ currentUser.agencyCode }} - Workspace for Demo</span>\n        <span class=\"balance\">Balance: 8500.48 TND</span>\n        <span class=\"due-info\">Due: -1399.52 TND</span>\n      </div>\n    </div>\n  </header>\n\n  <!-- Main content with navigation cards -->\n  <main class=\"dashboard-main\">\n    <div class=\"navigation-grid\">\n      <!-- First row of cards -->\n      <div class=\"row\">\n        <!-- Booking Queue -->\n        <div class=\"nav-card booking-queue\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-clipboard-list\"></i>\n          </div>\n          <span class=\"card-title\">Booking Queue</span>\n        </div>\n\n        <!-- Commissions -->\n        <div class=\"nav-card commissions\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-percentage\"></i>\n          </div>\n          <span class=\"card-title\">Commissions</span>\n        </div>\n\n\n\n        <!-- Sub-Agent -->\n        <div class=\"nav-card sub-agent\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-users\"></i>\n          </div>\n          <span class=\"card-title\">Sub Agent</span>\n        </div>\n      </div>\n\n      <!-- Second row of cards -->\n      <div class=\"row\">\n        <!-- Helpdesk -->\n        <div class=\"nav-card helpdesk\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-headset\"></i>\n          </div>\n          <span class=\"card-title\">Helpdesk</span>\n        </div>\n\n        <!-- Profile -->\n        <div class=\"nav-card profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user\"></i>\n          </div>\n          <span class=\"card-title\">Profile</span>\n        </div>\n\n        <!-- Finance -->\n        <div class=\"nav-card finance\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <span class=\"card-title\">Finance</span>\n        </div>\n\n        <!-- Agency Profile -->\n        <div class=\"nav-card agency-profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-building\"></i>\n          </div>\n          <span class=\"card-title\">Agency Profile</span>\n        </div>\n      </div>\n\n      <!-- Third row with larger cards -->\n      <div class=\"row featured-row\">\n        <!-- Flights -->\n        <div class=\"nav-card featured flights\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <span class=\"card-title\">Flights</span>\n        </div>\n\n        <!-- Hotels -->\n        <div class=\"nav-card featured hotels\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-hotel\"></i>\n          </div>\n          <span class=\"card-title\">Hotels</span>\n        </div>\n      </div>\n\n      <!-- Fourth row -->\n      <div class=\"row\">\n        <!-- News -->\n        <div class=\"nav-card news\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-newspaper\"></i>\n          </div>\n          <span class=\"card-title\">News</span>\n        </div>\n\n        <!-- Flight Info -->\n        <div class=\"nav-card flight-info\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-info-circle\"></i>\n          </div>\n          <span class=\"card-title\">Flight Info</span>\n        </div>\n\n        <!-- Charter -->\n        <div class=\"nav-card featured charter\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane-departure\"></i>\n          </div>\n          <span class=\"card-title\">Charter</span>\n        </div>\n      </div>\n\n      <!-- Fifth row -->\n      <div class=\"row\">\n        <!-- Passengers -->\n        <div class=\"nav-card passengers\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user-friends\"></i>\n          </div>\n          <span class=\"card-title\">Passengers</span>\n        </div>\n\n        <!-- Credit Request -->\n        <div class=\"nav-card credit-request\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <span class=\"card-title\">Credit Request</span>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;;;;ICQQA,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAxCH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,KAAoC;;;;;IAc1FR,EAAA,CAAAC,cAAA,cAA2C;IAEfD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFvBH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAI,MAAA,CAAAF,WAAA,CAAAG,UAAA,0BAAiD;;;ADfjF,OAAM,MAAOC,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAP,WAAW,GAAQ,IAAI;EAKpB;EAEHQ,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACR,WAAW,CAACQ,MAAM,EAAE;IACzB,IAAI,CAACP,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBA1BWT,kBAAkB,EAAAX,EAAA,CAAAsB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxB,EAAA,CAAAsB,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBf,kBAAkB;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BjC,EAAA,CAAAC,cAAA,aAAiC;UAMCD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAmC,UAAA,IAAAC,+BAAA,eAA0F;UAC5FpC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA0B;UAEOD,EAAA,CAAAqC,SAAA,aAA2B;UAACrC,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA2B;UAACrC,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA6B;UAACrC,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA4B;UAACrC,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAC,cAAA,kBAAiD;UAAnBD,EAAA,CAAAsC,UAAA,mBAAAC,qDAAA;YAAA,OAASL,GAAA,CAAAb,MAAA,EAAQ;UAAA,EAAC;UAC9CrB,EAAA,CAAAqC,SAAA,aAAmC;UAACrC,EAAA,CAAAE,MAAA,gBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIfH,EAAA,CAAAmC,UAAA,KAAAK,kCAAA,kBAMM;UACRxC,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,gBAA6B;UAOnBD,EAAA,CAAAqC,SAAA,aAAqC;UACvCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI/CH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAqC,SAAA,aAAiC;UACnCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAM7CH,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK7CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,aAA8B;UAChCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI1CH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAqC,SAAA,aAA2B;UAC7BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAqC,SAAA,aAAsC;UACxCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqC,SAAA,aAA+B;UACjCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKlDH,EAAA,CAAAC,cAAA,eAA8B;UAIxBD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAsC;UAElCD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK1CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,aAAgC;UAClCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAItCH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAqC,SAAA,aAAkC;UACpCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAAuC;UAEnCD,EAAA,CAAAqC,SAAA,aAAsC;UACxCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK3CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,aAAmC;UACrCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI5CH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqC,SAAA,cAAkC;UACpCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;UAvJjBH,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAyC,UAAA,SAAAP,GAAA,CAAA3B,WAAA,CAAiB;UAc5BP,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAyC,UAAA,SAAAP,GAAA,CAAA3B,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}