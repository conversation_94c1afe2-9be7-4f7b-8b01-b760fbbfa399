{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../shared/autocomplete/autocomplete.component\";\nfunction FlightComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"from\"), \" \");\n  }\n}\nfunction FlightComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"to\"), \" \");\n  }\n}\nfunction FlightComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"departureDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_33_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getErrorMessage(\"returnDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"app-autocomplete\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14);\n    i0.ɵɵelement(5, \"app-autocomplete\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19);\n    i0.ɵɵelement(8, \"input\", 73)(9, \"i\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, FlightComponent_div_33_div_10_Template, 2, 1, \"div\", 16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", (tmp_0_0 = ctx_r3.flightForm.get(\"to\")) == null ? null : tmp_0_0.value)(\"readonly\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", (tmp_2_0 = ctx_r3.flightForm.get(\"from\")) == null ? null : tmp_2_0.value)(\"readonly\", true);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getErrorMessage(\"returnDate\"));\n  }\n}\nfunction FlightComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14)(3, \"app-autocomplete\", 74);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_34_Template_app_autocomplete_ngModelChange_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.from = $event);\n    })(\"locationSelected\", function FlightComponent_div_34_Template_app_autocomplete_locationSelected_3_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r15 = restoredCtx.index;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onSegmentLocationSelected($event, i_r15, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"app-autocomplete\", 75);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_34_Template_app_autocomplete_ngModelChange_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.to = $event);\n    })(\"locationSelected\", function FlightComponent_div_34_Template_app_autocomplete_locationSelected_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r15 = restoredCtx.index;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onSegmentLocationSelected($event, i_r15, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 18)(7, \"div\", 19)(8, \"input\", 76);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_34_Template_input_ngModelChange_8_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.date = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(9, \"i\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_34_Template_button_click_10_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r15 = restoredCtx.index;\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.removeSegment(i_r15));\n    });\n    i0.ɵɵelement(11, \"i\", 78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const segment_r14 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.from);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.to);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.date);\n  }\n}\nfunction FlightComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 79)(1, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_35_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.addSegment());\n    });\n    i0.ɵɵelement(2, \"i\", 81);\n    i0.ɵɵtext(3, \" Add Sector \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightComponent_option_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r25.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r25.label, \" \");\n  }\n}\nfunction FlightComponent_span_114_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 82);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r27 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r27.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r27.label, \" \");\n  }\n}\nfunction FlightComponent_span_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83)(1, \"select\", 84);\n    i0.ɵɵtemplate(2, FlightComponent_span_114_option_2_Template, 2, 2, \"option\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.calendarDays);\n  }\n}\nfunction FlightComponent_span_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"SEARCH NOW\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_span_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 85);\n    i0.ɵɵtext(2, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_div_126_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_126_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const search_r28 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.loadLatestSearch(search_r28));\n    });\n    i0.ɵɵelementStart(1, \"div\", 87);\n    i0.ɵɵelement(2, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 88)(4, \"div\", 89);\n    i0.ɵɵtext(5, \" Coming from \");\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" - \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const search_r28 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(search_r28.from);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(search_r28.to);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" on \", i0.ɵɵpipeBind2(12, 3, search_r28.date, \"MMM d, yyyy\"), \" \");\n  }\n}\nfunction FlightComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵelement(1, \"i\", 91);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No recent searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Your recent flight searches will appear here\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_130_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.clearLatestSearches());\n    });\n    i0.ɵɵelement(2, \"i\", 94);\n    i0.ɵɵtext(3, \" Clear All \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FlightComponent {\n  constructor(fb, flightService, authService, router) {\n    this.fb = fb;\n    this.flightService = flightService;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.currentUser = null;\n    // Form state\n    this.tripType = 'oneWay';\n    this.showReturnDate = false;\n    this.showCalendar = false;\n    this.additionalSegments = [];\n    // Passenger counts\n    this.adultCount = 1;\n    this.childCount = 0;\n    this.infantCount = 0;\n    // Class selection\n    this.selectedClass = 'economy';\n    // Baggage options\n    this.baggageOptions = [{\n      value: 'all',\n      label: '--All--'\n    }, {\n      value: '20kg',\n      label: '20kg'\n    }, {\n      value: '30kg',\n      label: '30kg'\n    }, {\n      value: 'extra',\n      label: 'Extra'\n    }];\n    // Calendar options\n    this.calendarDays = [{\n      value: '1',\n      label: '+/- 1 Days'\n    }, {\n      value: '3',\n      label: '+/- 3 Days'\n    }, {\n      value: '7',\n      label: '+/- 7 Days'\n    }];\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n  ngOnInit() {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n    // Set default dates\n    this.setDefaultDates();\n  }\n  /**\n   * Create reactive form\n   */\n  createForm() {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  setDefaultDates() {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n  /**\n   * Format date for input field\n   */\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type) {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n    this.flightForm.patchValue({\n      tripType: type\n    });\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n    // Clear additional segments when switching away from multi-city\n    if (type !== 'multiCity') {\n      this.additionalSegments = [];\n    }\n  }\n  /**\n   * Add a new flight segment for multi-city trips\n   */\n  addSegment() {\n    const newSegment = {\n      from: '',\n      to: '',\n      date: ''\n    };\n    this.additionalSegments.push(newSegment);\n  }\n  /**\n   * Remove a flight segment\n   */\n  removeSegment(index) {\n    this.additionalSegments.splice(index, 1);\n  }\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type, increment) {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n    this.flightForm.patchValue({\n      [type]: newValue\n    });\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers() {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass) {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({\n      class: flightClass\n    });\n  }\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar() {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({\n      calendar: this.showCalendar\n    });\n  }\n  /**\n   * Swap from and to locations\n   */\n  swapLocations() {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n      const formValue = this.flightForm.value;\n      const searchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n      // Perform search based on trip type\n      let searchObservable;\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n      searchObservable.subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n\n        error: error => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search) {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches() {\n    this.flightService.clearLatestSearches();\n  }\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName) {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function FlightComponent_Factory(t) {\n      return new (t || FlightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightComponent,\n      selectors: [[\"app-flight\"]],\n      decls: 132,\n      vars: 32,\n      consts: [[1, \"flight-container\"], [1, \"flight-content\"], [1, \"flight-search-panel\"], [1, \"search-header\"], [1, \"search-title\"], [1, \"fas\", \"fa-plane\"], [1, \"search-subtitle\"], [1, \"flight-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-selector\"], [\"type\", \"button\", 1, \"trip-type-btn\", 3, \"click\"], [1, \"location-date-section\"], [1, \"flight-segments\"], [1, \"flight-segment\"], [1, \"segment-row\"], [1, \"form-group\", \"location-group\"], [\"formControlName\", \"from\", \"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-departure\", 3, \"locationSelected\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"formControlName\", \"to\", \"placeholder\", \"Going to (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-arrival\", 3, \"locationSelected\"], [1, \"form-group\", \"date-group\"], [1, \"date-input-wrapper\"], [\"type\", \"date\", \"formControlName\", \"departureDate\", \"placeholder\", \"Choose A Date\", 1, \"date-input\"], [1, \"fas\", \"fa-calendar-alt\", \"date-icon\"], [\"class\", \"flight-segment\", 4, \"ngIf\"], [\"class\", \"flight-segment\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"add-sector-section\", 4, \"ngIf\"], [1, \"passenger-class-section\"], [1, \"form-group\", \"passenger-group\"], [1, \"passenger-controls\"], [1, \"passenger-type\"], [1, \"passenger-icon\"], [1, \"fas\", \"fa-user\"], [1, \"passenger-count\"], [1, \"counter-controls\"], [\"type\", \"button\", 1, \"counter-btn\", 3, \"click\"], [1, \"fas\", \"fa-child\"], [1, \"fas\", \"fa-baby\"], [1, \"class-selection\"], [\"type\", \"button\", 1, \"class-btn\", 3, \"click\"], [1, \"fas\", \"fa-chair\"], [1, \"form-group\", \"airline-group\"], [\"for\", \"preferredAirline\"], [\"id\", \"preferredAirline\", \"formControlName\", \"preferredAirline\", 1, \"airline-select\"], [\"value\", \"\"], [\"value\", \"TK\"], [\"value\", \"AF\"], [\"value\", \"LH\"], [\"value\", \"EK\"], [\"value\", \"QR\"], [1, \"additional-options\"], [1, \"option-group\"], [1, \"option-label\"], [1, \"option-controls\"], [\"formControlName\", \"refundableFares\", 1, \"option-select\"], [\"value\", \"false\"], [\"value\", \"true\"], [\"formControlName\", \"baggage\", 1, \"option-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-toggle\"], [\"type\", \"checkbox\", \"id\", \"calendar\", \"formControlName\", \"calendar\", 3, \"change\"], [\"for\", \"calendar\", 1, \"calendar-label\"], [\"class\", \"calendar-days\", 4, \"ngIf\"], [1, \"search-button-section\"], [\"type\", \"submit\", 1, \"search-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"latest-searches-panel\"], [1, \"searches-header\"], [1, \"searches-list\"], [\"class\", \"search-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-searches\", 4, \"ngIf\"], [\"class\", \"searches-actions\", 4, \"ngIf\"], [1, \"error-message\"], [\"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-departure\", 3, \"ngModel\", \"readonly\"], [\"placeholder\", \"Going to (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-arrival\", 3, \"ngModel\", \"readonly\"], [\"type\", \"date\", \"formControlName\", \"returnDate\", \"placeholder\", \"Choose A Date\", 1, \"date-input\"], [\"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-departure\", 3, \"ngModel\", \"ngModelChange\", \"locationSelected\"], [\"placeholder\", \"Going to (City, Country Or Specific Airport)\", \"icon\", \"fas fa-plane-arrival\", 3, \"ngModel\", \"ngModelChange\", \"locationSelected\"], [\"type\", \"date\", \"placeholder\", \"Choose A Date\", 1, \"date-input\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", 1, \"remove-segment-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"add-sector-section\"], [\"type\", \"button\", 1, \"add-sector-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [3, \"value\"], [1, \"calendar-days\"], [\"formControlName\", \"calendarDays\", 1, \"calendar-select\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"search-item\", 3, \"click\"], [1, \"search-icon\"], [1, \"search-details\"], [1, \"search-route\"], [1, \"empty-searches\"], [1, \"fas\", \"fa-search\"], [1, \"searches-actions\"], [\"type\", \"button\", 1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"]],\n      template: function FlightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\");\n          i0.ɵɵtext(7, \"Search and Book Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function FlightComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_12_listener() {\n            return ctx.onTripTypeChange(\"oneWay\");\n          });\n          i0.ɵɵtext(13, \" One way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_14_listener() {\n            return ctx.onTripTypeChange(\"roundTrip\");\n          });\n          i0.ɵɵtext(15, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_16_listener() {\n            return ctx.onTripTypeChange(\"multiCity\");\n          });\n          i0.ɵɵtext(17, \" Multi-City/Stop-Overs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"div\", 13)(22, \"div\", 14)(23, \"app-autocomplete\", 15);\n          i0.ɵɵlistener(\"locationSelected\", function FlightComponent_Template_app_autocomplete_locationSelected_23_listener($event) {\n            return ctx.onFromLocationSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(24, FlightComponent_div_24_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 14)(26, \"app-autocomplete\", 17);\n          i0.ɵɵlistener(\"locationSelected\", function FlightComponent_Template_app_autocomplete_locationSelected_26_listener($event) {\n            return ctx.onToLocationSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, FlightComponent_div_27_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 18)(29, \"div\", 19);\n          i0.ɵɵelement(30, \"input\", 20)(31, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, FlightComponent_div_32_Template, 2, 1, \"div\", 16);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(33, FlightComponent_div_33_Template, 11, 5, \"div\", 22);\n          i0.ɵɵtemplate(34, FlightComponent_div_34_Template, 12, 3, \"div\", 23);\n          i0.ɵɵtemplate(35, FlightComponent_div_35_Template, 4, 0, \"div\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"div\", 26)(38, \"label\");\n          i0.ɵɵtext(39, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 27)(41, \"div\", 28)(42, \"span\", 29);\n          i0.ɵɵelement(43, \"i\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 31);\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 32)(47, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_47_listener() {\n            return ctx.updatePassengerCount(\"adults\", false);\n          });\n          i0.ɵɵtext(48, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_49_listener() {\n            return ctx.updatePassengerCount(\"adults\", true);\n          });\n          i0.ɵɵtext(50, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 28)(52, \"span\", 29);\n          i0.ɵɵelement(53, \"i\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 31);\n          i0.ɵɵtext(55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"div\", 32)(57, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_57_listener() {\n            return ctx.updatePassengerCount(\"children\", false);\n          });\n          i0.ɵɵtext(58, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_59_listener() {\n            return ctx.updatePassengerCount(\"children\", true);\n          });\n          i0.ɵɵtext(60, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"span\", 29);\n          i0.ɵɵelement(63, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 31);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 32)(67, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_67_listener() {\n            return ctx.updatePassengerCount(\"infants\", false);\n          });\n          i0.ɵɵtext(68, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"button\", 33);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_69_listener() {\n            return ctx.updatePassengerCount(\"infants\", true);\n          });\n          i0.ɵɵtext(70, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"div\", 36)(72, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_72_listener() {\n            return ctx.onClassChange(\"economy\");\n          });\n          i0.ɵɵelement(73, \"i\", 38);\n          i0.ɵɵtext(74, \" Economy \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"label\", 40);\n          i0.ɵɵtext(77, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"select\", 41)(79, \"option\", 42);\n          i0.ɵɵtext(80, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"option\", 43);\n          i0.ɵɵtext(82, \"Turkish Airlines\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"option\", 44);\n          i0.ɵɵtext(84, \"Air France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"option\", 45);\n          i0.ɵɵtext(86, \"Lufthansa\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"option\", 46);\n          i0.ɵɵtext(88, \"Emirates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"option\", 47);\n          i0.ɵɵtext(90, \"Qatar Airways\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(91, \"div\", 48)(92, \"div\", 49)(93, \"label\", 50);\n          i0.ɵɵtext(94, \"Refundable fares\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 51)(96, \"select\", 52)(97, \"option\", 53);\n          i0.ɵɵtext(98, \"--All--\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"option\", 54);\n          i0.ɵɵtext(100, \"Refundable Only\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(101, \"div\", 49)(102, \"label\", 50);\n          i0.ɵɵtext(103, \"Baggage\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"div\", 51)(105, \"select\", 55);\n          i0.ɵɵtemplate(106, FlightComponent_option_106_Template, 2, 2, \"option\", 56);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(107, \"div\", 49)(108, \"label\", 50);\n          i0.ɵɵtext(109, \"Calendar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"div\", 51)(111, \"div\", 57)(112, \"input\", 58);\n          i0.ɵɵlistener(\"change\", function FlightComponent_Template_input_change_112_listener() {\n            return ctx.toggleCalendar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"label\", 59);\n          i0.ɵɵtemplate(114, FlightComponent_span_114_Template, 3, 1, \"span\", 60);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(115, \"div\", 61)(116, \"button\", 62);\n          i0.ɵɵtemplate(117, FlightComponent_span_117_Template, 2, 0, \"span\", 63);\n          i0.ɵɵtemplate(118, FlightComponent_span_118_Template, 3, 0, \"span\", 63);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(119, \"div\", 64)(120, \"div\", 65)(121, \"h3\");\n          i0.ɵɵtext(122, \"Latest Searches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"p\");\n          i0.ɵɵtext(124, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(125, \"div\", 66);\n          i0.ɵɵtemplate(126, FlightComponent_div_126_Template, 13, 6, \"div\", 67);\n          i0.ɵɵpipe(127, \"async\");\n          i0.ɵɵtemplate(128, FlightComponent_div_128_Template, 6, 0, \"div\", 68);\n          i0.ɵɵpipe(129, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(130, FlightComponent_div_130_Template, 4, 0, \"div\", 69);\n          i0.ɵɵpipe(131, \"async\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_20_0;\n          let tmp_21_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.flightForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"oneWay\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"roundTrip\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"multiCity\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"from\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"to\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"departureDate\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showReturnDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.additionalSegments);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tripType === \"multiCity\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_10_0 = ctx.flightForm.get(\"adults\")) == null ? null : tmp_10_0.value) || 1);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_11_0 = ctx.flightForm.get(\"children\")) == null ? null : tmp_11_0.value) || 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_12_0 = ctx.flightForm.get(\"infants\")) == null ? null : tmp_12_0.value) || 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.selectedClass === \"economy\");\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baggageOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCalendar);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.flightForm.valid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(127, 26, ctx.latestSearches$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = i0.ɵɵpipeBind1(129, 28, ctx.latestSearches$)) == null ? null : tmp_20_0.length) === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = i0.ɵɵpipeBind1(131, 30, ctx.latestSearches$)) == null ? null : tmp_21_0.length) > 0);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i1.NgModel, i6.AutocompleteComponent, i5.AsyncPipe, i5.DatePipe],\n      styles: [\"\\n\\n.flight-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n.flight-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 30px;\\n  align-items: start;\\n}\\n\\n\\n\\n.flight-search-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 30px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.search-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 12px;\\n  border-radius: 50%;\\n  font-size: 20px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.search-subtitle[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n  margin-left: 55px;\\n}\\n\\n\\n\\n.trip-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  padding: 4px;\\n  margin-bottom: 25px;\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: none;\\n  background: transparent;\\n  color: #4a5568;\\n  font-size: 14px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.trip-type-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  box-shadow: 0 2px 8px rgba(59, 67, 113, 0.3);\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e2e8f0;\\n}\\n\\n\\n\\n.location-date-section[_ngcontent-%COMP%], .passenger-class-section[_ngcontent-%COMP%], .additional-options[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n\\n\\n.location-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.location-input-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  color: #a0aec0;\\n  z-index: 2;\\n}\\n\\n.location-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 15px 15px 45px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.location-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n.swap-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  padding: 8px;\\n  cursor: pointer;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.swap-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n\\n\\n.flight-segments[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 15px;\\n}\\n\\n.flight-segment[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 15px;\\n  border: 1px solid #e9ecef;\\n}\\n\\n.segment-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr 1fr auto;\\n  gap: 15px;\\n  align-items: end;\\n}\\n\\n.date-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.date-input-wrapper[_ngcontent-%COMP%]   .date-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  color: #a0aec0;\\n  z-index: 2;\\n}\\n\\n\\n\\n.date-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 45px 15px 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n  background: white;\\n}\\n\\n.date-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n.location-input[readonly][_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.remove-segment-btn[_ngcontent-%COMP%] {\\n  background: #dc3545;\\n  border: none;\\n  border-radius: 50%;\\n  width: 35px;\\n  height: 35px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  color: white;\\n}\\n\\n.remove-segment-btn[_ngcontent-%COMP%]:hover {\\n  background: #c82333;\\n  transform: scale(1.05);\\n}\\n\\n.add-sector-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 15px;\\n}\\n\\n.add-sector-btn[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  color: white;\\n  border: none;\\n  border-radius: 6px;\\n  padding: 10px 20px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.add-sector-btn[_ngcontent-%COMP%]:hover {\\n  background: #218838;\\n  transform: translateY(-1px);\\n}\\n\\n.add-sector-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n\\n\\n\\n.passenger-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: #f7fafc;\\n}\\n\\n.passenger-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n\\n.passenger-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.counter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 1px solid #cbd5e0;\\n  background: white;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-selection[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.class-btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: 2px solid #e2e8f0;\\n  background: white;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.class-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  border-color: #cbd5e0;\\n  background: #f7fafc;\\n}\\n\\n\\n\\n.airline-select[_ngcontent-%COMP%], .option-select[_ngcontent-%COMP%], .calendar-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  background: white;\\n  cursor: pointer;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.airline-select[_ngcontent-%COMP%]:focus, .option-select[_ngcontent-%COMP%]:focus, .calendar-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n\\n\\n.additional-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n}\\n\\n.option-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.option-label[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.calendar-select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.search-button-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n}\\n\\n.search-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  border: none;\\n  padding: 18px 60px;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n\\n\\n.latest-searches-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 25px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n\\n.searches-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n\\n\\n.searches-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.search-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 10px;\\n  border: 1px solid #f7fafc;\\n}\\n\\n.search-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  border-color: #e2e8f0;\\n  transform: translateX(5px);\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 10px;\\n  border-radius: 50%;\\n  font-size: 14px;\\n  flex-shrink: 0;\\n}\\n\\n.search-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.search-route[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.search-route[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.empty-searches[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a0aec0;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n  opacity: 0.5;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 5px 0;\\n  color: #718096;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a0aec0;\\n}\\n\\n\\n\\n.searches-actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  background: #fed7d7;\\n  color: #c53030;\\n  border: 1px solid #feb2b2;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  background: #fc8181;\\n  color: white;\\n  border-color: #fc8181;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .flight-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .flight-search-panel[_ngcontent-%COMP%], .latest-searches-panel[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  .additional-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .passenger-controls[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 15px;\\n  }\\n\\n  .search-title[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .search-subtitle[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n\\n  .segment-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 10px;\\n  }\\n\\n  .remove-segment-btn[_ngcontent-%COMP%] {\\n    justify-self: center;\\n    margin-top: 10px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .flight-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .trip-type-selector[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .trip-type-btn[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n\\n  .search-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 15px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ctx_r1", "ctx_r2", "ctx_r13", "ɵɵelement", "ɵɵtemplate", "FlightComponent_div_33_div_10_Template", "ɵɵproperty", "tmp_0_0", "ctx_r3", "flightForm", "get", "value", "tmp_2_0", "ɵɵlistener", "FlightComponent_div_34_Template_app_autocomplete_ngModelChange_3_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r17", "segment_r14", "$implicit", "ɵɵresetView", "from", "FlightComponent_div_34_Template_app_autocomplete_locationSelected_3_listener", "i_r15", "index", "ctx_r18", "ɵɵnextContext", "onSegmentLocationSelected", "FlightComponent_div_34_Template_app_autocomplete_ngModelChange_5_listener", "to", "FlightComponent_div_34_Template_app_autocomplete_locationSelected_5_listener", "ctx_r20", "FlightComponent_div_34_Template_input_ngModelChange_8_listener", "date", "FlightComponent_div_34_Template_button_click_10_listener", "ctx_r22", "removeSegment", "FlightComponent_div_35_Template_button_click_1_listener", "_r24", "ctx_r23", "addSegment", "option_r25", "label", "option_r27", "FlightComponent_span_114_option_2_Template", "ctx_r7", "calendarDays", "FlightComponent_div_126_Template_div_click_0_listener", "_r30", "search_r28", "ctx_r29", "loadLatestSearch", "ɵɵtextInterpolate", "ɵɵpipeBind2", "FlightComponent_div_130_Template_button_click_1_listener", "_r32", "ctx_r31", "clearLatestSearches", "FlightComponent", "constructor", "fb", "flightService", "authService", "router", "isLoading", "currentUser", "tripType", "showReturnDate", "showCalendar", "additionalSegments", "adultCount", "childCount", "infantCount", "selectedClass", "baggageOptions", "createForm", "latestSearches$", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "setDefaultDates", "group", "required", "departureDate", "returnDate", "adults", "min", "children", "infants", "class", "preferredAirline", "directFlights", "refundableFares", "baggage", "calendar", "today", "Date", "tomorrow", "setDate", "getDate", "nextWeek", "patchValue", "formatDate", "toISOString", "split", "onTripTypeChange", "type", "setValidators", "clearValidators", "updateValueAndValidity", "newSegment", "push", "splice", "updatePassengerCount", "increment", "currentValue", "newValue", "Math", "max", "getTotalPassengers", "onClassChange", "flightClass", "toggleCalendar", "swapLocations", "onSubmit", "valid", "formValue", "searchForm", "passengers", "saveLatestSearch", "searchObservable", "searchOneWayFlights", "searchRoundTripFlights", "searchMultiCityFlights", "next", "response", "header", "success", "console", "log", "error", "messages", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "search", "controlName", "control", "errors", "touched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightService", "i3", "AuthService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "FlightComponent_Template", "rf", "ctx", "FlightComponent_Template_form_ngSubmit_10_listener", "FlightComponent_Template_button_click_12_listener", "FlightComponent_Template_button_click_14_listener", "FlightComponent_Template_button_click_16_listener", "FlightComponent_Template_app_autocomplete_locationSelected_23_listener", "onFromLocationSelected", "FlightComponent_div_24_Template", "FlightComponent_Template_app_autocomplete_locationSelected_26_listener", "onToLocationSelected", "FlightComponent_div_27_Template", "FlightComponent_div_32_Template", "FlightComponent_div_33_Template", "FlightComponent_div_34_Template", "FlightComponent_div_35_Template", "FlightComponent_Template_button_click_47_listener", "FlightComponent_Template_button_click_49_listener", "FlightComponent_Template_button_click_57_listener", "FlightComponent_Template_button_click_59_listener", "FlightComponent_Template_button_click_67_listener", "FlightComponent_Template_button_click_69_listener", "FlightComponent_Template_button_click_72_listener", "FlightComponent_option_106_Template", "FlightComponent_Template_input_change_112_listener", "FlightComponent_span_114_Template", "FlightComponent_span_117_Template", "FlightComponent_span_118_Template", "FlightComponent_div_126_Template", "FlightComponent_div_128_Template", "FlightComponent_div_130_Template", "ɵɵclassProp", "tmp_10_0", "tmp_11_0", "tmp_12_0", "ɵɵpipeBind1", "tmp_20_0", "length", "tmp_21_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { FlightService } from '../../services/flight.service';\nimport { AuthService } from '../../services/auth.service';\nimport { FlightSearchForm, LatestSearch, Location } from '../../models/flight.models';\nimport { Observable } from 'rxjs';\n\ninterface FlightSegment {\n  from: string;\n  to: string;\n  date: string;\n}\n\n@Component({\n  selector: 'app-flight',\n  templateUrl: './flight.component.html',\n  styleUrls: ['./flight.component.css']\n})\nexport class FlightComponent implements OnInit {\n  flightForm: FormGroup;\n  isLoading = false;\n  currentUser: any = null;\n  latestSearches$: Observable<LatestSearch[]>;\n\n  // Form state\n  tripType = 'oneWay';\n  showReturnDate = false;\n  showCalendar = false;\n  additionalSegments: FlightSegment[] = [];\n\n  // Passenger counts\n  adultCount = 1;\n  childCount = 0;\n  infantCount = 0;\n\n  // Class selection\n  selectedClass = 'economy';\n\n  // Baggage options\n  baggageOptions = [\n    { value: 'all', label: '--All--' },\n    { value: '20kg', label: '20kg' },\n    { value: '30kg', label: '30kg' },\n    { value: 'extra', label: 'Extra' }\n  ];\n\n  // Calendar options\n  calendarDays = [\n    { value: '1', label: '+/- 1 Days' },\n    { value: '3', label: '+/- 3 Days' },\n    { value: '7', label: '+/- 7 Days' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private flightService: FlightService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n\n  ngOnInit(): void {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n\n    // Set default dates\n    this.setDefaultDates();\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  private setDefaultDates(): void {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n\n  /**\n   * Format date for input field\n   */\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type: string): void {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n\n    this.flightForm.patchValue({ tripType: type });\n\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n\n    // Clear additional segments when switching away from multi-city\n    if (type !== 'multiCity') {\n      this.additionalSegments = [];\n    }\n  }\n\n  /**\n   * Add a new flight segment for multi-city trips\n   */\n  addSegment(): void {\n    const newSegment: FlightSegment = {\n      from: '',\n      to: '',\n      date: ''\n    };\n    this.additionalSegments.push(newSegment);\n  }\n\n  /**\n   * Remove a flight segment\n   */\n  removeSegment(index: number): void {\n    this.additionalSegments.splice(index, 1);\n  }\n\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n\n    this.flightForm.patchValue({ [type]: newValue });\n\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers(): number {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass: string): void {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({ class: flightClass });\n  }\n\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar(): void {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({ calendar: this.showCalendar });\n  }\n\n  /**\n   * Swap from and to locations\n   */\n  swapLocations(): void {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.flightForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n\n      // Perform search based on trip type\n      let searchObservable;\n\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n\n      searchObservable.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search: LatestSearch): void {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches(): void {\n    this.flightService.clearLatestSearches();\n  }\n\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"flight-container\">\n  <!-- Main Content -->\n  <div class=\"flight-content\">\n    <!-- Left Panel - Flight Search -->\n    <div class=\"flight-search-panel\">\n      <div class=\"search-header\">\n        <div class=\"search-title\">\n          <i class=\"fas fa-plane\"></i>\n          <h2>Search and Book Flights</h2>\n        </div>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n\n      <form [formGroup]=\"flightForm\" (ngSubmit)=\"onSubmit()\" class=\"flight-form\">\n        <!-- Trip Type Selection -->\n        <div class=\"trip-type-selector\">\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'oneWay'\"\n            (click)=\"onTripTypeChange('oneWay')\">\n            One way\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'roundTrip'\"\n            (click)=\"onTripTypeChange('roundTrip')\">\n            Round Trip\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'multiCity'\"\n            (click)=\"onTripTypeChange('multiCity')\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location and Date Selection -->\n        <div class=\"location-date-section\">\n          <!-- Flight Segments -->\n          <div class=\"flight-segments\">\n            <!-- First Segment (always visible) -->\n            <div class=\"flight-segment\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    formControlName=\"from\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    (locationSelected)=\"onFromLocationSelected($event)\">\n                  </app-autocomplete>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('from')\">\n                    {{ getErrorMessage('from') }}\n                  </div>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    formControlName=\"to\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    (locationSelected)=\"onToLocationSelected($event)\">\n                  </app-autocomplete>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('to')\">\n                    {{ getErrorMessage('to') }}\n                  </div>\n                </div>\n\n                <!-- Departure Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"departureDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('departureDate')\">\n                    {{ getErrorMessage('departureDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Second Segment (for Round Trip) -->\n            <div class=\"flight-segment\" *ngIf=\"showReturnDate\">\n              <div class=\"segment-row\">\n                <!-- Return From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [ngModel]=\"flightForm.get('to')?.value\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    [readonly]=\"true\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Return To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [ngModel]=\"flightForm.get('from')?.value\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    [readonly]=\"true\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Return Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"returnDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('returnDate')\">\n                    {{ getErrorMessage('returnDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Additional Segments for Multi-City -->\n            <div class=\"flight-segment\" *ngFor=\"let segment of additionalSegments; let i = index\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [(ngModel)]=\"segment.from\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    (locationSelected)=\"onSegmentLocationSelected($event, i, 'from')\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [(ngModel)]=\"segment.to\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    (locationSelected)=\"onSegmentLocationSelected($event, i, 'to')\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      [(ngModel)]=\"segment.date\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                </div>\n\n                <!-- Remove Segment Button -->\n                <button type=\"button\" class=\"remove-segment-btn\" (click)=\"removeSegment(i)\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Add Sector Button -->\n            <div class=\"add-sector-section\" *ngIf=\"tripType === 'multiCity'\">\n              <button type=\"button\" class=\"add-sector-btn\" (click)=\"addSegment()\">\n                <i class=\"fas fa-plus\"></i>\n                Add Sector\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger and Class Selection -->\n        <div class=\"passenger-class-section\">\n          <!-- Passenger Count -->\n          <div class=\"form-group passenger-group\">\n            <label>Passenger & Class of travel</label>\n            <div class=\"passenger-controls\">\n              <!-- Adults -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-user\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('adults')?.value || 1 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Children -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-child\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('children')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Infants -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-baby\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('infants')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Class Selection -->\n              <div class=\"class-selection\">\n                <button\n                  type=\"button\"\n                  class=\"class-btn\"\n                  [class.active]=\"selectedClass === 'economy'\"\n                  (click)=\"onClassChange('economy')\">\n                  <i class=\"fas fa-chair\"></i> Economy\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Preferred Airline -->\n          <div class=\"form-group airline-group\">\n            <label for=\"preferredAirline\">Preferred Airline</label>\n            <select id=\"preferredAirline\" formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option value=\"TK\">Turkish Airlines</option>\n              <option value=\"AF\">Air France</option>\n              <option value=\"LH\">Lufthansa</option>\n              <option value=\"EK\">Emirates</option>\n              <option value=\"QR\">Qatar Airways</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Additional Options -->\n        <div class=\"additional-options\">\n          <!-- Refundable Fares -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"refundableFares\" class=\"option-select\">\n                <option value=\"false\">--All--</option>\n                <option value=\"true\">Refundable Only</option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Baggage -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"baggage\" class=\"option-select\">\n                <option *ngFor=\"let option of baggageOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Calendar -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"option-controls\">\n              <div class=\"calendar-toggle\">\n                <input\n                  type=\"checkbox\"\n                  id=\"calendar\"\n                  formControlName=\"calendar\"\n                  (change)=\"toggleCalendar()\">\n                <label for=\"calendar\" class=\"calendar-label\">\n                  <span class=\"calendar-days\" *ngIf=\"showCalendar\">\n                    <select formControlName=\"calendarDays\" class=\"calendar-select\">\n                      <option *ngFor=\"let option of calendarDays\" [value]=\"option.value\">\n                        {{ option.label }}\n                      </option>\n                    </select>\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <div class=\"search-button-section\">\n          <button\n            type=\"submit\"\n            class=\"search-btn\"\n            [disabled]=\"isLoading || !flightForm.valid\">\n            <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n            <span *ngIf=\"isLoading\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Searching...\n            </span>\n          </button>\n        </div>\n      </form>\n    </div>\n\n    <!-- Right Panel - Latest Searches -->\n    <div class=\"latest-searches-panel\">\n      <div class=\"searches-header\">\n        <h3>Latest Searches</h3>\n        <p>We're bringing you a new level of comfort</p>\n      </div>\n\n      <div class=\"searches-list\">\n        <div\n          *ngFor=\"let search of latestSearches$ | async\"\n          class=\"search-item\"\n          (click)=\"loadLatestSearch(search)\">\n          <div class=\"search-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <div class=\"search-details\">\n            <div class=\"search-route\">\n              Coming from <strong>{{ search.from }}</strong> - <strong>{{ search.to }}</strong> on {{ search.date | date:'MMM d, yyyy' }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div *ngIf=\"(latestSearches$ | async)?.length === 0\" class=\"empty-searches\">\n          <i class=\"fas fa-search\"></i>\n          <p>No recent searches</p>\n          <small>Your recent flight searches will appear here</small>\n        </div>\n      </div>\n\n      <!-- Clear searches button -->\n      <div class=\"searches-actions\" *ngIf=\"(latestSearches$ | async)?.length! > 0\">\n        <button type=\"button\" class=\"clear-btn\" (click)=\"clearLatestSearches()\">\n          <i class=\"fas fa-trash\"></i> Clear All\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;ICqDjDC,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,cACF;;;;;IAWAP,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAG,MAAA,CAAAD,eAAA,YACF;;;;;IAaAP,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,eAAA,uBACF;;;;;IAsCAP,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAK,OAAA,CAAAH,eAAA,oBACF;;;;;IAlCNP,EAAA,CAAAC,cAAA,cAAmD;IAI7CD,EAAA,CAAAW,SAAA,2BAKmB;IACrBX,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAW,SAAA,2BAKmB;IACrBX,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAAmC;IAE/BD,EAAA,CAAAW,SAAA,gBAIqB;IAEvBX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,UAAA,KAAAC,sCAAA,kBAEM;IACRb,EAAA,CAAAG,YAAA,EAAM;;;;;;IA9BFH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAc,UAAA,aAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,yBAAAH,OAAA,CAAAI,KAAA,CAAuC;IAUvCnB,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAc,UAAA,aAAAM,OAAA,GAAAJ,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,KAAA,CAAyC;IAiBfnB,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAc,UAAA,SAAAE,MAAA,CAAAT,eAAA,eAAmC;;;;;;IAQrEP,EAAA,CAAAC,cAAA,cAAsF;IAK9ED,EAAA,CAAAqB,UAAA,2BAAAC,0EAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAG,IAAA,GAAAP,MAAA,CAC3B;IAAA,EADwC,8BAAAQ,6EAAAR,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAM,KAAA,GAAAR,WAAA,CAAAS,KAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAmC,aAAA;MAAA,OAGNnC,EAAA,CAAA6B,WAAA,CAAAK,OAAA,CAAAE,yBAAA,CAAAb,MAAA,EAAAS,KAAA,EAAqC,MAAM,CAAC;IAAA,EAHtC;IAI5BhC,EAAA,CAAAG,YAAA,EAAmB;IAIrBH,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAqB,UAAA,2BAAAgB,0EAAAd,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAW,EAAA,GAAAf,MAAA,CAC3B;IAAA,EADsC,8BAAAgB,6EAAAhB,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAM,KAAA,GAAAR,WAAA,CAAAS,KAAA;MAAA,MAAAO,OAAA,GAAAxC,EAAA,CAAAmC,aAAA;MAAA,OAGJnC,EAAA,CAAA6B,WAAA,CAAAW,OAAA,CAAAJ,yBAAA,CAAAb,MAAA,EAAAS,KAAA,EAAqC,IAAI,CAAC;IAAA,EAHtC;IAI1BhC,EAAA,CAAAG,YAAA,EAAmB;IAIrBH,EAAA,CAAAC,cAAA,cAAmC;IAI7BD,EAAA,CAAAqB,UAAA,2BAAAoB,+DAAAlB,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAe,IAAA,GAAAnB,MAAA,CAC7B;IAAA,EAD0C;IAF5BvB,EAAA,CAAAG,YAAA,EAIqB;IACrBH,EAAA,CAAAW,SAAA,YAA6C;IAC/CX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,kBAA4E;IAA3BD,EAAA,CAAAqB,UAAA,mBAAAsB,yDAAA;MAAA,MAAAnB,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAM,KAAA,GAAAR,WAAA,CAAAS,KAAA;MAAA,MAAAW,OAAA,GAAA5C,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAA6B,WAAA,CAAAe,OAAA,CAAAC,aAAA,CAAAb,KAAA,CAAgB;IAAA,EAAC;IACzEhC,EAAA,CAAAW,SAAA,aAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAS;;;;IAhCLH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAG,IAAA,CAA0B;IAU1B9B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAW,EAAA,CAAwB;IAYtBtC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAe,IAAA,CAA0B;;;;;;IAepC1C,EAAA,CAAAC,cAAA,cAAiE;IAClBD,EAAA,CAAAqB,UAAA,mBAAAyB,wDAAA;MAAA9C,EAAA,CAAAyB,aAAA,CAAAsB,IAAA;MAAA,MAAAC,OAAA,GAAAhD,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAA6B,WAAA,CAAAmB,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACjEjD,EAAA,CAAAW,SAAA,YAA2B;IAC3BX,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAsFPH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFqCH,EAAA,CAAAc,UAAA,UAAAoC,UAAA,CAAA/B,KAAA,CAAsB;IAClEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA6C,UAAA,CAAAC,KAAA,MACF;;;;;IAkBMnD,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFmCH,EAAA,CAAAc,UAAA,UAAAsC,UAAA,CAAAjC,KAAA,CAAsB;IAChEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA+C,UAAA,CAAAD,KAAA,MACF;;;;;IAJJnD,EAAA,CAAAC,cAAA,eAAiD;IAE7CD,EAAA,CAAAY,UAAA,IAAAyC,0CAAA,qBAES;IACXrD,EAAA,CAAAG,YAAA,EAAS;;;;IAHoBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAc,UAAA,YAAAwC,MAAA,CAAAC,YAAA,CAAe;;;;;IAiBpDvD,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1CH,EAAA,CAAAC,cAAA,WAAwB;IACtBD,EAAA,CAAAW,SAAA,YAAsC;IAACX,EAAA,CAAAE,MAAA,qBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAcXH,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAqB,UAAA,mBAAAmC,sDAAA;MAAA,MAAAhC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAgC,IAAA;MAAA,MAAAC,UAAA,GAAAlC,WAAA,CAAAI,SAAA;MAAA,MAAA+B,OAAA,GAAA3D,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAA6B,WAAA,CAAA8B,OAAA,CAAAC,gBAAA,CAAAF,UAAA,CAAwB;IAAA,EAAC;IAClC1D,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAW,SAAA,WAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACpF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADgBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA6D,iBAAA,CAAAH,UAAA,CAAA5B,IAAA,CAAiB;IAAoB9B,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAA6D,iBAAA,CAAAH,UAAA,CAAApB,EAAA,CAAe;IAAUtC,EAAA,CAAAI,SAAA,GACpF;IADoFJ,EAAA,CAAAK,kBAAA,SAAAL,EAAA,CAAA8D,WAAA,QAAAJ,UAAA,CAAAhB,IAAA,sBACpF;;;;;IAKJ1C,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAW,SAAA,YAA6B;IAC7BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAK/DH,EAAA,CAAAC,cAAA,cAA6E;IACnCD,EAAA,CAAAqB,UAAA,mBAAA0C,yDAAA;MAAA/D,EAAA,CAAAyB,aAAA,CAAAuC,IAAA;MAAA,MAAAC,OAAA,GAAAjE,EAAA,CAAAmC,aAAA;MAAA,OAASnC,EAAA,CAAA6B,WAAA,CAAAoC,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IACrElE,EAAA,CAAAW,SAAA,YAA4B;IAACX,EAAA,CAAAE,MAAA,kBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADnUjB,OAAM,MAAOgE,eAAe;EAmC1BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IArChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAQ,IAAI;IAGvB;IACA,KAAAC,QAAQ,GAAG,QAAQ;IACnB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,kBAAkB,GAAoB,EAAE;IAExC;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,aAAa,GAAG,SAAS;IAEzB;IACA,KAAAC,cAAc,GAAG,CACf;MAAEhE,KAAK,EAAE,KAAK;MAAEgC,KAAK,EAAE;IAAS,CAAE,EAClC;MAAEhC,KAAK,EAAE,MAAM;MAAEgC,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEhC,KAAK,EAAE,MAAM;MAAEgC,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEhC,KAAK,EAAE,OAAO;MAAEgC,KAAK,EAAE;IAAO,CAAE,CACnC;IAED;IACA,KAAAI,YAAY,GAAG,CACb;MAAEpC,KAAK,EAAE,GAAG;MAAEgC,KAAK,EAAE;IAAY,CAAE,EACnC;MAAEhC,KAAK,EAAE,GAAG;MAAEgC,KAAK,EAAE;IAAY,CAAE,EACnC;MAAEhC,KAAK,EAAE,GAAG;MAAEgC,KAAK,EAAE;IAAY,CAAE,CACpC;IAQC,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACmE,UAAU,EAAE;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACf,aAAa,CAACe,eAAe;EAC3D;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACf,WAAW,CAACgB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACf,WAAW,GAAGe,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE,EAAE;MACvC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACjC;;IAGF;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQR,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACf,EAAE,CAACwB,KAAK,CAAC;MACnBlB,QAAQ,EAAE,CAAC,QAAQ,EAAE5E,UAAU,CAAC+F,QAAQ,CAAC;MACzChE,IAAI,EAAE,CAAC,EAAE,EAAE/B,UAAU,CAAC+F,QAAQ,CAAC;MAC/BxD,EAAE,EAAE,CAAC,EAAE,EAAEvC,UAAU,CAAC+F,QAAQ,CAAC;MAC7BC,aAAa,EAAE,CAAC,EAAE,EAAEhG,UAAU,CAAC+F,QAAQ,CAAC;MACxCE,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAClG,UAAU,CAAC+F,QAAQ,EAAE/F,UAAU,CAACmG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACpG,UAAU,CAACmG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAACrG,UAAU,CAACmG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCG,KAAK,EAAE,CAAC,SAAS,EAAEtG,UAAU,CAAC+F,QAAQ,CAAC;MACvCQ,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,eAAe,EAAE,CAAC,KAAK,CAAC;MACxBC,OAAO,EAAE,CAAC,KAAK,CAAC;MAChBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBnD,YAAY,EAAE,CAAC,GAAG;KACnB,CAAC;EACJ;EAEA;;;EAGQqC,eAAeA,CAAA;IACrB,MAAMe,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAChCE,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAAC;IAChCK,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC9F,UAAU,CAACgG,UAAU,CAAC;MACzBlB,aAAa,EAAE,IAAI,CAACmB,UAAU,CAACL,QAAQ,CAAC;MACxCb,UAAU,EAAE,IAAI,CAACkB,UAAU,CAACF,QAAQ;KACrC,CAAC;EACJ;EAEA;;;EAGQE,UAAUA,CAACxE,IAAU;IAC3B,OAAOA,IAAI,CAACyE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEA;;;EAGAC,gBAAgBA,CAACC,IAAY;IAC3B,IAAI,CAAC3C,QAAQ,GAAG2C,IAAI;IACpB,IAAI,CAAC1C,cAAc,GAAG0C,IAAI,KAAK,WAAW;IAE1C,IAAI,CAACrG,UAAU,CAACgG,UAAU,CAAC;MAAEtC,QAAQ,EAAE2C;IAAI,CAAE,CAAC;IAE9C,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAACrG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEqG,aAAa,CAAC,CAACxH,UAAU,CAAC+F,QAAQ,CAAC,CAAC;KACxE,MAAM;MACL,IAAI,CAAC7E,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEsG,eAAe,EAAE;;IAEtD,IAAI,CAACvG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEuG,sBAAsB,EAAE;IAE3D;IACA,IAAIH,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAACxC,kBAAkB,GAAG,EAAE;;EAEhC;EAEA;;;EAGA7B,UAAUA,CAAA;IACR,MAAMyE,UAAU,GAAkB;MAChC5F,IAAI,EAAE,EAAE;MACRQ,EAAE,EAAE,EAAE;MACNI,IAAI,EAAE;KACP;IACD,IAAI,CAACoC,kBAAkB,CAAC6C,IAAI,CAACD,UAAU,CAAC;EAC1C;EAEA;;;EAGA7E,aAAaA,CAACZ,KAAa;IACzB,IAAI,CAAC6C,kBAAkB,CAAC8C,MAAM,CAAC3F,KAAK,EAAE,CAAC,CAAC;EAC1C;EAEA;;;EAGA4F,oBAAoBA,CAACP,IAAuC,EAAEQ,SAAkB;IAC9E,MAAMC,YAAY,GAAG,IAAI,CAAC9G,UAAU,CAACC,GAAG,CAACoG,IAAI,CAAC,EAAEnG,KAAK,IAAI,CAAC;IAC1D,IAAI6G,QAAQ,GAAGF,SAAS,GAAGC,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,YAAY,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIT,IAAI,KAAK,QAAQ,IAAIU,QAAQ,GAAG,CAAC,EAAE;MACrCA,QAAQ,GAAG,CAAC;;IAGd,IAAI,CAAC/G,UAAU,CAACgG,UAAU,CAAC;MAAE,CAACK,IAAI,GAAGU;IAAQ,CAAE,CAAC;IAEhD;IACA,IAAIV,IAAI,KAAK,QAAQ,EAAE,IAAI,CAACvC,UAAU,GAAGiD,QAAQ;IACjD,IAAIV,IAAI,KAAK,UAAU,EAAE,IAAI,CAACtC,UAAU,GAAGgD,QAAQ;IACnD,IAAIV,IAAI,KAAK,SAAS,EAAE,IAAI,CAACrC,WAAW,GAAG+C,QAAQ;EACrD;EAEA;;;EAGAG,kBAAkBA,CAAA;IAChB,MAAMlC,MAAM,GAAG,IAAI,CAAChF,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,IAAI,CAAC;IACxD,MAAMgF,QAAQ,GAAG,IAAI,CAAClF,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC5D,MAAMiF,OAAO,GAAG,IAAI,CAACnF,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC1D,OAAO8E,MAAM,GAAGE,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGAgC,aAAaA,CAACC,WAAmB;IAC/B,IAAI,CAACnD,aAAa,GAAGmD,WAAW;IAChC,IAAI,CAACpH,UAAU,CAACgG,UAAU,CAAC;MAAEZ,KAAK,EAAEgC;IAAW,CAAE,CAAC;EACpD;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACzD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAAC5D,UAAU,CAACgG,UAAU,CAAC;MAAEP,QAAQ,EAAE,IAAI,CAAC7B;IAAY,CAAE,CAAC;EAC7D;EAEA;;;EAGA0D,aAAaA,CAAA;IACX,MAAMzG,IAAI,GAAG,IAAI,CAACb,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK;IAC/C,MAAMmB,EAAE,GAAG,IAAI,CAACrB,UAAU,CAACC,GAAG,CAAC,IAAI,CAAC,EAAEC,KAAK;IAE3C,IAAI,CAACF,UAAU,CAACgG,UAAU,CAAC;MACzBnF,IAAI,EAAEQ,EAAE;MACRA,EAAE,EAAER;KACL,CAAC;EACJ;EAEA;;;EAGA0G,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvH,UAAU,CAACwH,KAAK,EAAE;MACzB,IAAI,CAAChE,SAAS,GAAG,IAAI;MAErB,MAAMiE,SAAS,GAAG,IAAI,CAACzH,UAAU,CAACE,KAAK;MACvC,MAAMwH,UAAU,GAAqB;QACnChE,QAAQ,EAAE+D,SAAS,CAAC/D,QAAQ;QAC5B7C,IAAI,EAAE4G,SAAS,CAAC5G,IAAI;QACpBQ,EAAE,EAAEoG,SAAS,CAACpG,EAAE;QAChByD,aAAa,EAAE2C,SAAS,CAAC3C,aAAa;QACtCC,UAAU,EAAE0C,SAAS,CAAC1C,UAAU;QAChC4C,UAAU,EAAE;UACV3C,MAAM,EAAEyC,SAAS,CAACzC,MAAM;UACxBE,QAAQ,EAAEuC,SAAS,CAACvC,QAAQ;UAC5BC,OAAO,EAAEsC,SAAS,CAACtC;SACpB;QACDC,KAAK,EAAEqC,SAAS,CAACrC,KAAK;QACtBC,gBAAgB,EAAEoC,SAAS,CAACpC,gBAAgB;QAC5CC,aAAa,EAAEmC,SAAS,CAACnC,aAAa;QACtCC,eAAe,EAAEkC,SAAS,CAAClC,eAAe;QAC1CC,OAAO,EAAEiC,SAAS,CAACjC,OAAO;QAC1BC,QAAQ,EAAEgC,SAAS,CAAChC;OACrB;MAED;MACA,IAAI,CAACpC,aAAa,CAACuE,gBAAgB,CAACF,UAAU,CAAC;MAE/C;MACA,IAAIG,gBAAgB;MAEpB,QAAQH,UAAU,CAAChE,QAAQ;QACzB,KAAK,QAAQ;UACXmE,gBAAgB,GAAG,IAAI,CAACxE,aAAa,CAACyE,mBAAmB,CAACJ,UAAU,CAAC;UACrE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACxE,aAAa,CAAC0E,sBAAsB,CAACL,UAAU,CAAC;UACxE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACxE,aAAa,CAAC2E,sBAAsB,CAACN,UAAU,CAAC;UACxE;QACF;UACEG,gBAAgB,GAAG,IAAI,CAACxE,aAAa,CAACyE,mBAAmB,CAACJ,UAAU,CAAC;;MAGzEG,gBAAgB,CAACtD,SAAS,CAAC;QACzB0D,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAAC1E,SAAS,GAAG,KAAK;UACtB,IAAI0E,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3BC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,QAAQ,CAAC;YAC/C;YACA;WACD,MAAM;YACLG,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEL,QAAQ,CAACC,MAAM,CAACK,QAAQ,CAAC;YACzD;;QAEJ,CAAC;;QACDD,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC/E,SAAS,GAAG,KAAK;UACtB6E,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC;QACF;OACD,CAAC;KACH,MAAM;MACL;MACAE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1I,UAAU,CAAC2I,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QAClD,IAAI,CAAC7I,UAAU,CAACC,GAAG,CAAC4I,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC3C,CAAC,CAAC;;EAEN;EAEA;;;EAGAnG,gBAAgBA,CAACoG,MAAoB;IACnC,IAAI,CAAC/I,UAAU,CAACgG,UAAU,CAAC;MACzBnF,IAAI,EAAEkI,MAAM,CAAClI,IAAI;MACjBQ,EAAE,EAAE0H,MAAM,CAAC1H,EAAE;MACbyD,aAAa,EAAEiE,MAAM,CAACtH,IAAI;MAC1BuD,MAAM,EAAE+D,MAAM,CAACpB,UAAU;MACzBzC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;;;EAGAlC,mBAAmBA,CAAA;IACjB,IAAI,CAACI,aAAa,CAACJ,mBAAmB,EAAE;EAC1C;EAEA;;;EAGA3D,eAAeA,CAAC0J,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACjJ,UAAU,CAACC,GAAG,CAAC+I,WAAW,CAAC;IAChD,IAAIC,OAAO,EAAEC,MAAM,IAAID,OAAO,CAACE,OAAO,EAAE;MACtC,IAAIF,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGF,WAAW,cAAc;;MAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oBAAoBD,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,CAACjE,GAAG,EAAE;;;IAG1D,OAAO,EAAE;EACX;;;uBA3TW/B,eAAe,EAAAnE,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvK,EAAA,CAAAqK,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAzK,EAAA,CAAAqK,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3K,EAAA,CAAAqK,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf1G,eAAe;MAAA2G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB5BpL,EAAA,CAAAC,cAAA,aAA8B;UAOpBD,EAAA,CAAAW,SAAA,WAA4B;UAC5BX,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElCH,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAE,MAAA,gDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG1EH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAAqB,UAAA,sBAAAiK,mDAAA;YAAA,OAAYD,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAEpDxI,EAAA,CAAAC,cAAA,cAAgC;UAK5BD,EAAA,CAAAqB,UAAA,mBAAAkK,kDAAA;YAAA,OAASF,GAAA,CAAAhE,gBAAA,CAAiB,QAAQ,CAAC;UAAA,EAAC;UACpCrH,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAAqB,UAAA,mBAAAmK,kDAAA;YAAA,OAASH,GAAA,CAAAhE,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvCrH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAAqB,UAAA,mBAAAoK,kDAAA;YAAA,OAASJ,GAAA,CAAAhE,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvCrH,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAmC;UAYvBD,EAAA,CAAAqB,UAAA,8BAAAqK,uEAAAnK,MAAA;YAAA,OAAoB8J,GAAA,CAAAM,sBAAA,CAAApK,MAAA,CAA8B;UAAA,EAAC;UACrDvB,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAY,UAAA,KAAAgL,+BAAA,kBAEM;UACR5L,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAuC;UAKnCD,EAAA,CAAAqB,UAAA,8BAAAwK,uEAAAtK,MAAA;YAAA,OAAoB8J,GAAA,CAAAS,oBAAA,CAAAvK,MAAA,CAA4B;UAAA,EAAC;UACnDvB,EAAA,CAAAG,YAAA,EAAmB;UACnBH,EAAA,CAAAY,UAAA,KAAAmL,+BAAA,kBAEM;UACR/L,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmC;UAE/BD,EAAA,CAAAW,SAAA,iBAIqB;UAEvBX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAAoL,+BAAA,kBAEM;UACRhM,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAY,UAAA,KAAAqL,+BAAA,mBAqCM;UAGNjM,EAAA,CAAAY,UAAA,KAAAsL,+BAAA,mBAuCM;UAGNlM,EAAA,CAAAY,UAAA,KAAAuL,+BAAA,kBAKM;UACRnM,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAqC;UAG1BD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAAgC;UAGCD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/EH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAA+K,kDAAA;YAAA,OAASf,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,KAAK,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpGH,EAAA,CAAAC,cAAA,kBAAyF;UAA/CD,EAAA,CAAAqB,UAAA,mBAAAgL,kDAAA;YAAA,OAAShB,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,IAAI,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKvGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA4B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAAiL,kDAAA;YAAA,OAASjB,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,KAAK,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtGH,EAAA,CAAAC,cAAA,kBAA2F;UAAjDD,EAAA,CAAAqB,UAAA,mBAAAkL,kDAAA;YAAA,OAASlB,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,IAAI,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKzGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAAmL,kDAAA;YAAA,OAASnB,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,KAAK,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrGH,EAAA,CAAAC,cAAA,kBAA0F;UAAhDD,EAAA,CAAAqB,UAAA,mBAAAoL,kDAAA;YAAA,OAASpB,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,IAAI,CAAC;UAAA,EAAC;UAAC7H,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKxGH,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAAqB,UAAA,mBAAAqL,kDAAA;YAAA,OAASrB,GAAA,CAAAjD,aAAA,CAAc,SAAS,CAAC;UAAA,EAAC;UAClCpI,EAAA,CAAAW,SAAA,aAA4B;UAACX,EAAA,CAAAE,MAAA,iBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAsC;UACND,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAwF;UACrED,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM/CH,EAAA,CAAAC,cAAA,eAAgC;UAGAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,eAA6B;UAEHD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAAqB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMnDH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,gBAA6B;UAEzBD,EAAA,CAAAY,UAAA,MAAA+L,mCAAA,qBAES;UACX3M,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,gBAA6B;UAMvBD,EAAA,CAAAqB,UAAA,oBAAAuL,mDAAA;YAAA,OAAUvB,GAAA,CAAA/C,cAAA,EAAgB;UAAA,EAAC;UAJ7BtI,EAAA,CAAAG,YAAA,EAI8B;UAC9BH,EAAA,CAAAC,cAAA,kBAA6C;UAC3CD,EAAA,CAAAY,UAAA,MAAAiM,iCAAA,mBAMO;UACT7M,EAAA,CAAAG,YAAA,EAAQ;UAOhBH,EAAA,CAAAC,cAAA,gBAAmC;UAK/BD,EAAA,CAAAY,UAAA,MAAAkM,iCAAA,mBAA0C;UAC1C9M,EAAA,CAAAY,UAAA,MAAAmM,iCAAA,mBAEO;UACT/M,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,gBAAmC;UAE3BD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGlDH,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAY,UAAA,MAAAoM,gCAAA,mBAYM;;UAGNhN,EAAA,CAAAY,UAAA,MAAAqM,gCAAA,kBAIM;;UACRjN,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAY,UAAA,MAAAsM,gCAAA,kBAIM;;UACRlN,EAAA,CAAAG,YAAA,EAAM;;;;;;;;UA3UEH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAc,UAAA,cAAAuK,GAAA,CAAApK,UAAA,CAAwB;UAMxBjB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAmN,WAAA,WAAA9B,GAAA,CAAA1G,QAAA,cAAsC;UAOtC3E,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAmN,WAAA,WAAA9B,GAAA,CAAA1G,QAAA,iBAAyC;UAOzC3E,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAmN,WAAA,WAAA9B,GAAA,CAAA1G,QAAA,iBAAyC;UAqBP3E,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAA9K,eAAA,SAA6B;UAa7BP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAA9K,eAAA,OAA2B;UAe3BP,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAA9K,eAAA,kBAAsC;UAQ3CP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAAzG,cAAA,CAAoB;UAwCD5E,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAc,UAAA,YAAAuK,GAAA,CAAAvG,kBAAA,CAAuB;UA0CtC9E,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAA1G,QAAA,iBAA8B;UAkB7B3E,EAAA,CAAAI,SAAA,IAA0C;UAA1CJ,EAAA,CAAA6D,iBAAA,GAAAuJ,QAAA,GAAA/B,GAAA,CAAApK,UAAA,CAAAC,GAAA,6BAAAkM,QAAA,CAAAjM,KAAA,OAA0C;UAU1CnB,EAAA,CAAAI,SAAA,IAA4C;UAA5CJ,EAAA,CAAA6D,iBAAA,GAAAwJ,QAAA,GAAAhC,GAAA,CAAApK,UAAA,CAAAC,GAAA,+BAAAmM,QAAA,CAAAlM,KAAA,OAA4C;UAU5CnB,EAAA,CAAAI,SAAA,IAA2C;UAA3CJ,EAAA,CAAA6D,iBAAA,GAAAyJ,QAAA,GAAAjC,GAAA,CAAApK,UAAA,CAAAC,GAAA,8BAAAoM,QAAA,CAAAnM,KAAA,OAA2C;UAYvEnB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAmN,WAAA,WAAA9B,GAAA,CAAAnG,aAAA,eAA4C;UAwCnBlF,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAc,UAAA,YAAAuK,GAAA,CAAAlG,cAAA,CAAiB;UAkBbnF,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAAxG,YAAA,CAAkB;UAkBrD7E,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAc,UAAA,aAAAuK,GAAA,CAAA5G,SAAA,KAAA4G,GAAA,CAAApK,UAAA,CAAAwH,KAAA,CAA2C;UACpCzI,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,UAAAuK,GAAA,CAAA5G,SAAA,CAAgB;UAChBzE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAc,UAAA,SAAAuK,GAAA,CAAA5G,SAAA,CAAe;UAiBLzE,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAuN,WAAA,UAAAlC,GAAA,CAAAhG,eAAA,EAA0B;UAczCrF,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAc,UAAA,WAAA0M,QAAA,GAAAxN,EAAA,CAAAuN,WAAA,UAAAlC,GAAA,CAAAhG,eAAA,oBAAAmI,QAAA,CAAAC,MAAA,QAA6C;UAQtBzN,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAc,UAAA,WAAA4M,QAAA,GAAA1N,EAAA,CAAAuN,WAAA,UAAAlC,GAAA,CAAAhG,eAAA,oBAAAqI,QAAA,CAAAD,MAAA,MAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}