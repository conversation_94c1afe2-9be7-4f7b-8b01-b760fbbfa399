import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';
import { AutocompleteService, AutocompleteLocation } from '../../../services/autocomplete.service';

@Component({
  selector: 'app-autocomplete',
  templateUrl: './autocomplete.component.html',
  styleUrls: ['./autocomplete.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AutocompleteComponent),
      multi: true
    }
  ]
})
export class AutocompleteComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() placeholder: string = '';
  @Input() icon: string = '';
  @Input() readonly: boolean = false;
  @Output() locationSelected = new EventEmitter<AutocompleteLocation>();

  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;

  value: string = '';
  suggestions: AutocompleteLocation[] = [];
  showSuggestions: boolean = false;
  selectedIndex: number = -1;
  isLoading: boolean = false;

  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();
  private onChange = (value: string) => {};
  private onTouched = () => {};

  constructor(
    private autocompleteService: AutocompleteService,
    private elementRef: ElementRef
  ) {}

  ngOnInit(): void {
    // Setup search with debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(query => this.autocompleteService.searchLocations(query)),
      takeUntil(this.destroy$)
    ).subscribe(suggestions => {
      this.suggestions = suggestions;
      this.showSuggestions = suggestions.length > 0;
      this.isLoading = false;
    });

    // Close suggestions when clicking outside
    document.addEventListener('click', this.onDocumentClick.bind(this));
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    document.removeEventListener('click', this.onDocumentClick.bind(this));
  }

  // ControlValueAccessor implementation
  writeValue(value: string): void {
    this.value = value || '';
    if (this.inputElement) {
      this.inputElement.nativeElement.value = this.value;
    }
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    if (this.inputElement) {
      this.inputElement.nativeElement.disabled = isDisabled;
    }
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);

    if (this.value.length >= 2) {
      this.isLoading = true;
      this.searchSubject.next(this.value);
    } else {
      this.suggestions = [];
      this.showSuggestions = false;
      this.isLoading = false;
    }

    this.selectedIndex = -1;
  }

  onFocus(): void {
    this.onTouched();
    if (this.value.length >= 2) {
      this.showSuggestions = this.suggestions.length > 0;
    } else if (this.value.length === 0) {
      // Show popular destinations when focusing on empty input
      this.autocompleteService.getPopularDestinations().subscribe(destinations => {
        this.suggestions = destinations;
        this.showSuggestions = true;
      });
    }
  }

  onKeyDown(event: KeyboardEvent): void {
    if (!this.showSuggestions) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
          this.selectSuggestion(this.suggestions[this.selectedIndex]);
        }
        break;
      case 'Escape':
        this.hideSuggestions();
        break;
    }
  }

  selectSuggestion(location: AutocompleteLocation): void {
    this.value = location.displayText;
    this.onChange(this.value);
    this.locationSelected.emit(location);
    this.hideSuggestions();

    if (this.inputElement) {
      this.inputElement.nativeElement.value = this.value;
    }
  }

  hideSuggestions(): void {
    this.showSuggestions = false;
    this.selectedIndex = -1;
  }

  private onDocumentClick(event: Event): void {
    if (!this.elementRef.nativeElement.contains(event.target as Node)) {
      this.hideSuggestions();
    }
  }

  getLocationTypeIcon(type: string): string {
    switch (type) {
      case 'airport':
        return 'fas fa-plane';
      case 'city':
        return 'fas fa-city';
      case 'country':
        return 'fas fa-flag';
      default:
        return 'fas fa-map-marker-alt';
    }
  }
}
