import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { AuthRequest, AuthResponse, LoginCredentials, LoginResponse } from '../models/auth.models';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly API_URL = `${environment.apiUrl}${environment.authEndpoint}`;
  private readonly TOKEN_KEY = 'auth_token';
  private readonly TOKEN_EXPIRY_KEY = 'token_expiry';
  private readonly USER_KEY = 'user_data';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {
    // Check token expiry every minute
    setInterval(() => {
      this.checkTokenExpiry();
    }, 60000);
  }

  /**
   * Authenticate user with agency credentials
   */
  login(credentials: LoginCredentials): Observable<LoginResponse> {
    const authRequest: AuthRequest = {
      Agency: credentials.agency,
      User: credentials.username,
      Password: credentials.password
    };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<AuthResponse>(this.API_URL, authRequest, { headers })
      .pipe(
        map(response => {
          if (response.header.success && response.body.token) {
            // Store token with expiry date
            this.setToken(response.body.token, response.body.expiresOn);

            // Extract user data from Paximum response
            const userData = {
              code: response.body.userInfo?.code || '',
              name: response.body.userInfo?.name || '',
              agencyCode: response.body.userInfo?.agency?.code || '',
              agencyName: response.body.userInfo?.agency?.name || '',
              office: response.body.userInfo?.office || {},
              operator: response.body.userInfo?.operator || {},
              market: response.body.userInfo?.market || {}
            };
            this.setUserData(userData);

            // Update subjects
            this.isAuthenticatedSubject.next(true);
            this.currentUserSubject.next(userData);

            console.log('Login successful, token expires on:', response.body.expiresOn);

            return {
              success: true,
              token: response.body.token,
              user: userData,
              message: response.header.messages?.[0]?.message || 'Login successful'
            };
          } else {
            throw new Error(response.header.responseMessage || 'Authentication failed');
          }
        }),
        catchError(error => {
          console.error('Login error:', error);
          let errorMessage = 'An error occurred during login';

          if (error.error?.header?.responseMessage) {
            errorMessage = error.error.header.responseMessage;
          } else if (error.message) {
            errorMessage = error.message;
          } else if (error.status === 401) {
            errorMessage = 'Invalid credentials';
          } else if (error.status === 0) {
            errorMessage = 'Unable to connect to server';
          }

          return throwError(() => ({
            success: false,
            message: errorMessage
          }));
        })
      );
  }

  /**
   * Logout user
   */
  logout(): void {
    this.removeToken();
    this.removeUserData();
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.hasValidToken();
  }

  /**
   * Get current user data
   */
  getCurrentUser(): any {
    const userData = localStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Get authentication token
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Set authentication token with expiry
   */
  private setToken(token: string, expiresOn?: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
    if (expiresOn) {
      localStorage.setItem(this.TOKEN_EXPIRY_KEY, expiresOn);
    }
  }

  /**
   * Remove authentication token
   */
  private removeToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.TOKEN_EXPIRY_KEY);
  }

  /**
   * Set user data
   */
  private setUserData(userData: any): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
  }

  /**
   * Remove user data
   */
  private removeUserData(): void {
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * Check if token exists
   */
  private hasToken(): boolean {
    return !!localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Check if token is valid (exists and not expired)
   */
  private hasValidToken(): boolean {
    return this.hasToken() && !this.isTokenExpired();
  }

  /**
   * Check if token is expired based on expiresOn date from Paximum API
   */
  private isTokenExpired(): boolean {
    const expiryDate = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    if (!expiryDate) {
      return false; // If no expiry date, assume token is valid
    }

    const now = new Date();
    const expiry = new Date(expiryDate);
    return now >= expiry;
  }

  /**
   * Check token expiry and auto-logout if expired
   */
  private checkTokenExpiry(): void {
    if (this.hasToken() && this.isTokenExpired()) {
      console.warn('Token expired, logging out user');
      this.logout();
    }
  }

  /**
   * Get token expiry date
   */
  getTokenExpiry(): Date | null {
    const expiryDate = localStorage.getItem(this.TOKEN_EXPIRY_KEY);
    return expiryDate ? new Date(expiryDate) : null;
  }

  /**
   * Check if token will expire soon (within 5 minutes)
   */
  isTokenExpiringSoon(): boolean {
    const expiryDate = this.getTokenExpiry();
    if (!expiryDate) return false;

    const now = new Date();
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);
    return expiryDate <= fiveMinutesFromNow;
  }

  /**
   * Get authorization headers for API calls
   */
  getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
}
