{"ast": null, "code": "import { interval } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction TokenExpiryAlertComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n    i0.ɵɵelement(3, \"i\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5)(5, \"h4\");\n    i0.ɵɵtext(6, \"Session Expiring Soon\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Your session will expire in \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"p\");\n    i0.ɵɵtext(12, \"Please refresh your session to continue working.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 6)(14, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function TokenExpiryAlertComponent_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRefreshToken());\n    });\n    i0.ɵɵelement(15, \"i\", 8);\n    i0.ɵɵtext(16, \" Refresh Session \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function TokenExpiryAlertComponent_div_0_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onDismiss());\n    });\n    i0.ɵɵelement(18, \"i\", 10);\n    i0.ɵɵtext(19, \" Dismiss \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r0.timeRemaining);\n  }\n}\nexport class TokenExpiryAlertComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.showAlert = false;\n    this.timeRemaining = '';\n  }\n  ngOnInit() {\n    // Check token expiry every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkTokenExpiry();\n    });\n    // Initial check\n    this.checkTokenExpiry();\n  }\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  checkTokenExpiry() {\n    if (!this.authService.isAuthenticated()) {\n      this.showAlert = false;\n      return;\n    }\n    const expiryDate = this.authService.getTokenExpiry();\n    if (!expiryDate) {\n      this.showAlert = false;\n      return;\n    }\n    const now = new Date();\n    const timeDiff = expiryDate.getTime() - now.getTime();\n    // Show alert if token expires within 10 minutes\n    if (timeDiff > 0 && timeDiff <= 10 * 60 * 1000) {\n      this.showAlert = true;\n      this.timeRemaining = this.formatTimeRemaining(timeDiff);\n    } else if (timeDiff <= 0) {\n      // Token has expired\n      this.showAlert = false;\n      this.authService.logout();\n      this.router.navigate(['/signin']);\n    } else {\n      this.showAlert = false;\n    }\n  }\n  formatTimeRemaining(milliseconds) {\n    const minutes = Math.floor(milliseconds / (1000 * 60));\n    const seconds = Math.floor(milliseconds % (1000 * 60) / 1000);\n    return `${minutes}m ${seconds}s`;\n  }\n  onRefreshToken() {\n    // Redirect to login to get a new token\n    this.router.navigate(['/signin']);\n  }\n  onDismiss() {\n    this.showAlert = false;\n  }\n  static {\n    this.ɵfac = function TokenExpiryAlertComponent_Factory(t) {\n      return new (t || TokenExpiryAlertComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TokenExpiryAlertComponent,\n      selectors: [[\"app-token-expiry-alert\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"token-expiry-alert\", 4, \"ngIf\"], [1, \"token-expiry-alert\"], [1, \"alert-content\"], [1, \"alert-icon\"], [1, \"fas\", \"fa-exclamation-triangle\"], [1, \"alert-message\"], [1, \"alert-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"]],\n      template: function TokenExpiryAlertComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, TokenExpiryAlertComponent_div_0_Template, 20, 1, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showAlert);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".token-expiry-alert[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 20px;\\n  right: 20px;\\n  z-index: 9999;\\n  max-width: 400px;\\n  background: #fff;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\\n  border-left: 4px solid #f39c12;\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    transform: translateX(100%);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateX(0);\\n    opacity: 1;\\n  }\\n}\\n\\n.alert-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  padding: 20px;\\n  gap: 15px;\\n}\\n\\n.alert-icon[_ngcontent-%COMP%] {\\n  color: #f39c12;\\n  font-size: 24px;\\n  margin-top: 2px;\\n}\\n\\n.alert-message[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.alert-message[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n}\\n\\n.alert-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #666;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.alert-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 15px;\\n}\\n\\n.alert-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  margin-top: 10px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: none;\\n  border-radius: 4px;\\n  font-size: 12px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #4a90e2;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #357abd;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #5a6268;\\n}\\n\\n.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 11px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .token-expiry-alert[_ngcontent-%COMP%] {\\n    top: 10px;\\n    right: 10px;\\n    left: 10px;\\n    max-width: none;\\n  }\\n  \\n  .alert-content[_ngcontent-%COMP%] {\\n    padding: 15px;\\n    gap: 12px;\\n  }\\n  \\n  .alert-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .btn[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["interval", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "TokenExpiryAlertComponent_div_0_Template_button_click_14_listener", "ɵɵrestoreView", "_r2", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onRefreshToken", "TokenExpiryAlertComponent_div_0_Template_button_click_17_listener", "ctx_r3", "on<PERSON><PERSON><PERSON>", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "timeRemaining", "TokenExpiryAlertComponent", "constructor", "authService", "router", "show<PERSON><PERSON><PERSON>", "ngOnInit", "subscription", "subscribe", "checkTokenExpiry", "ngOnDestroy", "unsubscribe", "isAuthenticated", "expiryDate", "getTokenExpiry", "now", "Date", "timeDiff", "getTime", "formatTimeRemaining", "logout", "navigate", "milliseconds", "minutes", "Math", "floor", "seconds", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "TokenExpiryAlertComponent_Template", "rf", "ctx", "ɵɵtemplate", "TokenExpiryAlertComponent_div_0_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\shared\\token-expiry-alert\\token-expiry-alert.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\shared\\token-expiry-alert\\token-expiry-alert.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Subscription, interval } from 'rxjs';\nimport { AuthService } from '../../../services/auth.service';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-token-expiry-alert',\n  templateUrl: './token-expiry-alert.component.html',\n  styleUrls: ['./token-expiry-alert.component.css']\n})\nexport class TokenExpiryAlertComponent implements OnInit, OnDestroy {\n  showAlert = false;\n  timeRemaining = '';\n  private subscription?: Subscription;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Check token expiry every 30 seconds\n    this.subscription = interval(30000).subscribe(() => {\n      this.checkTokenExpiry();\n    });\n\n    // Initial check\n    this.checkTokenExpiry();\n  }\n\n  ngOnDestroy(): void {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n  private checkTokenExpiry(): void {\n    if (!this.authService.isAuthenticated()) {\n      this.showAlert = false;\n      return;\n    }\n\n    const expiryDate = this.authService.getTokenExpiry();\n    if (!expiryDate) {\n      this.showAlert = false;\n      return;\n    }\n\n    const now = new Date();\n    const timeDiff = expiryDate.getTime() - now.getTime();\n    \n    // Show alert if token expires within 10 minutes\n    if (timeDiff > 0 && timeDiff <= 10 * 60 * 1000) {\n      this.showAlert = true;\n      this.timeRemaining = this.formatTimeRemaining(timeDiff);\n    } else if (timeDiff <= 0) {\n      // Token has expired\n      this.showAlert = false;\n      this.authService.logout();\n      this.router.navigate(['/signin']);\n    } else {\n      this.showAlert = false;\n    }\n  }\n\n  private formatTimeRemaining(milliseconds: number): string {\n    const minutes = Math.floor(milliseconds / (1000 * 60));\n    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);\n    return `${minutes}m ${seconds}s`;\n  }\n\n  onRefreshToken(): void {\n    // Redirect to login to get a new token\n    this.router.navigate(['/signin']);\n  }\n\n  onDismiss(): void {\n    this.showAlert = false;\n  }\n}\n", "<div class=\"token-expiry-alert\" *ngIf=\"showAlert\">\n  <div class=\"alert-content\">\n    <div class=\"alert-icon\">\n      <i class=\"fas fa-exclamation-triangle\"></i>\n    </div>\n    <div class=\"alert-message\">\n      <h4>Session Expiring Soon</h4>\n      <p>Your session will expire in <strong>{{ timeRemaining }}</strong></p>\n      <p>Please refresh your session to continue working.</p>\n    </div>\n    <div class=\"alert-actions\">\n      <button class=\"btn btn-primary\" (click)=\"onRefreshToken()\">\n        <i class=\"fas fa-sync-alt\"></i> Refresh Session\n      </button>\n      <button class=\"btn btn-secondary\" (click)=\"onDismiss()\">\n        <i class=\"fas fa-times\"></i> Dismiss\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAuBA,QAAQ,QAAQ,MAAM;;;;;;;;ICD7CC,EAAA,CAAAC,cAAA,aAAkD;IAG5CD,EAAA,CAAAE,SAAA,WAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA2B;IACrBD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,mCAA4B;IAAAJ,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAI,MAAA,IAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACnEH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAI,MAAA,wDAAgD;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAEzDH,EAAA,CAAAC,cAAA,cAA2B;IACOD,EAAA,CAAAK,UAAA,mBAAAC,kEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IACxDZ,EAAA,CAAAE,SAAA,YAA+B;IAACF,EAAA,CAAAI,MAAA,yBAClC;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAwD;IAAtBD,EAAA,CAAAK,UAAA,mBAAAQ,kEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAG,MAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACrDf,EAAA,CAAAE,SAAA,aAA4B;IAACF,EAAA,CAAAI,MAAA,iBAC/B;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAT8BH,EAAA,CAAAgB,SAAA,IAAmB;IAAnBhB,EAAA,CAAAiB,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;;;ADGhE,OAAM,MAAOC,yBAAyB;EAKpCC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAL,aAAa,GAAG,EAAE;EAMf;EAEHM,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,YAAY,GAAG3B,QAAQ,CAAC,KAAK,CAAC,CAAC4B,SAAS,CAAC,MAAK;MACjD,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAACA,gBAAgB,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACH,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACI,WAAW,EAAE;;EAEnC;EAEQF,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACN,WAAW,CAACS,eAAe,EAAE,EAAE;MACvC,IAAI,CAACP,SAAS,GAAG,KAAK;MACtB;;IAGF,MAAMQ,UAAU,GAAG,IAAI,CAACV,WAAW,CAACW,cAAc,EAAE;IACpD,IAAI,CAACD,UAAU,EAAE;MACf,IAAI,CAACR,SAAS,GAAG,KAAK;MACtB;;IAGF,MAAMU,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,QAAQ,GAAGJ,UAAU,CAACK,OAAO,EAAE,GAAGH,GAAG,CAACG,OAAO,EAAE;IAErD;IACA,IAAID,QAAQ,GAAG,CAAC,IAAIA,QAAQ,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MAC9C,IAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,IAAI,CAACL,aAAa,GAAG,IAAI,CAACmB,mBAAmB,CAACF,QAAQ,CAAC;KACxD,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxB;MACA,IAAI,CAACZ,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,WAAW,CAACiB,MAAM,EAAE;MACzB,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;KAClC,MAAM;MACL,IAAI,CAAChB,SAAS,GAAG,KAAK;;EAE1B;EAEQc,mBAAmBA,CAACG,YAAoB;IAC9C,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IACtD,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEH,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,GAAI,IAAI,CAAC;IAC/D,OAAO,GAAGC,OAAO,KAAKG,OAAO,GAAG;EAClC;EAEAjC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACW,MAAM,CAACiB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;EAEAzB,SAASA,CAAA;IACP,IAAI,CAACS,SAAS,GAAG,KAAK;EACxB;;;uBApEWJ,yBAAyB,EAAApB,EAAA,CAAA8C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhD,EAAA,CAAA8C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAzB9B,yBAAyB;MAAA+B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVtCzD,EAAA,CAAA2D,UAAA,IAAAC,wCAAA,kBAmBM;;;UAnB2B5D,EAAA,CAAA6D,UAAA,SAAAH,GAAA,CAAAlC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}