.autocomplete-wrapper {
  position: relative;
  width: 100%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 15px;
  color: #a0aec0;
  z-index: 2;
  font-size: 14px;
}

.autocomplete-input {
  width: 100%;
  padding: 15px 45px 15px 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
  outline: none;
}

.autocomplete-input.with-icon {
  padding-left: 45px;
}

.autocomplete-input.readonly {
  background-color: #f8f9fa;
  color: #6c757d;
}

.autocomplete-input:focus {
  border-color: #3b4371;
  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);
}

.loading-spinner {
  position: absolute;
  right: 15px;
  color: #a0aec0;
  font-size: 14px;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.suggestions-list {
  padding: 8px 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f7fafc;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: #f7fafc;
}

.suggestion-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e2e8f0;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.suggestion-icon i {
  color: #4a5568;
  font-size: 14px;
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

.suggestion-main {
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.suggestion-details {
  font-size: 12px;
  color: #718096;
  margin-top: 2px;
}

.suggestion-type {
  margin-left: 8px;
  flex-shrink: 0;
}

.type-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.type-badge.type-airport {
  background: #e6fffa;
  color: #234e52;
}

.type-badge.type-city {
  background: #fef5e7;
  color: #744210;
}

.type-badge.type-country {
  background: #f0f4ff;
  color: #3c4fe0;
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #a0aec0;
  font-size: 14px;
  gap: 8px;
}

.no-results i {
  font-size: 16px;
}

/* Scrollbar styling */
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation */
.suggestions-dropdown {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .suggestion-main {
    font-size: 13px;
  }
  
  .suggestion-details {
    font-size: 11px;
  }
  
  .suggestion-item {
    padding: 10px 12px;
  }
  
  .suggestion-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
  }
}
