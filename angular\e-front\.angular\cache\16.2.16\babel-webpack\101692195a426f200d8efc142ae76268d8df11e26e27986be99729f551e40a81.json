{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}${environment.authEndpoint}`;\n    this.TOKEN_KEY = 'auth_token';\n    this.TOKEN_EXPIRY_KEY = 'token_expiry';\n    this.USER_KEY = 'user_data';\n    this.isAuthenticatedSubject = new BehaviorSubject(this.hasValidToken());\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.currentUserSubject = new BehaviorSubject(this.getCurrentUser());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    // Check token expiry every minute\n    setInterval(() => {\n      this.checkTokenExpiry();\n    }, 60000);\n  }\n  /**\n   * Authenticate user with agency credentials\n   */\n  login(credentials) {\n    const authRequest = {\n      Agency: credentials.agency,\n      User: credentials.username,\n      Password: credentials.password\n    };\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    return this.http.post(this.API_URL, authRequest, {\n      headers\n    }).pipe(map(response => {\n      if (response.header.success && response.body.token) {\n        // Store token with expiry date\n        this.setToken(response.body.token, response.body.expiresOn);\n        // Extract user data from Paximum response\n        const userData = {\n          code: response.body.userInfo?.code || '',\n          name: response.body.userInfo?.name || '',\n          agencyCode: response.body.userInfo?.agency?.code || '',\n          agencyName: response.body.userInfo?.agency?.name || '',\n          office: response.body.userInfo?.office || {},\n          operator: response.body.userInfo?.operator || {},\n          market: response.body.userInfo?.market || {}\n        };\n        this.setUserData(userData);\n        // Update subjects\n        this.isAuthenticatedSubject.next(true);\n        this.currentUserSubject.next(userData);\n        console.log('Login successful, token expires on:', response.body.expiresOn);\n        return {\n          success: true,\n          token: response.body.token,\n          user: userData,\n          message: response.header.messages?.[0]?.message || 'Login successful'\n        };\n      } else {\n        throw new Error(response.header.responseMessage || 'Authentication failed');\n      }\n    }), catchError(error => {\n      console.error('Login error:', error);\n      let errorMessage = 'An error occurred during login';\n      if (error.error?.header?.responseMessage) {\n        errorMessage = error.error.header.responseMessage;\n      } else if (error.message) {\n        errorMessage = error.message;\n      } else if (error.status === 401) {\n        errorMessage = 'Invalid credentials';\n      } else if (error.status === 0) {\n        errorMessage = 'Unable to connect to server';\n      }\n      return throwError(() => ({\n        success: false,\n        message: errorMessage\n      }));\n    }));\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.removeToken();\n    this.removeUserData();\n    this.isAuthenticatedSubject.next(false);\n    this.currentUserSubject.next(null);\n  }\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return this.hasValidToken();\n  }\n  /**\n   * Get current user data\n   */\n  getCurrentUser() {\n    const userData = localStorage.getItem(this.USER_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n  /**\n   * Get authentication token\n   */\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n  /**\n   * Set authentication token\n   */\n  setToken(token) {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n  /**\n   * Remove authentication token\n   */\n  removeToken() {\n    localStorage.removeItem(this.TOKEN_KEY);\n  }\n  /**\n   * Set user data\n   */\n  setUserData(userData) {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));\n  }\n  /**\n   * Remove user data\n   */\n  removeUserData() {\n    localStorage.removeItem(this.USER_KEY);\n  }\n  /**\n   * Check if token exists\n   */\n  hasToken() {\n    return !!localStorage.getItem(this.TOKEN_KEY);\n  }\n  /**\n   * Check if token is expired (basic check)\n   * Note: This is a simple implementation. In production, you should decode the JWT token\n   * and check the expiration time properly.\n   */\n  isTokenExpired() {\n    // For now, we'll assume the token is valid if it exists\n    // In a real implementation, you would decode the JWT and check the exp claim\n    return false;\n  }\n  /**\n   * Get authorization headers for API calls\n   */\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "environment", "AuthService", "constructor", "http", "API_URL", "apiUrl", "authEndpoint", "TOKEN_KEY", "TOKEN_EXPIRY_KEY", "USER_KEY", "isAuthenticatedSubject", "hasValidToken", "isAuthenticated$", "asObservable", "currentUserSubject", "getCurrentUser", "currentUser$", "setInterval", "checkTokenExpiry", "login", "credentials", "authRequest", "Agency", "agency", "User", "username", "Password", "password", "headers", "post", "pipe", "response", "header", "success", "body", "token", "setToken", "expiresOn", "userData", "code", "userInfo", "name", "agencyCode", "agencyName", "office", "operator", "market", "setUserData", "next", "console", "log", "user", "message", "messages", "Error", "responseMessage", "error", "errorMessage", "status", "logout", "removeToken", "removeUserData", "isAuthenticated", "localStorage", "getItem", "JSON", "parse", "getToken", "setItem", "removeItem", "stringify", "hasToken", "isTokenExpired", "getAuthHeaders", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { AuthRequest, AuthResponse, LoginCredentials, LoginResponse } from '../models/auth.models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = `${environment.apiUrl}${environment.authEndpoint}`;\n  private readonly TOKEN_KEY = 'auth_token';\n  private readonly TOKEN_EXPIRY_KEY = 'token_expiry';\n  private readonly USER_KEY = 'user_data';\n\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(private http: HttpClient) {\n    // Check token expiry every minute\n    setInterval(() => {\n      this.checkTokenExpiry();\n    }, 60000);\n  }\n\n  /**\n   * Authenticate user with agency credentials\n   */\n  login(credentials: LoginCredentials): Observable<LoginResponse> {\n    const authRequest: AuthRequest = {\n      Agency: credentials.agency,\n      User: credentials.username,\n      Password: credentials.password\n    };\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    return this.http.post<AuthResponse>(this.API_URL, authRequest, { headers })\n      .pipe(\n        map(response => {\n          if (response.header.success && response.body.token) {\n            // Store token with expiry date\n            this.setToken(response.body.token, response.body.expiresOn);\n\n            // Extract user data from Paximum response\n            const userData = {\n              code: response.body.userInfo?.code || '',\n              name: response.body.userInfo?.name || '',\n              agencyCode: response.body.userInfo?.agency?.code || '',\n              agencyName: response.body.userInfo?.agency?.name || '',\n              office: response.body.userInfo?.office || {},\n              operator: response.body.userInfo?.operator || {},\n              market: response.body.userInfo?.market || {}\n            };\n            this.setUserData(userData);\n\n            // Update subjects\n            this.isAuthenticatedSubject.next(true);\n            this.currentUserSubject.next(userData);\n\n            console.log('Login successful, token expires on:', response.body.expiresOn);\n\n            return {\n              success: true,\n              token: response.body.token,\n              user: userData,\n              message: response.header.messages?.[0]?.message || 'Login successful'\n            };\n          } else {\n            throw new Error(response.header.responseMessage || 'Authentication failed');\n          }\n        }),\n        catchError(error => {\n          console.error('Login error:', error);\n          let errorMessage = 'An error occurred during login';\n\n          if (error.error?.header?.responseMessage) {\n            errorMessage = error.error.header.responseMessage;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.status === 401) {\n            errorMessage = 'Invalid credentials';\n          } else if (error.status === 0) {\n            errorMessage = 'Unable to connect to server';\n          }\n\n          return throwError(() => ({\n            success: false,\n            message: errorMessage\n          }));\n        })\n      );\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.removeToken();\n    this.removeUserData();\n    this.isAuthenticatedSubject.next(false);\n    this.currentUserSubject.next(null);\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.hasValidToken();\n  }\n\n  /**\n   * Get current user data\n   */\n  getCurrentUser(): any {\n    const userData = localStorage.getItem(this.USER_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n\n  /**\n   * Get authentication token\n   */\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set authentication token\n   */\n  private setToken(token: string): void {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n\n  /**\n   * Remove authentication token\n   */\n  private removeToken(): void {\n    localStorage.removeItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set user data\n   */\n  private setUserData(userData: any): void {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));\n  }\n\n  /**\n   * Remove user data\n   */\n  private removeUserData(): void {\n    localStorage.removeItem(this.USER_KEY);\n  }\n\n  /**\n   * Check if token exists\n   */\n  private hasToken(): boolean {\n    return !!localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Check if token is expired (basic check)\n   * Note: This is a simple implementation. In production, you should decode the JWT token\n   * and check the expiration time properly.\n   */\n  private isTokenExpired(): boolean {\n    // For now, we'll assume the token is valid if it exists\n    // In a real implementation, you would decode the JWT and check the exp claim\n    return false;\n  }\n\n  /**\n   * Get authorization headers for API calls\n   */\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAa,gBAAgB;AAErD,SAASC,WAAW,QAAQ,gCAAgC;;;AAK5D,OAAM,MAAOC,WAAW;EAYtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAXP,KAAAC,OAAO,GAAG,GAAGJ,WAAW,CAACK,MAAM,GAAGL,WAAW,CAACM,YAAY,EAAE;IAC5D,KAAAC,SAAS,GAAG,YAAY;IACxB,KAAAC,gBAAgB,GAAG,cAAc;IACjC,KAAAC,QAAQ,GAAG,WAAW;IAE/B,KAAAC,sBAAsB,GAAG,IAAId,eAAe,CAAU,IAAI,CAACe,aAAa,EAAE,CAAC;IAC5E,KAAAC,gBAAgB,GAAG,IAAI,CAACF,sBAAsB,CAACG,YAAY,EAAE;IAE5D,KAAAC,kBAAkB,GAAG,IAAIlB,eAAe,CAAM,IAAI,CAACmB,cAAc,EAAE,CAAC;IACrE,KAAAC,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACD,YAAY,EAAE;IAG1D;IACAI,WAAW,CAAC,MAAK;MACf,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,KAAK,CAAC;EACX;EAEA;;;EAGAC,KAAKA,CAACC,WAA6B;IACjC,MAAMC,WAAW,GAAgB;MAC/BC,MAAM,EAAEF,WAAW,CAACG,MAAM;MAC1BC,IAAI,EAAEJ,WAAW,CAACK,QAAQ;MAC1BC,QAAQ,EAAEN,WAAW,CAACO;KACvB;IAED,MAAMC,OAAO,GAAG,IAAIjC,WAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,OAAO,IAAI,CAACQ,IAAI,CAAC0B,IAAI,CAAe,IAAI,CAACzB,OAAO,EAAEiB,WAAW,EAAE;MAAEO;IAAO,CAAE,CAAC,CACxEE,IAAI,CACHhC,GAAG,CAACiC,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,KAAK,EAAE;QAClD;QACA,IAAI,CAACC,QAAQ,CAACL,QAAQ,CAACG,IAAI,CAACC,KAAK,EAAEJ,QAAQ,CAACG,IAAI,CAACG,SAAS,CAAC;QAE3D;QACA,MAAMC,QAAQ,GAAG;UACfC,IAAI,EAAER,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAED,IAAI,IAAI,EAAE;UACxCE,IAAI,EAAEV,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEC,IAAI,IAAI,EAAE;UACxCC,UAAU,EAAEX,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEjB,MAAM,EAAEgB,IAAI,IAAI,EAAE;UACtDI,UAAU,EAAEZ,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEjB,MAAM,EAAEkB,IAAI,IAAI,EAAE;UACtDG,MAAM,EAAEb,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEI,MAAM,IAAI,EAAE;UAC5CC,QAAQ,EAAEd,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEK,QAAQ,IAAI,EAAE;UAChDC,MAAM,EAAEf,QAAQ,CAACG,IAAI,CAACM,QAAQ,EAAEM,MAAM,IAAI;SAC3C;QACD,IAAI,CAACC,WAAW,CAACT,QAAQ,CAAC;QAE1B;QACA,IAAI,CAAC5B,sBAAsB,CAACsC,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAAClC,kBAAkB,CAACkC,IAAI,CAACV,QAAQ,CAAC;QAEtCW,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEnB,QAAQ,CAACG,IAAI,CAACG,SAAS,CAAC;QAE3E,OAAO;UACLJ,OAAO,EAAE,IAAI;UACbE,KAAK,EAAEJ,QAAQ,CAACG,IAAI,CAACC,KAAK;UAC1BgB,IAAI,EAAEb,QAAQ;UACdc,OAAO,EAAErB,QAAQ,CAACC,MAAM,CAACqB,QAAQ,GAAG,CAAC,CAAC,EAAED,OAAO,IAAI;SACpD;OACF,MAAM;QACL,MAAM,IAAIE,KAAK,CAACvB,QAAQ,CAACC,MAAM,CAACuB,eAAe,IAAI,uBAAuB,CAAC;;IAE/E,CAAC,CAAC,EACFxD,UAAU,CAACyD,KAAK,IAAG;MACjBP,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIC,YAAY,GAAG,gCAAgC;MAEnD,IAAID,KAAK,CAACA,KAAK,EAAExB,MAAM,EAAEuB,eAAe,EAAE;QACxCE,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACxB,MAAM,CAACuB,eAAe;OAClD,MAAM,IAAIC,KAAK,CAACJ,OAAO,EAAE;QACxBK,YAAY,GAAGD,KAAK,CAACJ,OAAO;OAC7B,MAAM,IAAII,KAAK,CAACE,MAAM,KAAK,GAAG,EAAE;QAC/BD,YAAY,GAAG,qBAAqB;OACrC,MAAM,IAAID,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;QAC7BD,YAAY,GAAG,6BAA6B;;MAG9C,OAAO5D,UAAU,CAAC,OAAO;QACvBoC,OAAO,EAAE,KAAK;QACdmB,OAAO,EAAEK;OACV,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAE,MAAMA,CAAA;IACJ,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACnD,sBAAsB,CAACsC,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAAClC,kBAAkB,CAACkC,IAAI,CAAC,IAAI,CAAC;EACpC;EAEA;;;EAGAc,eAAeA,CAAA;IACb,OAAO,IAAI,CAACnD,aAAa,EAAE;EAC7B;EAEA;;;EAGAI,cAAcA,CAAA;IACZ,MAAMuB,QAAQ,GAAGyB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACvD,QAAQ,CAAC;IACpD,OAAO6B,QAAQ,GAAG2B,IAAI,CAACC,KAAK,CAAC5B,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEA;;;EAGA6B,QAAQA,CAAA;IACN,OAAOJ,YAAY,CAACC,OAAO,CAAC,IAAI,CAACzD,SAAS,CAAC;EAC7C;EAEA;;;EAGQ6B,QAAQA,CAACD,KAAa;IAC5B4B,YAAY,CAACK,OAAO,CAAC,IAAI,CAAC7D,SAAS,EAAE4B,KAAK,CAAC;EAC7C;EAEA;;;EAGQyB,WAAWA,CAAA;IACjBG,YAAY,CAACM,UAAU,CAAC,IAAI,CAAC9D,SAAS,CAAC;EACzC;EAEA;;;EAGQwC,WAAWA,CAACT,QAAa;IAC/ByB,YAAY,CAACK,OAAO,CAAC,IAAI,CAAC3D,QAAQ,EAAEwD,IAAI,CAACK,SAAS,CAAChC,QAAQ,CAAC,CAAC;EAC/D;EAEA;;;EAGQuB,cAAcA,CAAA;IACpBE,YAAY,CAACM,UAAU,CAAC,IAAI,CAAC5D,QAAQ,CAAC;EACxC;EAEA;;;EAGQ8D,QAAQA,CAAA;IACd,OAAO,CAAC,CAACR,YAAY,CAACC,OAAO,CAAC,IAAI,CAACzD,SAAS,CAAC;EAC/C;EAEA;;;;;EAKQiE,cAAcA,CAAA;IACpB;IACA;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,MAAMtC,KAAK,GAAG,IAAI,CAACgC,QAAQ,EAAE;IAC7B,OAAO,IAAIxE,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUwC,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;;;uBAjLWlC,WAAW,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAX5E,WAAW;MAAA6E,OAAA,EAAX7E,WAAW,CAAA8E,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}