{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DashboardComponent_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 42);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx_r0.currentUser.name, \"\");\n  }\n}\nfunction DashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"span\", 45);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 46);\n    i0.ɵɵtext(5, \"Balance: 8500.48 TND\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 47);\n    i0.ɵɵtext(7, \"Due: -1399.52 TND\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.agencyCode, \" - Workspace for Demo\");\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n  /**\n   * Navigate to flights page\n   */\n  navigateToFlights() {\n    this.router.navigate(['/flights']);\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 79,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"brand-logo\"], [1, \"logo-text\"], [\"class\", \"dashboard-subtitle\", 4, \"ngIf\"], [1, \"header-right\"], [1, \"top-nav\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [1, \"fas\", \"fa-wrench\"], [1, \"fas\", \"fa-globe\"], [1, \"logout-button\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"dashboard-main\"], [1, \"navigation-grid\"], [1, \"row\"], [1, \"nav-card\", \"booking-queue\"], [1, \"card-icon\"], [1, \"fas\", \"fa-clipboard-list\"], [1, \"card-title\"], [1, \"nav-card\", \"commissions\"], [1, \"fas\", \"fa-percentage\"], [1, \"nav-card\", \"sub-agent\"], [1, \"fas\", \"fa-users\"], [1, \"nav-card\", \"profile\"], [1, \"fas\", \"fa-user\"], [1, \"nav-card\", \"finance\"], [1, \"fas\", \"fa-money-bill-wave\"], [1, \"nav-card\", \"agency-profile\"], [1, \"fas\", \"fa-building\"], [1, \"row\", \"featured-row\"], [1, \"nav-card\", \"featured\", \"flights\", 3, \"click\"], [1, \"fas\", \"fa-plane\"], [1, \"nav-card\", \"flight-info\"], [1, \"fas\", \"fa-info-circle\"], [1, \"nav-card\", \"passengers\"], [1, \"fas\", \"fa-user-friends\"], [1, \"nav-card\", \"credit-request\"], [1, \"fas\", \"fa-credit-card\"], [1, \"dashboard-subtitle\"], [1, \"user-info\"], [1, \"agency-info\"], [1, \"agency-id\"], [1, \"balance\"], [1, \"due-info\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n          i0.ɵɵtext(6, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, DashboardComponent_p_7_Template, 2, 1, \"p\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"nav\", 8)(10, \"a\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵtext(12, \" Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"a\", 9);\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵtext(15, \" Tools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"a\", 9);\n          i0.ɵɵelement(17, \"i\", 12);\n          i0.ɵɵtext(18, \" Languages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_19_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(20, \"i\", 14);\n          i0.ɵɵtext(21, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(22, DashboardComponent_div_22_Template, 8, 1, \"div\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"main\", 16)(24, \"div\", 17)(25, \"div\", 18)(26, \"div\", 19)(27, \"div\", 20);\n          i0.ɵɵelement(28, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 22);\n          i0.ɵɵtext(30, \"Booking Queue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 23)(32, \"div\", 20);\n          i0.ɵɵelement(33, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 22);\n          i0.ɵɵtext(35, \"Commissions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"div\", 20);\n          i0.ɵɵelement(38, \"i\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 22);\n          i0.ɵɵtext(40, \"Sub Agent\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 18)(42, \"div\", 27)(43, \"div\", 20);\n          i0.ɵɵelement(44, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\", 22);\n          i0.ɵɵtext(46, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 29)(48, \"div\", 20);\n          i0.ɵɵelement(49, \"i\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 22);\n          i0.ɵɵtext(51, \"Finance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 31)(53, \"div\", 20);\n          i0.ɵɵelement(54, \"i\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"span\", 22);\n          i0.ɵɵtext(56, \"Agency Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\", 33)(58, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_58_listener() {\n            return ctx.navigateToFlights();\n          });\n          i0.ɵɵelementStart(59, \"div\", 20);\n          i0.ɵɵelement(60, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"span\", 22);\n          i0.ɵɵtext(62, \"Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 36)(64, \"div\", 20);\n          i0.ɵɵelement(65, \"i\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"span\", 22);\n          i0.ɵɵtext(67, \"Flight Info\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(68, \"div\", 18)(69, \"div\", 38)(70, \"div\", 20);\n          i0.ɵɵelement(71, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\", 22);\n          i0.ɵɵtext(73, \"Passengers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 40)(75, \"div\", 20);\n          i0.ɵɵelement(76, \"i\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"span\", 22);\n          i0.ɵɵtext(78, \"Credit Request\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\"\\n\\n\\n\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-color: #4a63a2; \\n\\n  --primary-color-dark: #3a4f82; \\n\\n  --secondary-color: #5b9bd5; \\n\\n  --accent-color: #3cc7b7; \\n\\n  --light-bg: #f8f9fa;\\n  --dark-text: #333;\\n  --light-text: #fff;\\n  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  --transition-speed: 0.3s;\\n}\\n\\n\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  min-height: 100vh;\\n  background-color: var(--light-bg);\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-dark));\\n  color: var(--light-text);\\n  padding: 15px 20px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: bold;\\n  margin-right: 20px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 14px;\\n  opacity: 0.9;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: center;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  color: var(--light-text);\\n  text-decoration: none;\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  transition: background-color var(--transition-speed);\\n  font-size: 14px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  color: var(--light-text);\\n  padding: 6px 12px;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: all var(--transition-speed);\\n  font-size: 14px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 6px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.1);\\n  padding: 10px 20px;\\n  margin-top: 15px;\\n  border-radius: 4px;\\n  display: flex;\\n  justify-content: flex-end;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 25px;\\n  align-items: center;\\n  font-size: 14px;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .agency-id[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%] {\\n  color: var(--accent-color);\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .due-info[_ngcontent-%COMP%] {\\n  color: #ff9f43; \\n\\n}\\n\\n\\n\\n.dashboard-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n\\n\\n.navigation-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.navigation-grid[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  flex-wrap: wrap;\\n}\\n\\n.navigation-grid[_ngcontent-%COMP%]   .row.featured-row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-height: 180px;\\n}\\n\\n\\n\\n.nav-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 10px;\\n  box-shadow: var(--card-shadow);\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  min-width: 150px;\\n  flex: 1;\\n  transition: all var(--transition-speed);\\n  min-height: 140px;\\n  text-align: center;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 5px;\\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\\n  opacity: 0.7;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.nav-card[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]:hover   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  margin-bottom: 15px;\\n  color: var(--primary-color);\\n  height: 50px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform var(--transition-speed);\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--dark-text);\\n}\\n\\n\\n\\n.nav-card.booking-queue[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #4a63a2;\\n}\\n\\n.nav-card.commissions[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #5b9bd5;\\n}\\n\\n.nav-card.sub-agent[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #ff9f43;\\n}\\n\\n.nav-card.profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n\\n.nav-card.finance[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #28c76f;\\n}\\n\\n.nav-card.agency-profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #7367f0;\\n}\\n\\n.nav-card.flights[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #4a63a2;\\n}\\n\\n.nav-card.flight-info[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #5b9bd5;\\n}\\n\\n.nav-card.passengers[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #7367f0;\\n}\\n\\n.nav-card.credit-request[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: #28c76f;\\n}\\n\\n.nav-card.featured[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.18);\\n}\\n\\n.nav-card.featured[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n}\\n\\n.nav-card.featured[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .row[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  \\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: calc(33.333% - 20px);\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: calc(50% - 10px);\\n  }\\n}\\n\\n@media (max-width: 576px) {\\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\\n    min-width: 100%;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "currentUser", "name", "ctx_r1", "agencyCode", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "navigateToFlights", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_p_7_Template", "ɵɵelement", "ɵɵlistener", "DashboardComponent_Template_button_click_19_listener", "DashboardComponent_div_22_Template", "DashboardComponent_Template_div_click_58_listener", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any = null;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n\n  /**\n   * Navigate to flights page\n   */\n  navigateToFlights(): void {\n    this.router.navigate(['/flights']);\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header with Block to Book branding -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <div class=\"brand-logo\">\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <p class=\"dashboard-subtitle\" *ngIf=\"currentUser\">Welcome back, {{ currentUser.name }}</p>\n      </div>\n      <div class=\"header-right\">\n        <nav class=\"top-nav\">\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-home\"></i> Home</a>\n\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-wrench\"></i> Tools</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-globe\"></i> Languages</a>\n          <button class=\"logout-button\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i> Logout\n          </button>\n        </nav>\n      </div>\n    </div>\n    <div class=\"user-info\" *ngIf=\"currentUser\">\n      <div class=\"agency-info\">\n        <span class=\"agency-id\">{{ currentUser.agencyCode }} - Workspace for Demo</span>\n        <span class=\"balance\">Balance: 8500.48 TND</span>\n        <span class=\"due-info\">Due: -1399.52 TND</span>\n      </div>\n    </div>\n  </header>\n\n  <!-- Main content with navigation cards -->\n  <main class=\"dashboard-main\">\n    <div class=\"navigation-grid\">\n      <!-- First row of cards -->\n      <div class=\"row\">\n        <!-- Booking Queue -->\n        <div class=\"nav-card booking-queue\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-clipboard-list\"></i>\n          </div>\n          <span class=\"card-title\">Booking Queue</span>\n        </div>\n\n        <!-- Commissions -->\n        <div class=\"nav-card commissions\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-percentage\"></i>\n          </div>\n          <span class=\"card-title\">Commissions</span>\n        </div>\n\n        <!-- Sub-Agent -->\n        <div class=\"nav-card sub-agent\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-users\"></i>\n          </div>\n          <span class=\"card-title\">Sub Agent</span>\n        </div>\n      </div>\n\n      <!-- Second row of cards -->\n      <div class=\"row\">\n        <!-- Profile -->\n        <div class=\"nav-card profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user\"></i>\n          </div>\n          <span class=\"card-title\">Profile</span>\n        </div>\n\n        <!-- Finance -->\n        <div class=\"nav-card finance\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <span class=\"card-title\">Finance</span>\n        </div>\n\n        <!-- Agency Profile -->\n        <div class=\"nav-card agency-profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-building\"></i>\n          </div>\n          <span class=\"card-title\">Agency Profile</span>\n        </div>\n      </div>\n\n      <!-- Third row with larger cards -->\n      <div class=\"row featured-row\">\n        <!-- Flights -->\n        <div class=\"nav-card featured flights\" (click)=\"navigateToFlights()\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <span class=\"card-title\">Flights</span>\n        </div>\n\n        <!-- Flight Info -->\n        <div class=\"nav-card flight-info\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-info-circle\"></i>\n          </div>\n          <span class=\"card-title\">Flight Info</span>\n        </div>\n      </div>\n\n      <!-- Fourth row -->\n      <div class=\"row\">\n        <!-- Passengers -->\n        <div class=\"nav-card passengers\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user-friends\"></i>\n          </div>\n          <span class=\"card-title\">Passengers</span>\n        </div>\n\n        <!-- Credit Request -->\n        <div class=\"nav-card credit-request\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <span class=\"card-title\">Credit Request</span>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;;;;ICQQA,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAxCH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,KAAoC;;;;;IAc1FR,EAAA,CAAAC,cAAA,cAA2C;IAEfD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFvBH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAI,MAAA,CAAAF,WAAA,CAAAG,UAAA,0BAAiD;;;ADfjF,OAAM,MAAOC,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAP,WAAW,GAAQ,IAAI;EAKpB;EAEHQ,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,IAAI,CAACP,MAAM,CAACM,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAE,MAAMA,CAAA;IACJ,IAAI,CAACT,WAAW,CAACS,MAAM,EAAE;IACzB,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBAjCWT,kBAAkB,EAAAX,EAAA,CAAAuB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzB,EAAA,CAAAuB,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBhB,kBAAkB;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BlC,EAAA,CAAAC,cAAA,aAAiC;UAMCD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAoC,UAAA,IAAAC,+BAAA,eAA0F;UAC5FrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA0B;UAEOD,EAAA,CAAAsC,SAAA,aAA2B;UAACtC,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAsC,SAAA,aAA6B;UAACtC,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAsC,SAAA,aAA4B;UAACtC,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAC,cAAA,kBAAiD;UAAnBD,EAAA,CAAAuC,UAAA,mBAAAC,qDAAA;YAAA,OAASL,GAAA,CAAAb,MAAA,EAAQ;UAAA,EAAC;UAC9CtB,EAAA,CAAAsC,SAAA,aAAmC;UAACtC,EAAA,CAAAE,MAAA,gBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIfH,EAAA,CAAAoC,UAAA,KAAAK,kCAAA,kBAMM;UACRzC,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,gBAA6B;UAOnBD,EAAA,CAAAsC,SAAA,aAAqC;UACvCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI/CH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAsC,SAAA,aAAiC;UACnCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAsC,SAAA,aAA4B;UAC9BtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK7CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAsC,SAAA,aAA2B;UAC7BtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAsC,SAAA,aAAsC;UACxCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAsC,SAAA,aAA+B;UACjCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKlDH,EAAA,CAAAC,cAAA,eAA8B;UAEWD,EAAA,CAAAuC,UAAA,mBAAAG,kDAAA;YAAA,OAASP,GAAA,CAAAd,iBAAA,EAAmB;UAAA,EAAC;UAClErB,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAsC,SAAA,aAA4B;UAC9BtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAsC,SAAA,aAAkC;UACpCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK/CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAsC,SAAA,aAAmC;UACrCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI5CH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAsC,SAAA,aAAkC;UACpCtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;UAlHjBH,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAA2C,UAAA,SAAAR,GAAA,CAAA5B,WAAA,CAAiB;UAc5BP,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAA2C,UAAA,SAAAR,GAAA,CAAA5B,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}