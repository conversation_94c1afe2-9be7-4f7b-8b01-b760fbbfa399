{"ast": null, "code": "import { of } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AutocompleteService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = `${environment.apiUrl}/location`;\n    // Mock data for demonstration - in real app, this would come from API\n    this.mockLocations = [\n    // Major airports\n    {\n      id: 'JFK',\n      name: 'John F. Kennedy International Airport',\n      code: 'JFK',\n      type: 'airport',\n      country: 'United States',\n      city: 'New York',\n      airport: 'JFK',\n      displayText: 'New York (JFK) - John F. Kennedy International Airport'\n    }, {\n      id: 'LAX',\n      name: 'Los Angeles International Airport',\n      code: 'LAX',\n      type: 'airport',\n      country: 'United States',\n      city: 'Los Angeles',\n      airport: 'LAX',\n      displayText: 'Los Angeles (LAX) - Los Angeles International Airport'\n    }, {\n      id: 'LHR',\n      name: 'London Heathrow Airport',\n      code: 'LHR',\n      type: 'airport',\n      country: 'United Kingdom',\n      city: 'London',\n      airport: 'LHR',\n      displayText: 'London (LHR) - London Heathrow Airport'\n    }, {\n      id: 'CDG',\n      name: 'Charles <PERSON> Airport',\n      code: 'CDG',\n      type: 'airport',\n      country: 'France',\n      city: 'Paris',\n      airport: 'CDG',\n      displayText: 'Paris (CDG) - Charles de Gaulle Airport'\n    }, {\n      id: 'DXB',\n      name: 'Dubai International Airport',\n      code: 'DXB',\n      type: 'airport',\n      country: 'United Arab Emirates',\n      city: 'Dubai',\n      airport: 'DXB',\n      displayText: 'Dubai (DXB) - Dubai International Airport'\n    }, {\n      id: 'NRT',\n      name: 'Narita International Airport',\n      code: 'NRT',\n      type: 'airport',\n      country: 'Japan',\n      city: 'Tokyo',\n      airport: 'NRT',\n      displayText: 'Tokyo (NRT) - Narita International Airport'\n    }, {\n      id: 'SIN',\n      name: 'Singapore Changi Airport',\n      code: 'SIN',\n      type: 'airport',\n      country: 'Singapore',\n      city: 'Singapore',\n      airport: 'SIN',\n      displayText: 'Singapore (SIN) - Singapore Changi Airport'\n    }, {\n      id: 'FRA',\n      name: 'Frankfurt Airport',\n      code: 'FRA',\n      type: 'airport',\n      country: 'Germany',\n      city: 'Frankfurt',\n      airport: 'FRA',\n      displayText: 'Frankfurt (FRA) - Frankfurt Airport'\n    }, {\n      id: 'AMS',\n      name: 'Amsterdam Airport Schiphol',\n      code: 'AMS',\n      type: 'airport',\n      country: 'Netherlands',\n      city: 'Amsterdam',\n      airport: 'AMS',\n      displayText: 'Amsterdam (AMS) - Amsterdam Airport Schiphol'\n    }, {\n      id: 'IST',\n      name: 'Istanbul Airport',\n      code: 'IST',\n      type: 'airport',\n      country: 'Turkey',\n      city: 'Istanbul',\n      airport: 'IST',\n      displayText: 'Istanbul (IST) - Istanbul Airport'\n    }, {\n      id: 'TUN',\n      name: 'Tunis-Carthage International Airport',\n      code: 'TUN',\n      type: 'airport',\n      country: 'Tunisia',\n      city: 'Tunis',\n      airport: 'TUN',\n      displayText: 'Tunis (TUN) - Tunis-Carthage International Airport'\n    }, {\n      id: 'CAI',\n      name: 'Cairo International Airport',\n      code: 'CAI',\n      type: 'airport',\n      country: 'Egypt',\n      city: 'Cairo',\n      airport: 'CAI',\n      displayText: 'Cairo (CAI) - Cairo International Airport'\n    }, {\n      id: 'CMN',\n      name: 'Mohammed V International Airport',\n      code: 'CMN',\n      type: 'airport',\n      country: 'Morocco',\n      city: 'Casablanca',\n      airport: 'CMN',\n      displayText: 'Casablanca (CMN) - Mohammed V International Airport'\n    }, {\n      id: 'ALG',\n      name: 'Houari Boumediene Airport',\n      code: 'ALG',\n      type: 'airport',\n      country: 'Algeria',\n      city: 'Algiers',\n      airport: 'ALG',\n      displayText: 'Algiers (ALG) - Houari Boumediene Airport'\n    },\n    // Cities\n    {\n      id: 'NYC',\n      name: 'New York',\n      code: 'NYC',\n      type: 'city',\n      country: 'United States',\n      city: 'New York',\n      displayText: 'New York, United States'\n    }, {\n      id: 'LON',\n      name: 'London',\n      code: 'LON',\n      type: 'city',\n      country: 'United Kingdom',\n      city: 'London',\n      displayText: 'London, United Kingdom'\n    }, {\n      id: 'PAR',\n      name: 'Paris',\n      code: 'PAR',\n      type: 'city',\n      country: 'France',\n      city: 'Paris',\n      displayText: 'Paris, France'\n    }, {\n      id: 'DUB',\n      name: 'Dubai',\n      code: 'DUB',\n      type: 'city',\n      country: 'United Arab Emirates',\n      city: 'Dubai',\n      displayText: 'Dubai, United Arab Emirates'\n    }, {\n      id: 'TOK',\n      name: 'Tokyo',\n      code: 'TOK',\n      type: 'city',\n      country: 'Japan',\n      city: 'Tokyo',\n      displayText: 'Tokyo, Japan'\n    }, {\n      id: 'TUN_CITY',\n      name: 'Tunis',\n      code: 'TUN',\n      type: 'city',\n      country: 'Tunisia',\n      city: 'Tunis',\n      displayText: 'Tunis, Tunisia'\n    },\n    // Countries\n    {\n      id: 'US',\n      name: 'United States',\n      code: 'US',\n      type: 'country',\n      country: 'United States',\n      displayText: 'United States'\n    }, {\n      id: 'UK',\n      name: 'United Kingdom',\n      code: 'UK',\n      type: 'country',\n      country: 'United Kingdom',\n      displayText: 'United Kingdom'\n    }, {\n      id: 'FR',\n      name: 'France',\n      code: 'FR',\n      type: 'country',\n      country: 'France',\n      displayText: 'France'\n    }, {\n      id: 'TN',\n      name: 'Tunisia',\n      code: 'TN',\n      type: 'country',\n      country: 'Tunisia',\n      displayText: 'Tunisia'\n    }, {\n      id: 'AE',\n      name: 'United Arab Emirates',\n      code: 'AE',\n      type: 'country',\n      country: 'United Arab Emirates',\n      displayText: 'United Arab Emirates'\n    }, {\n      id: 'JP',\n      name: 'Japan',\n      code: 'JP',\n      type: 'country',\n      country: 'Japan',\n      displayText: 'Japan'\n    }];\n  }\n  /**\n   * Search locations based on query string\n   */\n  searchLocations(query) {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n    // For now, use mock data. In production, replace with actual API call\n    return of(this.mockLocations).pipe(map(locations => locations.filter(location => location.name.toLowerCase().includes(query.toLowerCase()) || location.code.toLowerCase().includes(query.toLowerCase()) || location.city && location.city.toLowerCase().includes(query.toLowerCase()) || location.country && location.country.toLowerCase().includes(query.toLowerCase())).slice(0, 10) // Limit to 10 results\n    ));\n    // Uncomment this for actual API integration:\n    /*\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return of([]);\n    }\n         const headers = new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n         return this.http.get<any>(`${this.API_URL}/search`, {\n      headers,\n      params: { query, limit: '10' }\n    }).pipe(\n      map(response => this.mapApiResponseToLocations(response)),\n      catchError(error => {\n        console.error('Location search error:', error);\n        return of([]);\n      })\n    );\n    */\n  }\n  /**\n   * Get popular destinations\n   */\n  getPopularDestinations() {\n    return of(this.mockLocations.filter(location => ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'TUN', 'IST', 'FRA'].includes(location.code)));\n  }\n  /**\n   * Map API response to AutocompleteLocation format\n   */\n  mapApiResponseToLocations(response) {\n    // This would map the actual API response to our interface\n    // Implementation depends on the actual API structure\n    return response.data?.map(item => ({\n      id: item.id,\n      name: item.name,\n      code: item.code,\n      type: item.type,\n      country: item.country?.name,\n      city: item.city?.name,\n      airport: item.airport?.name,\n      displayText: this.formatDisplayText(item)\n    })) || [];\n  }\n  /**\n   * Format display text for location\n   */\n  formatDisplayText(location) {\n    if (location.type === 'airport') {\n      return `${location.city?.name || location.name} (${location.code}) - ${location.name}`;\n    } else if (location.type === 'city') {\n      return `${location.name}, ${location.country?.name}`;\n    } else {\n      return location.name;\n    }\n  }\n  static {\n    this.ɵfac = function AutocompleteService_Factory(t) {\n      return new (t || AutocompleteService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AutocompleteService,\n      factory: AutocompleteService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "map", "environment", "AutocompleteService", "constructor", "http", "API_URL", "apiUrl", "mockLocations", "id", "name", "code", "type", "country", "city", "airport", "displayText", "searchLocations", "query", "length", "pipe", "locations", "filter", "location", "toLowerCase", "includes", "slice", "getPopularDestinations", "mapApiResponseToLocations", "response", "data", "item", "formatDisplayText", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\services\\autocomplete.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { map, debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface AutocompleteLocation {\n  id: string;\n  name: string;\n  code: string;\n  type: 'airport' | 'city' | 'country';\n  country?: string;\n  city?: string;\n  airport?: string;\n  displayText: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AutocompleteService {\n  private readonly API_URL = `${environment.apiUrl}/location`;\n  \n  // Mock data for demonstration - in real app, this would come from API\n  private mockLocations: AutocompleteLocation[] = [\n    // Major airports\n    { id: 'JFK', name: 'John F. Kennedy International Airport', code: 'JFK', type: 'airport', country: 'United States', city: 'New York', airport: 'JFK', displayText: 'New York (JFK) - John <PERSON> International Airport' },\n    { id: 'LAX', name: 'Los Angeles International Airport', code: 'LAX', type: 'airport', country: 'United States', city: 'Los Angeles', airport: 'LAX', displayText: 'Los Angeles (LAX) - Los Angeles International Airport' },\n    { id: 'LHR', name: 'London Heathrow Airport', code: 'LHR', type: 'airport', country: 'United Kingdom', city: 'London', airport: 'LHR', displayText: 'London (LHR) - London Heathrow Airport' },\n    { id: 'CDG', name: 'Charles de Gaulle Airport', code: 'CDG', type: 'airport', country: 'France', city: 'Paris', airport: 'CDG', displayText: 'Paris (CDG) - Charles de Gaulle Airport' },\n    { id: 'DXB', name: 'Dubai International Airport', code: 'DXB', type: 'airport', country: 'United Arab Emirates', city: 'Dubai', airport: 'DXB', displayText: 'Dubai (DXB) - Dubai International Airport' },\n    { id: 'NRT', name: 'Narita International Airport', code: 'NRT', type: 'airport', country: 'Japan', city: 'Tokyo', airport: 'NRT', displayText: 'Tokyo (NRT) - Narita International Airport' },\n    { id: 'SIN', name: 'Singapore Changi Airport', code: 'SIN', type: 'airport', country: 'Singapore', city: 'Singapore', airport: 'SIN', displayText: 'Singapore (SIN) - Singapore Changi Airport' },\n    { id: 'FRA', name: 'Frankfurt Airport', code: 'FRA', type: 'airport', country: 'Germany', city: 'Frankfurt', airport: 'FRA', displayText: 'Frankfurt (FRA) - Frankfurt Airport' },\n    { id: 'AMS', name: 'Amsterdam Airport Schiphol', code: 'AMS', type: 'airport', country: 'Netherlands', city: 'Amsterdam', airport: 'AMS', displayText: 'Amsterdam (AMS) - Amsterdam Airport Schiphol' },\n    { id: 'IST', name: 'Istanbul Airport', code: 'IST', type: 'airport', country: 'Turkey', city: 'Istanbul', airport: 'IST', displayText: 'Istanbul (IST) - Istanbul Airport' },\n    { id: 'TUN', name: 'Tunis-Carthage International Airport', code: 'TUN', type: 'airport', country: 'Tunisia', city: 'Tunis', airport: 'TUN', displayText: 'Tunis (TUN) - Tunis-Carthage International Airport' },\n    { id: 'CAI', name: 'Cairo International Airport', code: 'CAI', type: 'airport', country: 'Egypt', city: 'Cairo', airport: 'CAI', displayText: 'Cairo (CAI) - Cairo International Airport' },\n    { id: 'CMN', name: 'Mohammed V International Airport', code: 'CMN', type: 'airport', country: 'Morocco', city: 'Casablanca', airport: 'CMN', displayText: 'Casablanca (CMN) - Mohammed V International Airport' },\n    { id: 'ALG', name: 'Houari Boumediene Airport', code: 'ALG', type: 'airport', country: 'Algeria', city: 'Algiers', airport: 'ALG', displayText: 'Algiers (ALG) - Houari Boumediene Airport' },\n    \n    // Cities\n    { id: 'NYC', name: 'New York', code: 'NYC', type: 'city', country: 'United States', city: 'New York', displayText: 'New York, United States' },\n    { id: 'LON', name: 'London', code: 'LON', type: 'city', country: 'United Kingdom', city: 'London', displayText: 'London, United Kingdom' },\n    { id: 'PAR', name: 'Paris', code: 'PAR', type: 'city', country: 'France', city: 'Paris', displayText: 'Paris, France' },\n    { id: 'DUB', name: 'Dubai', code: 'DUB', type: 'city', country: 'United Arab Emirates', city: 'Dubai', displayText: 'Dubai, United Arab Emirates' },\n    { id: 'TOK', name: 'Tokyo', code: 'TOK', type: 'city', country: 'Japan', city: 'Tokyo', displayText: 'Tokyo, Japan' },\n    { id: 'TUN_CITY', name: 'Tunis', code: 'TUN', type: 'city', country: 'Tunisia', city: 'Tunis', displayText: 'Tunis, Tunisia' },\n    \n    // Countries\n    { id: 'US', name: 'United States', code: 'US', type: 'country', country: 'United States', displayText: 'United States' },\n    { id: 'UK', name: 'United Kingdom', code: 'UK', type: 'country', country: 'United Kingdom', displayText: 'United Kingdom' },\n    { id: 'FR', name: 'France', code: 'FR', type: 'country', country: 'France', displayText: 'France' },\n    { id: 'TN', name: 'Tunisia', code: 'TN', type: 'country', country: 'Tunisia', displayText: 'Tunisia' },\n    { id: 'AE', name: 'United Arab Emirates', code: 'AE', type: 'country', country: 'United Arab Emirates', displayText: 'United Arab Emirates' },\n    { id: 'JP', name: 'Japan', code: 'JP', type: 'country', country: 'Japan', displayText: 'Japan' }\n  ];\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Search locations based on query string\n   */\n  searchLocations(query: string): Observable<AutocompleteLocation[]> {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n\n    // For now, use mock data. In production, replace with actual API call\n    return of(this.mockLocations).pipe(\n      map(locations => \n        locations.filter(location => \n          location.name.toLowerCase().includes(query.toLowerCase()) ||\n          location.code.toLowerCase().includes(query.toLowerCase()) ||\n          (location.city && location.city.toLowerCase().includes(query.toLowerCase())) ||\n          (location.country && location.country.toLowerCase().includes(query.toLowerCase()))\n        ).slice(0, 10) // Limit to 10 results\n      )\n    );\n\n    // Uncomment this for actual API integration:\n    /*\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return of([]);\n    }\n\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n\n    return this.http.get<any>(`${this.API_URL}/search`, {\n      headers,\n      params: { query, limit: '10' }\n    }).pipe(\n      map(response => this.mapApiResponseToLocations(response)),\n      catchError(error => {\n        console.error('Location search error:', error);\n        return of([]);\n      })\n    );\n    */\n  }\n\n  /**\n   * Get popular destinations\n   */\n  getPopularDestinations(): Observable<AutocompleteLocation[]> {\n    return of(this.mockLocations.filter(location => \n      ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'TUN', 'IST', 'FRA'].includes(location.code)\n    ));\n  }\n\n  /**\n   * Map API response to AutocompleteLocation format\n   */\n  private mapApiResponseToLocations(response: any): AutocompleteLocation[] {\n    // This would map the actual API response to our interface\n    // Implementation depends on the actual API structure\n    return response.data?.map((item: any) => ({\n      id: item.id,\n      name: item.name,\n      code: item.code,\n      type: item.type,\n      country: item.country?.name,\n      city: item.city?.name,\n      airport: item.airport?.name,\n      displayText: this.formatDisplayText(item)\n    })) || [];\n  }\n\n  /**\n   * Format display text for location\n   */\n  private formatDisplayText(location: any): string {\n    if (location.type === 'airport') {\n      return `${location.city?.name || location.name} (${location.code}) - ${location.name}`;\n    } else if (location.type === 'city') {\n      return `${location.name}, ${location.country?.name}`;\n    } else {\n      return location.name;\n    }\n  }\n}\n"], "mappings": "AAEA,SAAqBA,EAAE,QAAyB,MAAM;AACtD,SAASC,GAAG,QAAmE,gBAAgB;AAC/F,SAASC,WAAW,QAAQ,gCAAgC;;;AAgB5D,OAAM,MAAOC,mBAAmB;EAsC9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IArCP,KAAAC,OAAO,GAAG,GAAGJ,WAAW,CAACK,MAAM,WAAW;IAE3D;IACQ,KAAAC,aAAa,GAA2B;IAC9C;IACA;MAAEC,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,uCAAuC;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,eAAe;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAwD,CAAE,EAC7N;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,mCAAmC;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,eAAe;MAAEC,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAuD,CAAE,EAC3N;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,yBAAyB;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,gBAAgB;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAwC,CAAE,EAC9L;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,QAAQ;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAyC,CAAE,EACxL;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,sBAAsB;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA2C,CAAE,EAC1M;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,8BAA8B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,OAAO;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA4C,CAAE,EAC7L;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA4C,CAAE,EACjM;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAqC,CAAE,EACjL;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,4BAA4B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,aAAa;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA8C,CAAE,EACvM;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,QAAQ;MAAEC,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAmC,CAAE,EAC5K;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,sCAAsC;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAoD,CAAE,EAC/M;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,OAAO;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA2C,CAAE,EAC3L;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,kCAAkC;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAAqD,CAAE,EACjN;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,2BAA2B;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,KAAK;MAAEC,WAAW,EAAE;IAA2C,CAAE;IAE7L;IACA;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,eAAe;MAAEC,IAAI,EAAE,UAAU;MAAEE,WAAW,EAAE;IAAyB,CAAE,EAC9I;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,gBAAgB;MAAEC,IAAI,EAAE,QAAQ;MAAEE,WAAW,EAAE;IAAwB,CAAE,EAC1I;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,QAAQ;MAAEC,IAAI,EAAE,OAAO;MAAEE,WAAW,EAAE;IAAe,CAAE,EACvH;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,sBAAsB;MAAEC,IAAI,EAAE,OAAO;MAAEE,WAAW,EAAE;IAA6B,CAAE,EACnJ;MAAEP,EAAE,EAAE,KAAK;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,OAAO;MAAEC,IAAI,EAAE,OAAO;MAAEE,WAAW,EAAE;IAAc,CAAE,EACrH;MAAEP,EAAE,EAAE,UAAU;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE,SAAS;MAAEC,IAAI,EAAE,OAAO;MAAEE,WAAW,EAAE;IAAgB,CAAE;IAE9H;IACA;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,eAAe;MAAEG,WAAW,EAAE;IAAe,CAAE,EACxH;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,gBAAgB;MAAEG,WAAW,EAAE;IAAgB,CAAE,EAC3H;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,QAAQ;MAAEG,WAAW,EAAE;IAAQ,CAAE,EACnG;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,SAAS;MAAEG,WAAW,EAAE;IAAS,CAAE,EACtG;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,sBAAsB;MAAEG,WAAW,EAAE;IAAsB,CAAE,EAC7I;MAAEP,EAAE,EAAE,IAAI;MAAEC,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,OAAO;MAAEG,WAAW,EAAE;IAAO,CAAE,CACjG;EAEsC;EAEvC;;;EAGAC,eAAeA,CAACC,KAAa;IAC3B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MAC9B,OAAOnB,EAAE,CAAC,EAAE,CAAC;;IAGf;IACA,OAAOA,EAAE,CAAC,IAAI,CAACQ,aAAa,CAAC,CAACY,IAAI,CAChCnB,GAAG,CAACoB,SAAS,IACXA,SAAS,CAACC,MAAM,CAACC,QAAQ,IACvBA,QAAQ,CAACb,IAAI,CAACc,WAAW,EAAE,CAACC,QAAQ,CAACP,KAAK,CAACM,WAAW,EAAE,CAAC,IACzDD,QAAQ,CAACZ,IAAI,CAACa,WAAW,EAAE,CAACC,QAAQ,CAACP,KAAK,CAACM,WAAW,EAAE,CAAC,IACxDD,QAAQ,CAACT,IAAI,IAAIS,QAAQ,CAACT,IAAI,CAACU,WAAW,EAAE,CAACC,QAAQ,CAACP,KAAK,CAACM,WAAW,EAAE,CAAE,IAC3ED,QAAQ,CAACV,OAAO,IAAIU,QAAQ,CAACV,OAAO,CAACW,WAAW,EAAE,CAACC,QAAQ,CAACP,KAAK,CAACM,WAAW,EAAE,CAAE,CACnF,CAACE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KAChB,CACF;IAED;IACA;;;;;;;;;;;;;;;;;;;EAqBF;EAEA;;;EAGAC,sBAAsBA,CAAA;IACpB,OAAO3B,EAAE,CAAC,IAAI,CAACQ,aAAa,CAACc,MAAM,CAACC,QAAQ,IAC1C,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACE,QAAQ,CAACF,QAAQ,CAACZ,IAAI,CAAC,CACjF,CAAC;EACJ;EAEA;;;EAGQiB,yBAAyBA,CAACC,QAAa;IAC7C;IACA;IACA,OAAOA,QAAQ,CAACC,IAAI,EAAE7B,GAAG,CAAE8B,IAAS,KAAM;MACxCtB,EAAE,EAAEsB,IAAI,CAACtB,EAAE;MACXC,IAAI,EAAEqB,IAAI,CAACrB,IAAI;MACfC,IAAI,EAAEoB,IAAI,CAACpB,IAAI;MACfC,IAAI,EAAEmB,IAAI,CAACnB,IAAI;MACfC,OAAO,EAAEkB,IAAI,CAAClB,OAAO,EAAEH,IAAI;MAC3BI,IAAI,EAAEiB,IAAI,CAACjB,IAAI,EAAEJ,IAAI;MACrBK,OAAO,EAAEgB,IAAI,CAAChB,OAAO,EAAEL,IAAI;MAC3BM,WAAW,EAAE,IAAI,CAACgB,iBAAiB,CAACD,IAAI;KACzC,CAAC,CAAC,IAAI,EAAE;EACX;EAEA;;;EAGQC,iBAAiBA,CAACT,QAAa;IACrC,IAAIA,QAAQ,CAACX,IAAI,KAAK,SAAS,EAAE;MAC/B,OAAO,GAAGW,QAAQ,CAACT,IAAI,EAAEJ,IAAI,IAAIa,QAAQ,CAACb,IAAI,KAAKa,QAAQ,CAACZ,IAAI,OAAOY,QAAQ,CAACb,IAAI,EAAE;KACvF,MAAM,IAAIa,QAAQ,CAACX,IAAI,KAAK,MAAM,EAAE;MACnC,OAAO,GAAGW,QAAQ,CAACb,IAAI,KAAKa,QAAQ,CAACV,OAAO,EAAEH,IAAI,EAAE;KACrD,MAAM;MACL,OAAOa,QAAQ,CAACb,IAAI;;EAExB;;;uBA1HWP,mBAAmB,EAAA8B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBjC,mBAAmB;MAAAkC,OAAA,EAAnBlC,mBAAmB,CAAAmC,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}