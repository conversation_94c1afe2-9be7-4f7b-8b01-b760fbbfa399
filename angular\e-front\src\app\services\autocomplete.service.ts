import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface AutocompleteLocation {
  id: string;
  name: string;
  code: string;
  type: 'airport' | 'city' | 'country';
  country?: string;
  city?: string;
  airport?: string;
  displayText: string;
}

@Injectable({
  providedIn: 'root'
})
export class AutocompleteService {
  private readonly API_URL = `${environment.apiUrl}/location`;
  
  // Mock data for demonstration - in real app, this would come from API
  private mockLocations: AutocompleteLocation[] = [
    // Major airports
    { id: 'JFK', name: 'John F. Kennedy International Airport', code: 'JFK', type: 'airport', country: 'United States', city: 'New York', airport: 'JFK', displayText: 'New York (JFK) - John <PERSON> International Airport' },
    { id: 'LAX', name: 'Los Angeles International Airport', code: 'LAX', type: 'airport', country: 'United States', city: 'Los Angeles', airport: 'LAX', displayText: 'Los Angeles (LAX) - Los Angeles International Airport' },
    { id: 'LHR', name: 'London Heathrow Airport', code: 'LHR', type: 'airport', country: 'United Kingdom', city: 'London', airport: 'LHR', displayText: 'London (LHR) - London Heathrow Airport' },
    { id: 'CDG', name: 'Charles de Gaulle Airport', code: 'CDG', type: 'airport', country: 'France', city: 'Paris', airport: 'CDG', displayText: 'Paris (CDG) - Charles de Gaulle Airport' },
    { id: 'DXB', name: 'Dubai International Airport', code: 'DXB', type: 'airport', country: 'United Arab Emirates', city: 'Dubai', airport: 'DXB', displayText: 'Dubai (DXB) - Dubai International Airport' },
    { id: 'NRT', name: 'Narita International Airport', code: 'NRT', type: 'airport', country: 'Japan', city: 'Tokyo', airport: 'NRT', displayText: 'Tokyo (NRT) - Narita International Airport' },
    { id: 'SIN', name: 'Singapore Changi Airport', code: 'SIN', type: 'airport', country: 'Singapore', city: 'Singapore', airport: 'SIN', displayText: 'Singapore (SIN) - Singapore Changi Airport' },
    { id: 'FRA', name: 'Frankfurt Airport', code: 'FRA', type: 'airport', country: 'Germany', city: 'Frankfurt', airport: 'FRA', displayText: 'Frankfurt (FRA) - Frankfurt Airport' },
    { id: 'AMS', name: 'Amsterdam Airport Schiphol', code: 'AMS', type: 'airport', country: 'Netherlands', city: 'Amsterdam', airport: 'AMS', displayText: 'Amsterdam (AMS) - Amsterdam Airport Schiphol' },
    { id: 'IST', name: 'Istanbul Airport', code: 'IST', type: 'airport', country: 'Turkey', city: 'Istanbul', airport: 'IST', displayText: 'Istanbul (IST) - Istanbul Airport' },
    { id: 'TUN', name: 'Tunis-Carthage International Airport', code: 'TUN', type: 'airport', country: 'Tunisia', city: 'Tunis', airport: 'TUN', displayText: 'Tunis (TUN) - Tunis-Carthage International Airport' },
    { id: 'CAI', name: 'Cairo International Airport', code: 'CAI', type: 'airport', country: 'Egypt', city: 'Cairo', airport: 'CAI', displayText: 'Cairo (CAI) - Cairo International Airport' },
    { id: 'CMN', name: 'Mohammed V International Airport', code: 'CMN', type: 'airport', country: 'Morocco', city: 'Casablanca', airport: 'CMN', displayText: 'Casablanca (CMN) - Mohammed V International Airport' },
    { id: 'ALG', name: 'Houari Boumediene Airport', code: 'ALG', type: 'airport', country: 'Algeria', city: 'Algiers', airport: 'ALG', displayText: 'Algiers (ALG) - Houari Boumediene Airport' },
    
    // Cities
    { id: 'NYC', name: 'New York', code: 'NYC', type: 'city', country: 'United States', city: 'New York', displayText: 'New York, United States' },
    { id: 'LON', name: 'London', code: 'LON', type: 'city', country: 'United Kingdom', city: 'London', displayText: 'London, United Kingdom' },
    { id: 'PAR', name: 'Paris', code: 'PAR', type: 'city', country: 'France', city: 'Paris', displayText: 'Paris, France' },
    { id: 'DUB', name: 'Dubai', code: 'DUB', type: 'city', country: 'United Arab Emirates', city: 'Dubai', displayText: 'Dubai, United Arab Emirates' },
    { id: 'TOK', name: 'Tokyo', code: 'TOK', type: 'city', country: 'Japan', city: 'Tokyo', displayText: 'Tokyo, Japan' },
    { id: 'TUN_CITY', name: 'Tunis', code: 'TUN', type: 'city', country: 'Tunisia', city: 'Tunis', displayText: 'Tunis, Tunisia' },
    
    // Countries
    { id: 'US', name: 'United States', code: 'US', type: 'country', country: 'United States', displayText: 'United States' },
    { id: 'UK', name: 'United Kingdom', code: 'UK', type: 'country', country: 'United Kingdom', displayText: 'United Kingdom' },
    { id: 'FR', name: 'France', code: 'FR', type: 'country', country: 'France', displayText: 'France' },
    { id: 'TN', name: 'Tunisia', code: 'TN', type: 'country', country: 'Tunisia', displayText: 'Tunisia' },
    { id: 'AE', name: 'United Arab Emirates', code: 'AE', type: 'country', country: 'United Arab Emirates', displayText: 'United Arab Emirates' },
    { id: 'JP', name: 'Japan', code: 'JP', type: 'country', country: 'Japan', displayText: 'Japan' }
  ];

  constructor(private http: HttpClient) {}

  /**
   * Search locations based on query string
   */
  searchLocations(query: string): Observable<AutocompleteLocation[]> {
    if (!query || query.length < 2) {
      return of([]);
    }

    // For now, use mock data. In production, replace with actual API call
    return of(this.mockLocations).pipe(
      map(locations => 
        locations.filter(location => 
          location.name.toLowerCase().includes(query.toLowerCase()) ||
          location.code.toLowerCase().includes(query.toLowerCase()) ||
          (location.city && location.city.toLowerCase().includes(query.toLowerCase())) ||
          (location.country && location.country.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 10) // Limit to 10 results
      )
    );

    // Uncomment this for actual API integration:
    /*
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return of([]);
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    return this.http.get<any>(`${this.API_URL}/search`, {
      headers,
      params: { query, limit: '10' }
    }).pipe(
      map(response => this.mapApiResponseToLocations(response)),
      catchError(error => {
        console.error('Location search error:', error);
        return of([]);
      })
    );
    */
  }

  /**
   * Get popular destinations
   */
  getPopularDestinations(): Observable<AutocompleteLocation[]> {
    return of(this.mockLocations.filter(location => 
      ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'TUN', 'IST', 'FRA'].includes(location.code)
    ));
  }

  /**
   * Map API response to AutocompleteLocation format
   */
  private mapApiResponseToLocations(response: any): AutocompleteLocation[] {
    // This would map the actual API response to our interface
    // Implementation depends on the actual API structure
    return response.data?.map((item: any) => ({
      id: item.id,
      name: item.name,
      code: item.code,
      type: item.type,
      country: item.country?.name,
      city: item.city?.name,
      airport: item.airport?.name,
      displayText: this.formatDisplayText(item)
    })) || [];
  }

  /**
   * Format display text for location
   */
  private formatDisplayText(location: any): string {
    if (location.type === 'airport') {
      return `${location.city?.name || location.name} (${location.code}) - ${location.name}`;
    } else if (location.type === 'city') {
      return `${location.name}, ${location.country?.name}`;
    } else {
      return location.name;
    }
  }
}
