/* Modern Dashboard Styles - Block to Book */

/* CSS Custom Properties (Variables) */
:root {
  --primary-color: #4a63a2; /* Blue similar to Block to Book */
  --primary-color-dark: #3a4f82; /* Darker version of primary */
  --secondary-color: #5b9bd5; /* Lighter blue */
  --accent-color: #3cc7b7; /* Teal accent */
  --light-bg: #f8f9fa;
  --dark-text: #333;
  --light-text: #fff;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --transition-speed: 0.3s;
}

/* Base styles */
.dashboard-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--light-bg);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header styles */
.dashboard-header {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-dark));
  color: var(--light-text);
  padding: 15px 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dashboard-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.dashboard-header .header-left {
  display: flex;
  align-items: center;
}

.dashboard-header .brand-logo {
  display: flex;
  align-items: center;
  font-weight: bold;
  margin-right: 20px;
}

.dashboard-header .brand-logo .logo-text {
  font-size: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dashboard-header .dashboard-subtitle {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.dashboard-header .top-nav {
  display: flex;
  gap: 20px;
  align-items: center;
}

.dashboard-header .top-nav .nav-link {
  color: var(--light-text);
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 4px;
  transition: background-color var(--transition-speed);
  font-size: 14px;
}

.dashboard-header .top-nav .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-header .top-nav .nav-link i {
  margin-right: 6px;
}

.dashboard-header .logout-button {
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--light-text);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all var(--transition-speed);
  font-size: 14px;
}

.dashboard-header .logout-button i {
  margin-right: 6px;
}

.dashboard-header .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dashboard-header .user-info {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
  margin-top: 15px;
  border-radius: 4px;
  display: flex;
  justify-content: flex-end;
}

.dashboard-header .user-info .agency-info {
  display: flex;
  gap: 25px;
  align-items: center;
  font-size: 14px;
}

.dashboard-header .user-info .agency-info .agency-id {
  font-weight: 500;
}

.dashboard-header .user-info .agency-info .balance {
  color: var(--accent-color);
}

.dashboard-header .user-info .agency-info .due-info {
  color: #ff9f43; /* Orange for dues */
}

/* Main content styles */
.dashboard-main {
  flex: 1;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* Navigation grid styles */
.navigation-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.navigation-grid .row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.navigation-grid .row.featured-row .nav-card {
  flex: 1;
  min-height: 180px;
}

/* Card styles */
.nav-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 150px;
  flex: 1;
  transition: all var(--transition-speed);
  min-height: 140px;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  opacity: 0.7;
}

.nav-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.nav-card:hover::before {
  opacity: 1;
}

.nav-card:hover .card-icon i {
  transform: scale(1.1);
}

.nav-card .card-icon {
  font-size: 36px;
  margin-bottom: 15px;
  color: var(--primary-color);
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-card .card-icon i {
  transition: transform var(--transition-speed);
}

.nav-card .card-title {
  font-weight: 500;
  color: var(--dark-text);
}

/* Specific card styling */
.nav-card.booking-queue .card-icon {
  color: #4a63a2;
}

.nav-card.commissions .card-icon {
  color: #5b9bd5;
}

.nav-card.sub-agent .card-icon {
  color: #ff9f43;
}

.nav-card.profile .card-icon {
  color: #6c757d;
}

.nav-card.finance .card-icon {
  color: #28c76f;
}

.nav-card.agency-profile .card-icon {
  color: #7367f0;
}

.nav-card.flights .card-icon {
  color: #4a63a2;
}

.nav-card.flight-info .card-icon {
  color: #5b9bd5;
}

.nav-card.passengers .card-icon {
  color: #7367f0;
}

.nav-card.credit-request .card-icon {
  color: #28c76f;
}

.nav-card.featured {
  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.nav-card.featured .card-icon {
  font-size: 48px;
}

.nav-card.featured .card-title {
  font-size: 18px;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .row {
    flex-wrap: wrap;
  }
  
  .row .nav-card {
    min-width: calc(33.333% - 20px);
  }
}

@media (max-width: 768px) {
  .dashboard-header .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .dashboard-header .top-nav {
    flex-wrap: wrap;
  }

  .dashboard-header .user-info .agency-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .row .nav-card {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 576px) {
  .row .nav-card {
    min-width: 100%;
  }
}
