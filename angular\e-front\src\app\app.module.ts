import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { SigninComponent } from './components/signin/signin.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { FlightComponent } from './components/flight/flight.component';
import { FlightResultsComponent } from './components/flight-results/flight-results.component';
import { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';
// import { TokenExpiryAlertComponent } from './components/shared/token-expiry-alert/token-expiry-alert.component';
import { AuthInterceptor } from './interceptors/auth.interceptor';

@NgModule({
  declarations: [
    AppComponent,
    SigninComponent,
    DashboardComponent,
    FlightComponent,
    FlightResultsComponent,
    AutocompleteComponent
    // TokenExpiryAlertComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    HttpClientModule
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
