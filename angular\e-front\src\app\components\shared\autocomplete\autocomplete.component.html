<div class="autocomplete-wrapper">
  <div class="input-wrapper">
    <i *ngIf="icon" [class]="icon" class="input-icon"></i>
    <input
      #inputElement
      type="text"
      [placeholder]="placeholder"
      [readonly]="readonly"
      class="autocomplete-input"
      [class.with-icon]="icon"
      [class.readonly]="readonly"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (keydown)="onKeyDown($event)"
      autocomplete="off">
    <div *ngIf="isLoading" class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
    </div>
  </div>

  <div *ngIf="showSuggestions" class="suggestions-dropdown">
    <div class="suggestions-list">
      <div
        *ngFor="let suggestion of suggestions; let i = index"
        class="suggestion-item"
        [class.selected]="i === selectedIndex"
        (click)="selectSuggestion(suggestion)"
        (mouseenter)="selectedIndex = i">
        
        <div class="suggestion-icon">
          <i [class]="getLocationTypeIcon(suggestion.type)"></i>
        </div>
        
        <div class="suggestion-content">
          <div class="suggestion-main">{{ suggestion.displayText }}</div>
          <div *ngIf="suggestion.type === 'airport' && suggestion.country" class="suggestion-details">
            {{ suggestion.country }}
          </div>
        </div>
        
        <div class="suggestion-type">
          <span class="type-badge" [class]="'type-' + suggestion.type">
            {{ suggestion.type }}
          </span>
        </div>
      </div>
    </div>
    
    <div *ngIf="suggestions.length === 0 && !isLoading" class="no-results">
      <i class="fas fa-search"></i>
      <span>No locations found</span>
    </div>
  </div>
</div>
