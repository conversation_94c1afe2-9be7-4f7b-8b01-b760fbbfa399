.token-expiry-alert {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-left: 4px solid #f39c12;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.alert-content {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  gap: 15px;
}

.alert-icon {
  color: #f39c12;
  font-size: 24px;
  margin-top: 2px;
}

.alert-message {
  flex: 1;
}

.alert-message h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.alert-message p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.alert-message p:last-child {
  margin-bottom: 15px;
}

.alert-actions {
  display: flex;
  gap: 8px;
  margin-top: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover {
  background: #357abd;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn i {
  font-size: 11px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .token-expiry-alert {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .alert-content {
    padding: 15px;
    gap: 12px;
  }
  
  .alert-actions {
    flex-direction: column;
  }
  
  .btn {
    justify-content: center;
  }
}
