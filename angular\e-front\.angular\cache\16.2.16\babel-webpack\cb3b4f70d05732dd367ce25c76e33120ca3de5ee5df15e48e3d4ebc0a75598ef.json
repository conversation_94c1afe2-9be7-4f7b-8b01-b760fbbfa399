{"ast": null, "code": "import { EventEmitter, forwardRef } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/autocomplete.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"inputElement\"];\nfunction AutocompleteComponent_i_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.icon);\n  }\n}\nfunction AutocompleteComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵelement(1, \"i\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AutocompleteComponent_div_6_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const suggestion_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r6.country, \" \");\n  }\n}\nfunction AutocompleteComponent_div_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function AutocompleteComponent_div_6_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const suggestion_r6 = restoredCtx.$implicit;\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.selectSuggestion(suggestion_r6));\n    })(\"mouseenter\", function AutocompleteComponent_div_6_div_2_Template_div_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r11);\n      const i_r7 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.selectedIndex = i_r7);\n    });\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"div\", 17);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, AutocompleteComponent_div_6_div_2_div_6_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 19)(8, \"span\", 20);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const suggestion_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", i_r7 === ctx_r4.selectedIndex);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getLocationTypeIcon(suggestion_r6.type));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(suggestion_r6.displayText);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", suggestion_r6.type === \"airport\" && suggestion_r6.country);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"type-\" + suggestion_r6.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", suggestion_r6.type, \" \");\n  }\n}\nfunction AutocompleteComponent_div_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵelement(1, \"i\", 23);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"No locations found\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutocompleteComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, AutocompleteComponent_div_6_div_2_Template, 10, 9, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AutocompleteComponent_div_6_div_3_Template, 4, 0, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.suggestions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.suggestions.length === 0 && !ctx_r3.isLoading);\n  }\n}\nexport class AutocompleteComponent {\n  constructor(autocompleteService, elementRef) {\n    this.autocompleteService = autocompleteService;\n    this.elementRef = elementRef;\n    this.placeholder = '';\n    this.icon = '';\n    this.readonly = false;\n    this.locationSelected = new EventEmitter();\n    this.value = '';\n    this.suggestions = [];\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.isLoading = false;\n    this.searchSubject = new Subject();\n    this.destroy$ = new Subject();\n    this.onChange = value => {};\n    this.onTouched = () => {};\n  }\n  ngOnInit() {\n    // Setup search with debouncing\n    this.searchSubject.pipe(debounceTime(300), distinctUntilChanged(), switchMap(query => this.autocompleteService.searchLocations(query)), takeUntil(this.destroy$)).subscribe(suggestions => {\n      this.suggestions = suggestions;\n      this.showSuggestions = suggestions.length > 0;\n      this.isLoading = false;\n    });\n    // Close suggestions when clicking outside\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n  // ControlValueAccessor implementation\n  writeValue(value) {\n    this.value = value || '';\n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    if (this.inputElement) {\n      this.inputElement.nativeElement.disabled = isDisabled;\n    }\n  }\n  onInput(event) {\n    const target = event.target;\n    this.value = target.value;\n    this.onChange(this.value);\n    if (this.value.length >= 2) {\n      this.isLoading = true;\n      this.searchSubject.next(this.value);\n    } else {\n      this.suggestions = [];\n      this.showSuggestions = false;\n      this.isLoading = false;\n    }\n    this.selectedIndex = -1;\n  }\n  onFocus() {\n    this.onTouched();\n    if (this.value.length >= 2) {\n      this.showSuggestions = this.suggestions.length > 0;\n    } else if (this.value.length === 0) {\n      // Show popular destinations when focusing on empty input\n      this.autocompleteService.getPopularDestinations().subscribe(destinations => {\n        this.suggestions = destinations;\n        this.showSuggestions = true;\n      });\n    }\n  }\n  onKeyDown(event) {\n    if (!this.showSuggestions) return;\n    switch (event.key) {\n      case 'ArrowDown':\n        event.preventDefault();\n        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);\n        break;\n      case 'Enter':\n        event.preventDefault();\n        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {\n          this.selectSuggestion(this.suggestions[this.selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        this.hideSuggestions();\n        break;\n    }\n  }\n  selectSuggestion(location) {\n    this.value = location.displayText;\n    this.onChange(this.value);\n    this.locationSelected.emit(location);\n    this.hideSuggestions();\n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n  hideSuggestions() {\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n  }\n  onDocumentClick(event) {\n    if (!this.elementRef.nativeElement.contains(event.target)) {\n      this.hideSuggestions();\n    }\n  }\n  getLocationTypeIcon(type) {\n    switch (type) {\n      case 'airport':\n        return 'fas fa-plane';\n      case 'city':\n        return 'fas fa-city';\n      case 'country':\n        return 'fas fa-flag';\n      default:\n        return 'fas fa-map-marker-alt';\n    }\n  }\n  static {\n    this.ɵfac = function AutocompleteComponent_Factory(t) {\n      return new (t || AutocompleteComponent)(i0.ɵɵdirectiveInject(i1.AutocompleteService), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AutocompleteComponent,\n      selectors: [[\"app-autocomplete\"]],\n      viewQuery: function AutocompleteComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        icon: \"icon\",\n        readonly: \"readonly\"\n      },\n      outputs: {\n        locationSelected: \"locationSelected\"\n      },\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => AutocompleteComponent),\n        multi: true\n      }])],\n      decls: 7,\n      vars: 9,\n      consts: [[1, \"autocomplete-wrapper\"], [1, \"input-wrapper\"], [\"class\", \"input-icon\", 3, \"class\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", 1, \"autocomplete-input\", 3, \"placeholder\", \"readonly\", \"input\", \"focus\", \"keydown\"], [\"inputElement\", \"\"], [\"class\", \"loading-spinner\", 4, \"ngIf\"], [\"class\", \"suggestions-dropdown\", 4, \"ngIf\"], [1, \"input-icon\"], [1, \"loading-spinner\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"suggestions-dropdown\"], [1, \"suggestions-list\"], [\"class\", \"suggestion-item\", 3, \"selected\", \"click\", \"mouseenter\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"suggestion-item\", 3, \"click\", \"mouseenter\"], [1, \"suggestion-icon\"], [1, \"suggestion-content\"], [1, \"suggestion-main\"], [\"class\", \"suggestion-details\", 4, \"ngIf\"], [1, \"suggestion-type\"], [1, \"type-badge\"], [1, \"suggestion-details\"], [1, \"no-results\"], [1, \"fas\", \"fa-search\"]],\n      template: function AutocompleteComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, AutocompleteComponent_i_2_Template, 1, 2, \"i\", 2);\n          i0.ɵɵelementStart(3, \"input\", 3, 4);\n          i0.ɵɵlistener(\"input\", function AutocompleteComponent_Template_input_input_3_listener($event) {\n            return ctx.onInput($event);\n          })(\"focus\", function AutocompleteComponent_Template_input_focus_3_listener() {\n            return ctx.onFocus();\n          })(\"keydown\", function AutocompleteComponent_Template_input_keydown_3_listener($event) {\n            return ctx.onKeyDown($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, AutocompleteComponent_div_5_Template, 2, 0, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, AutocompleteComponent_div_6_Template, 4, 2, \"div\", 6);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"with-icon\", ctx.icon)(\"readonly\", ctx.readonly);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeholder)(\"readonly\", ctx.readonly);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSuggestions);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf],\n      styles: [\".autocomplete-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  color: #a0aec0;\\n  z-index: 2;\\n  font-size: 14px;\\n}\\n\\n.autocomplete-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 45px 15px 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n  background: white;\\n  outline: none;\\n}\\n\\n.autocomplete-input.with-icon[_ngcontent-%COMP%] {\\n  padding-left: 45px;\\n}\\n\\n.autocomplete-input.readonly[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.autocomplete-input[_ngcontent-%COMP%]:focus {\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  color: #a0aec0;\\n  font-size: 14px;\\n}\\n\\n.suggestions-dropdown[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  background: white;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  z-index: 1000;\\n  max-height: 300px;\\n  overflow-y: auto;\\n  margin-top: 4px;\\n}\\n\\n.suggestions-list[_ngcontent-%COMP%] {\\n  padding: 8px 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 12px 16px;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  border-bottom: 1px solid #f7fafc;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover, .suggestion-item.selected[_ngcontent-%COMP%] {\\n  background-color: #f7fafc;\\n}\\n\\n.suggestion-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: #e2e8f0;\\n  border-radius: 50%;\\n  margin-right: 12px;\\n  flex-shrink: 0;\\n}\\n\\n.suggestion-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 14px;\\n}\\n\\n.suggestion-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 0;\\n}\\n\\n.suggestion-main[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #2d3748;\\n  line-height: 1.4;\\n  white-space: nowrap;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n}\\n\\n.suggestion-details[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #718096;\\n  margin-top: 2px;\\n}\\n\\n.suggestion-type[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  flex-shrink: 0;\\n}\\n\\n.type-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.type-badge.type-airport[_ngcontent-%COMP%] {\\n  background: #e6fffa;\\n  color: #234e52;\\n}\\n\\n.type-badge.type-city[_ngcontent-%COMP%] {\\n  background: #fef5e7;\\n  color: #744210;\\n}\\n\\n.type-badge.type-country[_ngcontent-%COMP%] {\\n  background: #f0f4ff;\\n  color: #3c4fe0;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 20px;\\n  color: #a0aec0;\\n  font-size: 14px;\\n  gap: 8px;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n\\n\\n\\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 3px;\\n}\\n\\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #c1c1c1;\\n  border-radius: 3px;\\n}\\n\\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a8a8a8;\\n}\\n\\n\\n\\n.suggestions-dropdown[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .suggestion-main[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  \\n  .suggestion-details[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  \\n  .suggestion-item[_ngcontent-%COMP%] {\\n    padding: 10px 12px;\\n  }\\n  \\n  .suggestion-icon[_ngcontent-%COMP%] {\\n    width: 28px;\\n    height: 28px;\\n    margin-right: 10px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "Subject", "debounceTime", "distinctUntilChanged", "switchMap", "takeUntil", "i0", "ɵɵelement", "ɵɵclassMap", "ctx_r0", "icon", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "suggestion_r6", "country", "ɵɵlistener", "AutocompleteComponent_div_6_div_2_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r11", "$implicit", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "selectSuggestion", "AutocompleteComponent_div_6_div_2_Template_div_mouseenter_0_listener", "i_r7", "index", "ctx_r12", "selectedIndex", "ɵɵtemplate", "AutocompleteComponent_div_6_div_2_div_6_Template", "ɵɵclassProp", "ctx_r4", "getLocationTypeIcon", "type", "ɵɵtextInterpolate", "displayText", "ɵɵproperty", "AutocompleteComponent_div_6_div_2_Template", "AutocompleteComponent_div_6_div_3_Template", "ctx_r3", "suggestions", "length", "isLoading", "AutocompleteComponent", "constructor", "autocompleteService", "elementRef", "placeholder", "readonly", "locationSelected", "value", "showSuggestions", "searchSubject", "destroy$", "onChange", "onTouched", "ngOnInit", "pipe", "query", "searchLocations", "subscribe", "document", "addEventListener", "onDocumentClick", "bind", "ngOnDestroy", "next", "complete", "removeEventListener", "writeValue", "inputElement", "nativeElement", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "onInput", "event", "target", "onFocus", "getPopularDestinations", "destinations", "onKeyDown", "key", "preventDefault", "Math", "min", "max", "hideSuggestions", "location", "emit", "contains", "ɵɵdirectiveInject", "i1", "AutocompleteService", "ElementRef", "selectors", "viewQuery", "AutocompleteComponent_Query", "rf", "ctx", "provide", "useExisting", "multi", "decls", "vars", "consts", "template", "AutocompleteComponent_Template", "AutocompleteComponent_i_2_Template", "AutocompleteComponent_Template_input_input_3_listener", "$event", "AutocompleteComponent_Template_input_focus_3_listener", "AutocompleteComponent_Template_input_keydown_3_listener", "AutocompleteComponent_div_5_Template", "AutocompleteComponent_div_6_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\shared\\autocomplete\\autocomplete.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\shared\\autocomplete\\autocomplete.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild, forwardRef } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, Observable } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\nimport { AutocompleteService, AutocompleteLocation } from '../../../services/autocomplete.service';\n\n@Component({\n  selector: 'app-autocomplete',\n  templateUrl: './autocomplete.component.html',\n  styleUrls: ['./autocomplete.component.css'],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => AutocompleteComponent),\n      multi: true\n    }\n  ]\n})\nexport class AutocompleteComponent implements OnInit, OnDestroy, ControlValueAccessor {\n  @Input() placeholder: string = '';\n  @Input() icon: string = '';\n  @Input() readonly: boolean = false;\n  @Output() locationSelected = new EventEmitter<AutocompleteLocation>();\n\n  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;\n\n  value: string = '';\n  suggestions: AutocompleteLocation[] = [];\n  showSuggestions: boolean = false;\n  selectedIndex: number = -1;\n  isLoading: boolean = false;\n\n  private searchSubject = new Subject<string>();\n  private destroy$ = new Subject<void>();\n  private onChange = (value: string) => {};\n  private onTouched = () => {};\n\n  constructor(\n    private autocompleteService: AutocompleteService,\n    private elementRef: ElementRef\n  ) {}\n\n  ngOnInit(): void {\n    // Setup search with debouncing\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(query => this.autocompleteService.searchLocations(query)),\n      takeUntil(this.destroy$)\n    ).subscribe(suggestions => {\n      this.suggestions = suggestions;\n      this.showSuggestions = suggestions.length > 0;\n      this.isLoading = false;\n    });\n\n    // Close suggestions when clicking outside\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n\n  // ControlValueAccessor implementation\n  writeValue(value: string): void {\n    this.value = value || '';\n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n\n  registerOnChange(fn: (value: string) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    if (this.inputElement) {\n      this.inputElement.nativeElement.disabled = isDisabled;\n    }\n  }\n\n  onInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.value = target.value;\n    this.onChange(this.value);\n    \n    if (this.value.length >= 2) {\n      this.isLoading = true;\n      this.searchSubject.next(this.value);\n    } else {\n      this.suggestions = [];\n      this.showSuggestions = false;\n      this.isLoading = false;\n    }\n    \n    this.selectedIndex = -1;\n  }\n\n  onFocus(): void {\n    this.onTouched();\n    if (this.value.length >= 2) {\n      this.showSuggestions = this.suggestions.length > 0;\n    } else if (this.value.length === 0) {\n      // Show popular destinations when focusing on empty input\n      this.autocompleteService.getPopularDestinations().subscribe(destinations => {\n        this.suggestions = destinations;\n        this.showSuggestions = true;\n      });\n    }\n  }\n\n  onKeyDown(event: KeyboardEvent): void {\n    if (!this.showSuggestions) return;\n\n    switch (event.key) {\n      case 'ArrowDown':\n        event.preventDefault();\n        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);\n        break;\n      case 'Enter':\n        event.preventDefault();\n        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {\n          this.selectSuggestion(this.suggestions[this.selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        this.hideSuggestions();\n        break;\n    }\n  }\n\n  selectSuggestion(location: AutocompleteLocation): void {\n    this.value = location.displayText;\n    this.onChange(this.value);\n    this.locationSelected.emit(location);\n    this.hideSuggestions();\n    \n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n\n  hideSuggestions(): void {\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n  }\n\n  private onDocumentClick(event: Event): void {\n    if (!this.elementRef.nativeElement.contains(event.target as Node)) {\n      this.hideSuggestions();\n    }\n  }\n\n  getLocationTypeIcon(type: string): string {\n    switch (type) {\n      case 'airport':\n        return 'fas fa-plane';\n      case 'city':\n        return 'fas fa-city';\n      case 'country':\n        return 'fas fa-flag';\n      default:\n        return 'fas fa-map-marker-alt';\n    }\n  }\n}\n", "<div class=\"autocomplete-wrapper\">\n  <div class=\"input-wrapper\">\n    <i *ngIf=\"icon\" [class]=\"icon\" class=\"input-icon\"></i>\n    <input\n      #inputElement\n      type=\"text\"\n      [placeholder]=\"placeholder\"\n      [readonly]=\"readonly\"\n      class=\"autocomplete-input\"\n      [class.with-icon]=\"icon\"\n      [class.readonly]=\"readonly\"\n      (input)=\"onInput($event)\"\n      (focus)=\"onFocus()\"\n      (keydown)=\"onKeyDown($event)\"\n      autocomplete=\"off\">\n    <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n      <i class=\"fas fa-spinner fa-spin\"></i>\n    </div>\n  </div>\n\n  <div *ngIf=\"showSuggestions\" class=\"suggestions-dropdown\">\n    <div class=\"suggestions-list\">\n      <div\n        *ngFor=\"let suggestion of suggestions; let i = index\"\n        class=\"suggestion-item\"\n        [class.selected]=\"i === selectedIndex\"\n        (click)=\"selectSuggestion(suggestion)\"\n        (mouseenter)=\"selectedIndex = i\">\n        \n        <div class=\"suggestion-icon\">\n          <i [class]=\"getLocationTypeIcon(suggestion.type)\"></i>\n        </div>\n        \n        <div class=\"suggestion-content\">\n          <div class=\"suggestion-main\">{{ suggestion.displayText }}</div>\n          <div *ngIf=\"suggestion.type === 'airport' && suggestion.country\" class=\"suggestion-details\">\n            {{ suggestion.country }}\n          </div>\n        </div>\n        \n        <div class=\"suggestion-type\">\n          <span class=\"type-badge\" [class]=\"'type-' + suggestion.type\">\n            {{ suggestion.type }}\n          </span>\n        </div>\n      </div>\n    </div>\n    \n    <div *ngIf=\"suggestions.length === 0 && !isLoading\" class=\"no-results\">\n      <i class=\"fas fa-search\"></i>\n      <span>No locations found</span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,EAA4CC,UAAU,QAAQ,eAAe;AAC5H,SAA+BC,iBAAiB,QAAQ,gBAAgB;AACxE,SAASC,OAAO,QAAoB,MAAM;AAC1C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;ICDrFC,EAAA,CAAAC,SAAA,WAAsD;;;;IAAtCD,EAAA,CAAAE,UAAA,CAAAC,MAAA,CAAAC,IAAA,CAAc;;;;;IAa9BJ,EAAA,CAAAK,cAAA,aAA+C;IAC7CL,EAAA,CAAAC,SAAA,WAAsC;IACxCD,EAAA,CAAAM,YAAA,EAAM;;;;;IAkBAN,EAAA,CAAAK,cAAA,cAA4F;IAC1FL,EAAA,CAAAO,MAAA,GACF;IAAAP,EAAA,CAAAM,YAAA,EAAM;;;;IADJN,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,aAAA,CAAAC,OAAA,MACF;;;;;;IAfJX,EAAA,CAAAK,cAAA,cAKmC;IADjCL,EAAA,CAAAY,UAAA,mBAAAC,gEAAA;MAAA,MAAAC,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAN,aAAA,GAAAI,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAlB,EAAA,CAAAmB,aAAA;MAAA,OAASnB,EAAA,CAAAoB,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAAX,aAAA,CAA4B;IAAA,EAAC,wBAAAY,qEAAA;MAAA,MAAAR,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,IAAA;MAAA,MAAAO,IAAA,GAAAT,WAAA,CAAAU,KAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAK,OAAA,CAAAC,aAAA,GAAAH,IAAA;IAAA;IAGtCvB,EAAA,CAAAK,cAAA,cAA6B;IAC3BL,EAAA,CAAAC,SAAA,QAAsD;IACxDD,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAK,cAAA,cAAgC;IACDL,EAAA,CAAAO,MAAA,GAA4B;IAAAP,EAAA,CAAAM,YAAA,EAAM;IAC/DN,EAAA,CAAA2B,UAAA,IAAAC,gDAAA,kBAEM;IACR5B,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAK,cAAA,cAA6B;IAEzBL,EAAA,CAAAO,MAAA,GACF;IAAAP,EAAA,CAAAM,YAAA,EAAO;;;;;;IAlBTN,EAAA,CAAA6B,WAAA,aAAAN,IAAA,KAAAO,MAAA,CAAAJ,aAAA,CAAsC;IAKjC1B,EAAA,CAAAQ,SAAA,GAA8C;IAA9CR,EAAA,CAAAE,UAAA,CAAA4B,MAAA,CAAAC,mBAAA,CAAArB,aAAA,CAAAsB,IAAA,EAA8C;IAIpBhC,EAAA,CAAAQ,SAAA,GAA4B;IAA5BR,EAAA,CAAAiC,iBAAA,CAAAvB,aAAA,CAAAwB,WAAA,CAA4B;IACnDlC,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAmC,UAAA,SAAAzB,aAAA,CAAAsB,IAAA,kBAAAtB,aAAA,CAAAC,OAAA,CAAyD;IAMtCX,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAE,UAAA,WAAAQ,aAAA,CAAAsB,IAAA,CAAmC;IAC1DhC,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAC,aAAA,CAAAsB,IAAA,MACF;;;;;IAKNhC,EAAA,CAAAK,cAAA,cAAuE;IACrEL,EAAA,CAAAC,SAAA,YAA6B;IAC7BD,EAAA,CAAAK,cAAA,WAAM;IAAAL,EAAA,CAAAO,MAAA,yBAAkB;IAAAP,EAAA,CAAAM,YAAA,EAAO;;;;;IA9BnCN,EAAA,CAAAK,cAAA,cAA0D;IAEtDL,EAAA,CAAA2B,UAAA,IAAAS,0CAAA,mBAuBM;IACRpC,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAA2B,UAAA,IAAAU,0CAAA,kBAGM;IACRrC,EAAA,CAAAM,YAAA,EAAM;;;;IA7BuBN,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAmC,UAAA,YAAAG,MAAA,CAAAC,WAAA,CAAgB;IAyBrCvC,EAAA,CAAAQ,SAAA,GAA4C;IAA5CR,EAAA,CAAAmC,UAAA,SAAAG,MAAA,CAAAC,WAAA,CAAAC,MAAA,WAAAF,MAAA,CAAAG,SAAA,CAA4C;;;AD9BtD,OAAM,MAAOC,qBAAqB;EAmBhCC,YACUC,mBAAwC,EACxCC,UAAsB;IADtB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,UAAU,GAAVA,UAAU;IApBX,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAA1C,IAAI,GAAW,EAAE;IACjB,KAAA2C,QAAQ,GAAY,KAAK;IACxB,KAAAC,gBAAgB,GAAG,IAAIxD,YAAY,EAAwB;IAIrE,KAAAyD,KAAK,GAAW,EAAE;IAClB,KAAAV,WAAW,GAA2B,EAAE;IACxC,KAAAW,eAAe,GAAY,KAAK;IAChC,KAAAxB,aAAa,GAAW,CAAC,CAAC;IAC1B,KAAAe,SAAS,GAAY,KAAK;IAElB,KAAAU,aAAa,GAAG,IAAIxD,OAAO,EAAU;IACrC,KAAAyD,QAAQ,GAAG,IAAIzD,OAAO,EAAQ;IAC9B,KAAA0D,QAAQ,GAAIJ,KAAa,IAAI,CAAE,CAAC;IAChC,KAAAK,SAAS,GAAG,MAAK,CAAE,CAAC;EAKzB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACJ,aAAa,CAACK,IAAI,CACrB5D,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAAC2D,KAAK,IAAI,IAAI,CAACb,mBAAmB,CAACc,eAAe,CAACD,KAAK,CAAC,CAAC,EACnE1D,SAAS,CAAC,IAAI,CAACqD,QAAQ,CAAC,CACzB,CAACO,SAAS,CAACpB,WAAW,IAAG;MACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACW,eAAe,GAAGX,WAAW,CAACC,MAAM,GAAG,CAAC;MAC7C,IAAI,CAACC,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC;IAEF;IACAmB,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACrE;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACZ,QAAQ,CAACa,IAAI,EAAE;IACpB,IAAI,CAACb,QAAQ,CAACc,QAAQ,EAAE;IACxBN,QAAQ,CAACO,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACL,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;EAEA;EACAK,UAAUA,CAACnB,KAAa;IACtB,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,EAAE;IACxB,IAAI,IAAI,CAACoB,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACrB,KAAK,GAAG,IAAI,CAACA,KAAK;;EAEtD;EAEAsB,gBAAgBA,CAACC,EAA2B;IAC1C,IAAI,CAACnB,QAAQ,GAAGmB,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAAClB,SAAS,GAAGkB,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,IAAI,CAACN,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACM,QAAQ,GAAGD,UAAU;;EAEzD;EAEAE,OAAOA,CAACC,KAAY;IAClB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA0B;IAC/C,IAAI,CAAC9B,KAAK,GAAG8B,MAAM,CAAC9B,KAAK;IACzB,IAAI,CAACI,QAAQ,CAAC,IAAI,CAACJ,KAAK,CAAC;IAEzB,IAAI,IAAI,CAACA,KAAK,CAACT,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACU,aAAa,CAACc,IAAI,CAAC,IAAI,CAAChB,KAAK,CAAC;KACpC,MAAM;MACL,IAAI,CAACV,WAAW,GAAG,EAAE;MACrB,IAAI,CAACW,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACT,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACf,aAAa,GAAG,CAAC,CAAC;EACzB;EAEAsD,OAAOA,CAAA;IACL,IAAI,CAAC1B,SAAS,EAAE;IAChB,IAAI,IAAI,CAACL,KAAK,CAACT,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACU,eAAe,GAAG,IAAI,CAACX,WAAW,CAACC,MAAM,GAAG,CAAC;KACnD,MAAM,IAAI,IAAI,CAACS,KAAK,CAACT,MAAM,KAAK,CAAC,EAAE;MAClC;MACA,IAAI,CAACI,mBAAmB,CAACqC,sBAAsB,EAAE,CAACtB,SAAS,CAACuB,YAAY,IAAG;QACzE,IAAI,CAAC3C,WAAW,GAAG2C,YAAY;QAC/B,IAAI,CAAChC,eAAe,GAAG,IAAI;MAC7B,CAAC,CAAC;;EAEN;EAEAiC,SAASA,CAACL,KAAoB;IAC5B,IAAI,CAAC,IAAI,CAAC5B,eAAe,EAAE;IAE3B,QAAQ4B,KAAK,CAACM,GAAG;MACf,KAAK,WAAW;QACdN,KAAK,CAACO,cAAc,EAAE;QACtB,IAAI,CAAC3D,aAAa,GAAG4D,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7D,aAAa,GAAG,CAAC,EAAE,IAAI,CAACa,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;QAClF;MACF,KAAK,SAAS;QACZsC,KAAK,CAACO,cAAc,EAAE;QACtB,IAAI,CAAC3D,aAAa,GAAG4D,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC9D,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD;MACF,KAAK,OAAO;QACVoD,KAAK,CAACO,cAAc,EAAE;QACtB,IAAI,IAAI,CAAC3D,aAAa,IAAI,CAAC,IAAI,IAAI,CAACA,aAAa,GAAG,IAAI,CAACa,WAAW,CAACC,MAAM,EAAE;UAC3E,IAAI,CAACnB,gBAAgB,CAAC,IAAI,CAACkB,WAAW,CAAC,IAAI,CAACb,aAAa,CAAC,CAAC;;QAE7D;MACF,KAAK,QAAQ;QACX,IAAI,CAAC+D,eAAe,EAAE;QACtB;;EAEN;EAEApE,gBAAgBA,CAACqE,QAA8B;IAC7C,IAAI,CAACzC,KAAK,GAAGyC,QAAQ,CAACxD,WAAW;IACjC,IAAI,CAACmB,QAAQ,CAAC,IAAI,CAACJ,KAAK,CAAC;IACzB,IAAI,CAACD,gBAAgB,CAAC2C,IAAI,CAACD,QAAQ,CAAC;IACpC,IAAI,CAACD,eAAe,EAAE;IAEtB,IAAI,IAAI,CAACpB,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACrB,KAAK,GAAG,IAAI,CAACA,KAAK;;EAEtD;EAEAwC,eAAeA,CAAA;IACb,IAAI,CAACvC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACxB,aAAa,GAAG,CAAC,CAAC;EACzB;EAEQoC,eAAeA,CAACgB,KAAY;IAClC,IAAI,CAAC,IAAI,CAACjC,UAAU,CAACyB,aAAa,CAACsB,QAAQ,CAACd,KAAK,CAACC,MAAc,CAAC,EAAE;MACjE,IAAI,CAACU,eAAe,EAAE;;EAE1B;EAEA1D,mBAAmBA,CAACC,IAAY;IAC9B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,SAAS;QACZ,OAAO,aAAa;MACtB;QACE,OAAO,uBAAuB;;EAEpC;;;uBA5JWU,qBAAqB,EAAA1C,EAAA,CAAA6F,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAA/F,EAAA,CAAA6F,iBAAA,CAAA7F,EAAA,CAAAgG,UAAA;IAAA;EAAA;;;YAArBtD,qBAAqB;MAAAuD,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;uCARrB,CACT;QACEE,OAAO,EAAE5G,iBAAiB;QAC1B6G,WAAW,EAAE9G,UAAU,CAAC,MAAMiD,qBAAqB,CAAC;QACpD8D,KAAK,EAAE;OACR,CACF;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBHpG,EAAA,CAAAK,cAAA,aAAkC;UAE9BL,EAAA,CAAA2B,UAAA,IAAAmF,kCAAA,eAAsD;UACtD9G,EAAA,CAAAK,cAAA,kBAWqB;UAHnBL,EAAA,CAAAY,UAAA,mBAAAmG,sDAAAC,MAAA;YAAA,OAASX,GAAA,CAAAxB,OAAA,CAAAmC,MAAA,CAAe;UAAA,EAAC,mBAAAC,sDAAA;YAAA,OAChBZ,GAAA,CAAArB,OAAA,EAAS;UAAA,EADO,qBAAAkC,wDAAAF,MAAA;YAAA,OAEdX,GAAA,CAAAlB,SAAA,CAAA6B,MAAA,CAAiB;UAAA,EAFH;UAR3BhH,EAAA,CAAAM,YAAA,EAWqB;UACrBN,EAAA,CAAA2B,UAAA,IAAAwF,oCAAA,iBAEM;UACRnH,EAAA,CAAAM,YAAA,EAAM;UAENN,EAAA,CAAA2B,UAAA,IAAAyF,oCAAA,iBAgCM;UACRpH,EAAA,CAAAM,YAAA,EAAM;;;UAnDEN,EAAA,CAAAQ,SAAA,GAAU;UAAVR,EAAA,CAAAmC,UAAA,SAAAkE,GAAA,CAAAjG,IAAA,CAAU;UAOZJ,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAA6B,WAAA,cAAAwE,GAAA,CAAAjG,IAAA,CAAwB,aAAAiG,GAAA,CAAAtD,QAAA;UAHxB/C,EAAA,CAAAmC,UAAA,gBAAAkE,GAAA,CAAAvD,WAAA,CAA2B,aAAAuD,GAAA,CAAAtD,QAAA;UASvB/C,EAAA,CAAAQ,SAAA,GAAe;UAAfR,EAAA,CAAAmC,UAAA,SAAAkE,GAAA,CAAA5D,SAAA,CAAe;UAKjBzC,EAAA,CAAAQ,SAAA,GAAqB;UAArBR,EAAA,CAAAmC,UAAA,SAAAkE,GAAA,CAAAnD,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}