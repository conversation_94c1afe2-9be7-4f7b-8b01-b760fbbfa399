import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { FlightService } from '../../services/flight.service';
import { AuthService } from '../../services/auth.service';
import { FlightSearchForm, LatestSearch, Location } from '../../models/flight.models';
import { AutocompleteLocation } from '../../services/autocomplete.service';
import { Observable } from 'rxjs';

interface FlightSegment {
  from: string;
  to: string;
  date: string;
}

@Component({
  selector: 'app-flight',
  templateUrl: './flight.component.html',
  styleUrls: ['./flight.component.css']
})
export class FlightComponent implements OnInit {
  flightForm: FormGroup;
  isLoading = false;
  currentUser: any = null;
  latestSearches$: Observable<LatestSearch[]>;

  // Form state
  tripType = 'oneWay';
  showReturnDate = false;
  showCalendar = false;
  additionalSegments: FlightSegment[] = [];

  // Passenger counts
  adultCount = 1;
  childCount = 0;
  infantCount = 0;

  // Class selection
  selectedClass = 'economy';

  // Baggage options
  baggageOptions = [
    { value: 'all', label: '--All--' },
    { value: '20kg', label: '20kg' },
    { value: '30kg', label: '30kg' },
    { value: 'extra', label: 'Extra' }
  ];

  // Calendar options
  calendarDays = [
    { value: '1', label: '+/- 1 Days' },
    { value: '3', label: '+/- 3 Days' },
    { value: '7', label: '+/- 7 Days' }
  ];

  constructor(
    private fb: FormBuilder,
    private flightService: FlightService,
    private authService: AuthService,
    private router: Router
  ) {
    this.flightForm = this.createForm();
    this.latestSearches$ = this.flightService.latestSearches$;
  }

  ngOnInit(): void {
    // Check authentication
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });

    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/signin']);
      return;
    }

    // Set default dates
    this.setDefaultDates();
  }

  /**
   * Create reactive form
   */
  private createForm(): FormGroup {
    // Set default dates
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    return this.fb.group({
      tripType: ['oneWay', Validators.required],
      from: ['IST - Istanbul Airport', Validators.required], // Default test value
      to: ['TUN - Carthage Airport', Validators.required], // Default test value
      departureDate: [this.formatDate(tomorrow), Validators.required],
      returnDate: [this.formatDate(nextWeek)],
      adults: [1, [Validators.required, Validators.min(1)]],
      children: [0, [Validators.min(0)]],
      infants: [0, [Validators.min(0)]],
      class: ['economy', Validators.required],
      preferredAirline: [''],
      directFlights: [false],
      refundableFares: [false],
      baggage: ['all'],
      calendar: [false],
      calendarDays: ['3']
    });
  }

  /**
   * Set default dates (today and tomorrow)
   */
  private setDefaultDates(): void {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    this.flightForm.patchValue({
      departureDate: this.formatDate(tomorrow),
      returnDate: this.formatDate(nextWeek)
    });
  }

  /**
   * Format date for input field
   */
  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Handle trip type change
   */
  onTripTypeChange(type: string): void {
    this.tripType = type;
    this.showReturnDate = type === 'roundTrip';

    this.flightForm.patchValue({ tripType: type });

    if (type === 'roundTrip') {
      this.flightForm.get('returnDate')?.setValidators([Validators.required]);
    } else {
      this.flightForm.get('returnDate')?.clearValidators();
    }
    this.flightForm.get('returnDate')?.updateValueAndValidity();

    // Clear additional segments when switching away from multi-city
    if (type !== 'multiCity') {
      this.additionalSegments = [];
    }
  }

  /**
   * Add a new flight segment for multi-city trips
   */
  addSegment(): void {
    const newSegment: FlightSegment = {
      from: '',
      to: '',
      date: ''
    };
    this.additionalSegments.push(newSegment);
  }

  /**
   * Remove a flight segment
   */
  removeSegment(index: number): void {
    this.additionalSegments.splice(index, 1);
  }

  /**
   * Handle location selection for main form fields
   */
  onFromLocationSelected(location: AutocompleteLocation): void {
    this.flightForm.patchValue({ from: location.displayText });
  }

  onToLocationSelected(location: AutocompleteLocation): void {
    this.flightForm.patchValue({ to: location.displayText });
  }

  /**
   * Handle location selection for additional segments
   */
  onSegmentLocationSelected(location: AutocompleteLocation, segmentIndex: number, field: 'from' | 'to'): void {
    if (this.additionalSegments[segmentIndex]) {
      this.additionalSegments[segmentIndex][field] = location.displayText;
    }
  }

  /**
   * Handle passenger count changes
   */
  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {
    const currentValue = this.flightForm.get(type)?.value || 0;
    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);

    // Ensure at least 1 adult
    if (type === 'adults' && newValue < 1) {
      newValue = 1;
    }

    this.flightForm.patchValue({ [type]: newValue });

    // Update component properties for display
    if (type === 'adults') this.adultCount = newValue;
    if (type === 'children') this.childCount = newValue;
    if (type === 'infants') this.infantCount = newValue;
  }

  /**
   * Get total passenger count
   */
  getTotalPassengers(): number {
    const adults = this.flightForm.get('adults')?.value || 0;
    const children = this.flightForm.get('children')?.value || 0;
    const infants = this.flightForm.get('infants')?.value || 0;
    return adults + children + infants;
  }

  /**
   * Handle class selection
   */
  onClassChange(flightClass: string): void {
    this.selectedClass = flightClass;
    this.flightForm.patchValue({ class: flightClass });
  }

  /**
   * Toggle calendar option
   */
  toggleCalendar(): void {
    this.showCalendar = !this.showCalendar;
    this.flightForm.patchValue({ calendar: this.showCalendar });
  }

  /**
   * Swap from and to locations
   */
  swapLocations(): void {
    const from = this.flightForm.get('from')?.value;
    const to = this.flightForm.get('to')?.value;

    this.flightForm.patchValue({
      from: to,
      to: from
    });
  }

  /**
   * Handle form submission
   */
  onSubmit(): void {
    if (this.flightForm.valid) {
      this.isLoading = true;

      const formValue = this.flightForm.value;
      const searchForm: FlightSearchForm = {
        tripType: formValue.tripType,
        from: formValue.from,
        to: formValue.to,
        departureDate: formValue.departureDate,
        returnDate: formValue.returnDate,
        passengers: {
          adults: formValue.adults,
          children: formValue.children,
          infants: formValue.infants
        },
        class: formValue.class,
        preferredAirline: formValue.preferredAirline,
        directFlights: formValue.directFlights,
        refundableFares: formValue.refundableFares,
        baggage: formValue.baggage,
        calendar: formValue.calendar
      };

      // Save to latest searches
      this.flightService.saveLatestSearch(searchForm);

      // Perform search based on trip type
      let searchObservable;

      switch (searchForm.tripType) {
        case 'oneWay':
          searchObservable = this.flightService.searchOneWayFlights(searchForm);
          break;
        case 'roundTrip':
          searchObservable = this.flightService.searchRoundTripFlights(searchForm);
          break;
        case 'multiCity':
          searchObservable = this.flightService.searchMultiCityFlights(searchForm);
          break;
        default:
          searchObservable = this.flightService.searchOneWayFlights(searchForm);
      }

      searchObservable.subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.header.success) {
            console.log('Flight search results:', response);
            // Navigate to results page with search results and parameters
            this.router.navigate(['/flight-results'], {
              state: {
                results: response,
                searchParams: searchForm
              }
            });
          } else {
            console.error('Search failed:', response.header.messages);
            // TODO: Show error message to user
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Search error:', error);
          // TODO: Show error message to user
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.flightForm.controls).forEach(key => {
        this.flightForm.get(key)?.markAsTouched();
      });
    }
  }

  /**
   * Load a previous search
   */
  loadLatestSearch(search: LatestSearch): void {
    this.flightForm.patchValue({
      from: search.from,
      to: search.to,
      departureDate: search.date,
      adults: search.passengers,
      children: 0,
      infants: 0
    });
  }

  /**
   * Clear latest searches
   */
  clearLatestSearches(): void {
    this.flightService.clearLatestSearches();
  }

  /**
   * Get form control error message
   */
  getErrorMessage(controlName: string): string {
    const control = this.flightForm.get(controlName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${controlName} is required`;
      }
      if (control.errors['min']) {
        return `Minimum value is ${control.errors['min'].min}`;
      }
    }
    return '';
  }
}
