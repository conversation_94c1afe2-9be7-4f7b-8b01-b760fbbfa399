// Flight Search Request Models
export interface FlightSearchRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  ReturnDate?: string;
  Night?: number;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: number[];
  Culture: string;
  Currency: string;
}

export interface Location {
  type: number;
  id: string;
  name: string;
  code: string;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

export interface CorporateCode {
  Code: string;
  Rule: {
    Airline: string;
    Supplier: string;
  };
}

// Flight Search Response Models
export interface FlightSearchResponse {
  header: ResponseHeader;
  body: FlightSearchBody;
}

export interface ResponseHeader {
  requestId: string;
  success: boolean;
  responseMessage: string;
  messages?: Message[];
}

export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

export interface FlightSearchBody {
  searchId: string;
  expiresOn: string;
  flights: Flight[];
}

export interface Flight {
  provider: number;
  id: string;
  items: FlightItem[];
  offers: Offer[];
  groupKeys: string[];
}

export interface FlightItem {
  segmentNumber: number;
  flightNo: string;
  pnlName: string;
  flightDate: string;
  airline: Airline;
  marketingAirline?: Airline;
  duration: number;
  dayChange: number;
  departure: FlightLocation;
  arrival: FlightLocation;
  flightClass: FlightClass;
  route: number;
  segments: Segment[];
  stopCount: number;
  flightProvider: FlightProvider;
  baggageInformations: BaggageInformation[];
  passengers: FlightPassenger[];
  flightType: number;
}

export interface Airline {
  id: string;
  name: string;
  code: string;
}

export interface FlightLocation {
  id: string;
  name: string;
  code: string;
  country: Country;
  city: City;
  airport: Airport;
  terminal: string;
  geoLocation: GeoLocation;
}

export interface Country {
  id: string;
  name: string;
  code: string;
}

export interface City {
  id: string;
  name: string;
  code: string;
}

export interface Airport {
  id: string;
  name: string;
  code: string;
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
}

export interface FlightClass {
  id: number;
  name: string;
  code: string;
}

export interface Segment {
  id: string;
  departure: FlightLocation;
  arrival: FlightLocation;
  flightNo: string;
  airline: Airline;
  duration: number;
  aircraft: string;
}

export interface FlightProvider {
  id: number;
  name: string;
  code: string;
}

export interface BaggageInformation {
  segmentNumber: number;
  passengerType: number;
  unit: number;
  value: number;
  weight: number;
  piece: number;
}

export interface FlightPassenger {
  type: number;
  count: number;
}

export interface Offer {
  segmentNumber: number;
  singleAdultPrice: Price;
  priceBreakDown: PriceBreakDown;
  serviceFee: Price;
  seatInfo: SeatInfo;
  flightClassInformations: FlightClass[];
  baggageInformations: BaggageInformation[];
  services: Service[];
  reservableInfo: ReservableInfo;
  groupKeys: string[];
  fees: Fees;
  isPackageOffer: boolean;
  hasBrand: boolean;
  route: number;
  flightProvider: FlightProvider;
  flightBrandInfo: FlightBrandInfo;
  expiresOn: string;
  offerId: string;
  price: Price;
  provider: number;
}

export interface Price {
  amount: number;
  currency: string;
}

export interface PriceBreakDown {
  items: PriceBreakDownItem[];
}

export interface PriceBreakDownItem {
  passengerType: number;
  count: number;
  price: Price;
}

export interface SeatInfo {
  availableSeats: number;
  lastTicketDate: string;
}

export interface Service {
  id: string;
  name: string;
  code: string;
  type: number;
  price: Price;
}

export interface ReservableInfo {
  isReservable: boolean;
  reservableUntil: string;
}

export interface Fees {
  totalFee: Price;
  feeItems: FeeItem[];
}

export interface FeeItem {
  type: number;
  name: string;
  price: Price;
}

export interface FlightBrandInfo {
  brandName: string;
  brandCode: string;
  features: Feature[];
}

export interface Feature {
  id: string;
  name: string;
  included: boolean;
}

// Form Models for UI
export interface FlightSearchForm {
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
  from: string;
  to: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  class: 'economy' | 'business' | 'first';
  preferredAirline?: string;
  directFlights: boolean;
  refundableFares: boolean;
  baggage: string;
  calendar: boolean;
}

// Latest Searches Model
export interface LatestSearch {
  id: string;
  from: string;
  to: string;
  date: string;
  passengers: number;
  searchDate: Date;
}
