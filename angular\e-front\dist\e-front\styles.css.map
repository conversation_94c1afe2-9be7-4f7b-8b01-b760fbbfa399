{"version": 3, "file": "styles.css", "mappings": ";;;AAAA,oCAAoC;;AAEpC,0BAA0B;AAC1B;EACE,sBAAsB;AACxB;;AAEA;EACE,YAAY;EACZ,SAAS;EACT,UAAU;AACZ;;AAEA;EACE,yGAAyG;EACzG,gBAAgB;EAChB,cAAc;EACd,yBAAyB;EACzB,mCAAmC;EACnC,kCAAkC;AACpC;;AAEA,iCAAiC;AACjC;EACE,YAAY;EACZ,gBAAgB;EAChB,eAAe;EACf,oBAAoB;AACtB;;AAEA,gCAAgC;AAChC;EACE,oBAAoB;EACpB,kBAAkB;AACpB;;AAEA,+BAA+B;AAC/B;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA,mCAAmC;AACnC;EACE,0BAA0B;EAC1B,mBAAmB;AACrB;;AAEA,oBAAoB;AACpB;EACE,kBAAkB;EAClB,UAAU;EACV,WAAW;EACX,UAAU;EACV,YAAY;EACZ,gBAAgB;EAChB,sBAAsB;EACtB,mBAAmB;EACnB,SAAS;AACX", "sources": ["./src/styles.css"], "sourcesContent": ["/* Global Styles for Agency Portal */\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n}\n\nhtml, body {\n  height: 100%;\n  margin: 0;\n  padding: 0;\n}\n\nbody {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n  line-height: 1.6;\n  color: #1e293b;\n  background-color: #f8fafc;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n/* Remove default button styles */\nbutton {\n  border: none;\n  background: none;\n  cursor: pointer;\n  font-family: inherit;\n}\n\n/* Remove default input styles */\ninput, textarea, select {\n  font-family: inherit;\n  font-size: inherit;\n}\n\n/* Remove default link styles */\na {\n  color: inherit;\n  text-decoration: none;\n}\n\n/* Focus styles for accessibility */\n*:focus {\n  outline: 2px solid #3b82f6;\n  outline-offset: 2px;\n}\n\n/* Utility classes */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n"], "names": [], "sourceRoot": "webpack:///", "x_google_ignoreList": []}