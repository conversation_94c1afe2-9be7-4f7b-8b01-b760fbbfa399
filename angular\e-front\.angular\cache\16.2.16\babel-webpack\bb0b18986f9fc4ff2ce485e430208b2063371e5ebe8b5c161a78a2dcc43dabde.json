{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/signin',\n  pathMatch: 'full'\n}, {\n  path: 'signin',\n  component: SigninComponent\n}, {\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'flights',\n  component: FlightComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'flight-results',\n  component: FlightResultsComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/signin'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SigninComponent", "DashboardComponent", "FlightComponent", "FlightResultsComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AuthGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/signin', pathMatch: 'full' },\n  { path: 'signin', component: SigninComponent },\n  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },\n  { path: 'flights', component: FlightComponent, canActivate: [AuthGuard] },\n  { path: 'flight-results', component: FlightResultsComponent, canActivate: [AuthGuard] },\n  { path: '**', redirectTo: '/signin' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,SAAS;EAAEC,SAAS,EAAE;AAAM,CAAE,EACtD;EAAEF,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAET;AAAe,CAAE,EAC9C;EAAEM,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAER,kBAAkB;EAAES,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC9E;EAAEE,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEP,eAAe;EAAEQ,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACzE;EAAEE,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAEN,sBAAsB;EAAEO,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EACvF;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAS,CAAE,CACtC;AAMD,OAAM,MAAOI,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBZ,YAAY,CAACa,OAAO,CAACP,MAAM,CAAC,EAC5BN,YAAY;IAAA;EAAA;;;2EAEXY,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAf,YAAA;IAAAgB,OAAA,GAFjBhB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}