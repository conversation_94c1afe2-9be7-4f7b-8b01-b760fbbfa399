{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction DashboardComponent_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"Welcome back, \", ctx_r0.currentUser.name, \"\");\n  }\n}\nfunction DashboardComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"span\", 56);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 57);\n    i0.ɵɵtext(5, \"Balance: 8500.48 TND\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 58);\n    i0.ɵɵtext(7, \"Due: -1399.52 TND\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.agencyCode, \" - Workspace for Demo\");\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.currentUser = null;\n  }\n  ngOnInit() {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 108,\n      vars: 2,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"header-left\"], [1, \"brand-logo\"], [1, \"logo-text\"], [\"class\", \"dashboard-subtitle\", 4, \"ngIf\"], [1, \"header-right\"], [1, \"top-nav\"], [\"href\", \"#\", 1, \"nav-link\"], [1, \"fas\", \"fa-home\"], [1, \"fas\", \"fa-bell\"], [1, \"fas\", \"fa-wrench\"], [1, \"fas\", \"fa-globe\"], [1, \"logout-button\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"dashboard-main\"], [1, \"navigation-grid\"], [1, \"row\"], [1, \"nav-card\", \"booking-queue\"], [1, \"card-icon\"], [1, \"fas\", \"fa-clipboard-list\"], [1, \"card-title\"], [1, \"nav-card\", \"commissions\"], [1, \"fas\", \"fa-percentage\"], [1, \"nav-card\", \"support\"], [1, \"fas\", \"fa-question-circle\"], [1, \"nav-card\", \"sub-agent\"], [1, \"fas\", \"fa-users\"], [1, \"nav-card\", \"helpdesk\"], [1, \"fas\", \"fa-headset\"], [1, \"nav-card\", \"profile\"], [1, \"fas\", \"fa-user\"], [1, \"nav-card\", \"finance\"], [1, \"fas\", \"fa-money-bill-wave\"], [1, \"nav-card\", \"agency-profile\"], [1, \"fas\", \"fa-building\"], [1, \"row\", \"featured-row\"], [1, \"nav-card\", \"featured\", \"flights\"], [1, \"fas\", \"fa-plane\"], [1, \"nav-card\", \"featured\", \"hotels\"], [1, \"fas\", \"fa-hotel\"], [1, \"nav-card\", \"news\"], [1, \"fas\", \"fa-newspaper\"], [1, \"nav-card\", \"flight-info\"], [1, \"fas\", \"fa-info-circle\"], [1, \"nav-card\", \"featured\", \"charter\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"nav-card\", \"passengers\"], [1, \"fas\", \"fa-user-friends\"], [1, \"nav-card\", \"credit-request\"], [1, \"fas\", \"fa-credit-card\"], [1, \"dashboard-subtitle\"], [1, \"user-info\"], [1, \"agency-info\"], [1, \"agency-id\"], [1, \"balance\"], [1, \"due-info\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5);\n          i0.ɵɵtext(6, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, DashboardComponent_p_7_Template, 2, 1, \"p\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"nav\", 8)(10, \"a\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵtext(12, \" Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"a\", 9);\n          i0.ɵɵelement(14, \"i\", 11);\n          i0.ɵɵtext(15, \" News\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"a\", 9);\n          i0.ɵɵelement(17, \"i\", 12);\n          i0.ɵɵtext(18, \" Tools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"a\", 9);\n          i0.ɵɵelement(20, \"i\", 13);\n          i0.ɵɵtext(21, \" Languages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_22_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(23, \"i\", 15);\n          i0.ɵɵtext(24, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(25, DashboardComponent_div_25_Template, 8, 1, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"main\", 17)(27, \"div\", 18)(28, \"div\", 19)(29, \"div\", 20)(30, \"div\", 21);\n          i0.ɵɵelement(31, \"i\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"span\", 23);\n          i0.ɵɵtext(33, \"Booking Queue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 24)(35, \"div\", 21);\n          i0.ɵɵelement(36, \"i\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 23);\n          i0.ɵɵtext(38, \"Commissions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 26)(40, \"div\", 21);\n          i0.ɵɵelement(41, \"i\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\", 23);\n          i0.ɵɵtext(43, \"Support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 28)(45, \"div\", 21);\n          i0.ɵɵelement(46, \"i\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"span\", 23);\n          i0.ɵɵtext(48, \"Sub Agent\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 19)(50, \"div\", 30)(51, \"div\", 21);\n          i0.ɵɵelement(52, \"i\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 23);\n          i0.ɵɵtext(54, \"Helpdesk\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 32)(56, \"div\", 21);\n          i0.ɵɵelement(57, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 23);\n          i0.ɵɵtext(59, \"Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 34)(61, \"div\", 21);\n          i0.ɵɵelement(62, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\", 23);\n          i0.ɵɵtext(64, \"Finance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 36)(66, \"div\", 21);\n          i0.ɵɵelement(67, \"i\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\", 23);\n          i0.ɵɵtext(69, \"Agency Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 38)(71, \"div\", 39)(72, \"div\", 21);\n          i0.ɵɵelement(73, \"i\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"span\", 23);\n          i0.ɵɵtext(75, \"Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 41)(77, \"div\", 21);\n          i0.ɵɵelement(78, \"i\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"span\", 23);\n          i0.ɵɵtext(80, \"Hotels\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(81, \"div\", 19)(82, \"div\", 43)(83, \"div\", 21);\n          i0.ɵɵelement(84, \"i\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 23);\n          i0.ɵɵtext(86, \"News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 45)(88, \"div\", 21);\n          i0.ɵɵelement(89, \"i\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"span\", 23);\n          i0.ɵɵtext(91, \"Flight Info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(92, \"div\", 47)(93, \"div\", 21);\n          i0.ɵɵelement(94, \"i\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"span\", 23);\n          i0.ɵɵtext(96, \"Charter\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(97, \"div\", 19)(98, \"div\", 49)(99, \"div\", 21);\n          i0.ɵɵelement(100, \"i\", 50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"span\", 23);\n          i0.ɵɵtext(102, \"Passengers\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 51)(104, \"div\", 21);\n          i0.ɵɵelement(105, \"i\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"span\", 23);\n          i0.ɵɵtext(107, \"Credit Request\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\"[_ngcontent-%COMP%]:root {\\n  --primary-color: #3b82f6;\\n  --primary-hover: #2563eb;\\n  --secondary-color: #64748b;\\n  --success-color: #10b981;\\n  --background-color: #f8fafc;\\n  --surface-color: #ffffff;\\n  --text-primary: #1e293b;\\n  --text-secondary: #64748b;\\n  --text-muted: #94a3b8;\\n  --border-color: #e2e8f0;\\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --radius-md: 0.5rem;\\n  --radius-lg: 0.75rem;\\n  --radius-xl: 1rem;\\n}\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: var(--background-color);\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: var(--surface-color);\\n  border-bottom: 1px solid var(--border-color);\\n  box-shadow: var(--shadow-sm);\\n  padding: 1rem 0;\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 1.5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n@media (max-width: 768px) {\\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    padding: 0 1rem;\\n  }\\n}\\n\\n.header-left[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.dashboard-title[_ngcontent-%COMP%] {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.dashboard-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n}\\n\\n.header-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.user-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n}\\n@media (max-width: 768px) {\\n  .user-details[_ngcontent-%COMP%] {\\n    align-items: center;\\n  }\\n}\\n\\n.user-name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n}\\n\\n.user-agency[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-secondary);\\n}\\n\\n.logout-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: var(--surface-color);\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  color: var(--text-secondary);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n.logout-button[_ngcontent-%COMP%]:hover {\\n  background: #f1f5f9;\\n  border-color: var(--text-secondary);\\n  color: var(--text-primary);\\n}\\n.logout-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n\\n.dashboard-main[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 1.5rem;\\n}\\n@media (max-width: 768px) {\\n  .dashboard-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem;\\n  }\\n}\\n\\n.welcome-card[_ngcontent-%COMP%] {\\n  background: var(--surface-color);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-md);\\n  overflow: hidden;\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  color: white;\\n  text-align: center;\\n}\\n.card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin: 0 0 0.5rem 0;\\n}\\n.card-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.user-info-card[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.user-info-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.info-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: var(--text-secondary);\\n}\\n.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-primary);\\n  font-weight: 500;\\n}\\n\\n.actions-card[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.actions-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 1rem;\\n}\\n\\n.action-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  padding: 1rem 1.5rem;\\n  border: 2px solid transparent;\\n  border-radius: var(--radius-lg);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  text-decoration: none;\\n}\\n.action-button.primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  color: white;\\n  box-shadow: var(--shadow-md);\\n}\\n.action-button.primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, var(--primary-hover), #1d4ed8);\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-lg);\\n}\\n.action-button.secondary[_ngcontent-%COMP%] {\\n  background: var(--surface-color);\\n  border-color: var(--border-color);\\n  color: var(--text-primary);\\n}\\n.action-button.secondary[_ngcontent-%COMP%]:hover {\\n  background: #f8fafc;\\n  border-color: var(--primary-color);\\n  color: var(--primary-color);\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-md);\\n}\\n.action-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n.action-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n@media (max-width: 640px) {\\n  .dashboard-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .card-header[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .card-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .user-info-card[_ngcontent-%COMP%], .actions-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem;\\n  }\\n  .info-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .action-buttons[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .action-button[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1.25rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "currentUser", "name", "ctx_r1", "agencyCode", "DashboardComponent", "constructor", "authService", "router", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "DashboardComponent_p_7_Template", "ɵɵelement", "ɵɵlistener", "DashboardComponent_Template_button_click_22_listener", "DashboardComponent_div_25_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any = null;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header with Block to Book branding -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <div class=\"brand-logo\">\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <p class=\"dashboard-subtitle\" *ngIf=\"currentUser\">Welcome back, {{ currentUser.name }}</p>\n      </div>\n      <div class=\"header-right\">\n        <nav class=\"top-nav\">\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-home\"></i> Home</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-bell\"></i> News</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-wrench\"></i> Tools</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-globe\"></i> Languages</a>\n          <button class=\"logout-button\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i> Logout\n          </button>\n        </nav>\n      </div>\n    </div>\n    <div class=\"user-info\" *ngIf=\"currentUser\">\n      <div class=\"agency-info\">\n        <span class=\"agency-id\">{{ currentUser.agencyCode }} - Workspace for Demo</span>\n        <span class=\"balance\">Balance: 8500.48 TND</span>\n        <span class=\"due-info\">Due: -1399.52 TND</span>\n      </div>\n    </div>\n  </header>\n\n  <!-- Main content with navigation cards -->\n  <main class=\"dashboard-main\">\n    <div class=\"navigation-grid\">\n      <!-- First row of cards -->\n      <div class=\"row\">\n        <!-- Booking Queue -->\n        <div class=\"nav-card booking-queue\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-clipboard-list\"></i>\n          </div>\n          <span class=\"card-title\">Booking Queue</span>\n        </div>\n\n        <!-- Commissions -->\n        <div class=\"nav-card commissions\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-percentage\"></i>\n          </div>\n          <span class=\"card-title\">Commissions</span>\n        </div>\n\n        <!-- Support -->\n        <div class=\"nav-card support\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-question-circle\"></i>\n          </div>\n          <span class=\"card-title\">Support</span>\n        </div>\n\n        <!-- Sub-Agent -->\n        <div class=\"nav-card sub-agent\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-users\"></i>\n          </div>\n          <span class=\"card-title\">Sub Agent</span>\n        </div>\n      </div>\n\n      <!-- Second row of cards -->\n      <div class=\"row\">\n        <!-- Helpdesk -->\n        <div class=\"nav-card helpdesk\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-headset\"></i>\n          </div>\n          <span class=\"card-title\">Helpdesk</span>\n        </div>\n\n        <!-- Profile -->\n        <div class=\"nav-card profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user\"></i>\n          </div>\n          <span class=\"card-title\">Profile</span>\n        </div>\n\n        <!-- Finance -->\n        <div class=\"nav-card finance\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <span class=\"card-title\">Finance</span>\n        </div>\n\n        <!-- Agency Profile -->\n        <div class=\"nav-card agency-profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-building\"></i>\n          </div>\n          <span class=\"card-title\">Agency Profile</span>\n        </div>\n      </div>\n\n      <!-- Third row with larger cards -->\n      <div class=\"row featured-row\">\n        <!-- Flights -->\n        <div class=\"nav-card featured flights\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <span class=\"card-title\">Flights</span>\n        </div>\n\n        <!-- Hotels -->\n        <div class=\"nav-card featured hotels\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-hotel\"></i>\n          </div>\n          <span class=\"card-title\">Hotels</span>\n        </div>\n      </div>\n\n      <!-- Fourth row -->\n      <div class=\"row\">\n        <!-- News -->\n        <div class=\"nav-card news\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-newspaper\"></i>\n          </div>\n          <span class=\"card-title\">News</span>\n        </div>\n\n        <!-- Flight Info -->\n        <div class=\"nav-card flight-info\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-info-circle\"></i>\n          </div>\n          <span class=\"card-title\">Flight Info</span>\n        </div>\n\n        <!-- Charter -->\n        <div class=\"nav-card featured charter\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane-departure\"></i>\n          </div>\n          <span class=\"card-title\">Charter</span>\n        </div>\n      </div>\n\n      <!-- Fifth row -->\n      <div class=\"row\">\n        <!-- Passengers -->\n        <div class=\"nav-card passengers\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user-friends\"></i>\n          </div>\n          <span class=\"card-title\">Passengers</span>\n        </div>\n\n        <!-- Credit Request -->\n        <div class=\"nav-card credit-request\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <span class=\"card-title\">Credit Request</span>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;;;;ICQQA,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAxCH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,WAAA,CAAAC,IAAA,KAAoC;;;;;IAc1FR,EAAA,CAAAC,cAAA,cAA2C;IAEfD,EAAA,CAAAE,MAAA,GAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChFH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjDH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFvBH,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAI,MAAA,CAAAF,WAAA,CAAAG,UAAA,0BAAiD;;;ADfjF,OAAM,MAAOC,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAP,WAAW,GAAQ,IAAI;EAKpB;EAEHQ,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACX,WAAW,GAAGW,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACR,WAAW,CAACQ,MAAM,EAAE;IACzB,IAAI,CAACP,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBA1BWT,kBAAkB,EAAAX,EAAA,CAAAsB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxB,EAAA,CAAAsB,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBf,kBAAkB;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BjC,EAAA,CAAAC,cAAA,aAAiC;UAMCD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAmC,UAAA,IAAAC,+BAAA,eAA0F;UAC5FpC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA0B;UAEOD,EAAA,CAAAqC,SAAA,aAA2B;UAACrC,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA2B;UAACrC,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA6B;UAACrC,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,YAA6B;UAAAD,EAAA,CAAAqC,SAAA,aAA4B;UAACrC,EAAA,CAAAE,MAAA,kBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAA,CAAAC,cAAA,kBAAiD;UAAnBD,EAAA,CAAAsC,UAAA,mBAAAC,qDAAA;YAAA,OAASL,GAAA,CAAAb,MAAA,EAAQ;UAAA,EAAC;UAC9CrB,EAAA,CAAAqC,SAAA,aAAmC;UAACrC,EAAA,CAAAE,MAAA,gBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIfH,EAAA,CAAAmC,UAAA,KAAAK,kCAAA,kBAMM;UACRxC,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,gBAA6B;UAOnBD,EAAA,CAAAqC,SAAA,aAAqC;UACvCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI/CH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAqC,SAAA,aAAiC;UACnCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAqC,SAAA,aAAsC;UACxCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK7CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,aAA8B;UAChCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI1CH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAqC,SAAA,aAA2B;UAC7BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAqC,SAAA,aAAsC;UACxCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAqC;UAEjCD,EAAA,CAAAqC,SAAA,aAA+B;UACjCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKlDH,EAAA,CAAAC,cAAA,eAA8B;UAIxBD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAIzCH,EAAA,CAAAC,cAAA,eAAsC;UAElCD,EAAA,CAAAqC,SAAA,aAA4B;UAC9BrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK1CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,aAAgC;UAClCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAItCH,EAAA,CAAAC,cAAA,eAAkC;UAE9BD,EAAA,CAAAqC,SAAA,aAAkC;UACpCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI7CH,EAAA,CAAAC,cAAA,eAAuC;UAEnCD,EAAA,CAAAqC,SAAA,aAAsC;UACxCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK3CH,EAAA,CAAAC,cAAA,eAAiB;UAIXD,EAAA,CAAAqC,SAAA,cAAmC;UACrCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAI5CH,EAAA,CAAAC,cAAA,gBAAqC;UAEjCD,EAAA,CAAAqC,SAAA,cAAkC;UACpCrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,iBAAyB;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;UA7JjBH,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAyC,UAAA,SAAAP,GAAA,CAAA3B,WAAA,CAAiB;UAc5BP,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAyC,UAAA,SAAAP,GAAA,CAAA3B,WAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}