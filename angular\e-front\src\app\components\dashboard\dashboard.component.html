<div class="dashboard-container">
  <!-- Header with Block to Book branding -->
  <header class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <div class="brand-logo">
          <span class="logo-text">BLOCK TO BOOK</span>
        </div>
        <p class="dashboard-subtitle" *ngIf="currentUser">Welcome back, {{ currentUser.name }}</p>
      </div>
      <div class="header-right">
        <nav class="top-nav">
          <a href="#" class="nav-link"><i class="fas fa-home"></i> Home</a>

          <a href="#" class="nav-link"><i class="fas fa-wrench"></i> Tools</a>
          <a href="#" class="nav-link"><i class="fas fa-globe"></i> Languages</a>
          <button class="logout-button" (click)="logout()">
            <i class="fas fa-sign-out-alt"></i> Logout
          </button>
        </nav>
      </div>
    </div>
    <div class="user-info" *ngIf="currentUser">
      <div class="agency-info">
        <span class="agency-id">{{ currentUser.agencyCode }} - Workspace for Demo</span>
        <span class="balance">Balance: 8500.48 TND</span>
        <span class="due-info">Due: -1399.52 TND</span>
      </div>
    </div>
  </header>

  <!-- Main content with navigation cards -->
  <main class="dashboard-main">
    <div class="navigation-grid">
      <!-- First row of cards -->
      <div class="row">
        <!-- Booking Queue -->
        <div class="nav-card booking-queue">
          <div class="card-icon">
            <i class="fas fa-clipboard-list"></i>
          </div>
          <span class="card-title">Booking Queue</span>
        </div>

        <!-- Commissions -->
        <div class="nav-card commissions">
          <div class="card-icon">
            <i class="fas fa-percentage"></i>
          </div>
          <span class="card-title">Commissions</span>
        </div>

        <!-- Sub-Agent -->
        <div class="nav-card sub-agent">
          <div class="card-icon">
            <i class="fas fa-users"></i>
          </div>
          <span class="card-title">Sub Agent</span>
        </div>
      </div>

      <!-- Second row of cards -->
      <div class="row">
        <!-- Profile -->
        <div class="nav-card profile">
          <div class="card-icon">
            <i class="fas fa-user"></i>
          </div>
          <span class="card-title">Profile</span>
        </div>

        <!-- Finance -->
        <div class="nav-card finance">
          <div class="card-icon">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <span class="card-title">Finance</span>
        </div>

        <!-- Agency Profile -->
        <div class="nav-card agency-profile">
          <div class="card-icon">
            <i class="fas fa-building"></i>
          </div>
          <span class="card-title">Agency Profile</span>
        </div>
      </div>

      <!-- Third row with larger cards -->
      <div class="row featured-row">
        <!-- Flights -->
        <div class="nav-card featured flights" (click)="navigateToFlights()">
          <div class="card-icon">
            <i class="fas fa-plane"></i>
          </div>
          <span class="card-title">Flights</span>
        </div>

        <!-- Flight Info -->
        <div class="nav-card flight-info">
          <div class="card-icon">
            <i class="fas fa-info-circle"></i>
          </div>
          <span class="card-title">Flight Info</span>
        </div>
      </div>

      <!-- Fourth row -->
      <div class="row">
        <!-- Passengers -->
        <div class="nav-card passengers">
          <div class="card-icon">
            <i class="fas fa-user-friends"></i>
          </div>
          <span class="card-title">Passengers</span>
        </div>

        <!-- Credit Request -->
        <div class="nav-card credit-request">
          <div class="card-icon">
            <i class="fas fa-credit-card"></i>
          </div>
          <span class="card-title">Credit Request</span>
        </div>
      </div>
    </div>
  </main>
</div>
