{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nfunction SigninComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 41);\n    i0.ɵɵelement(3, \"circle\", 42)(4, \"line\", 43)(5, \"line\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function SigninComponent_div_13_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.clearError());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 46);\n    i0.ɵɵelement(10, \"line\", 47)(11, \"line\", 48);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction SigninComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"agency\"), \" \");\n  }\n}\nfunction SigninComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"username\"), \" \");\n  }\n}\nfunction SigninComponent__svg_svg_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 50);\n    i0.ɵɵelement(1, \"path\", 51)(2, \"circle\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent__svg_svg_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 50);\n    i0.ɵɵelement(1, \"path\", 53)(2, \"path\", 54)(3, \"circle\", 52)(4, \"path\", 55);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction SigninComponent_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 57);\n    i0.ɵɵelement(2, \"path\", 58)(3, \"path\", 59);\n    i0.ɵɵelementStart(4, \"defs\")(5, \"linearGradient\", 60);\n    i0.ɵɵelement(6, \"stop\", 61)(7, \"stop\", 62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtext(8, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SigninComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n    this.signinForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // If user is already authenticated, redirect to dashboard\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.signinForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      const credentials = {\n        agency: this.signinForm.value.agency.trim(),\n        username: this.signinForm.value.username.trim(),\n        password: this.signinForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          if (response.success) {\n            // Redirect to dashboard or intended route\n            this.router.navigate(['/dashboard']);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n          // Clear password field on error\n          this.signinForm.patchValue({\n            password: ''\n          });\n        },\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Mark all form fields as touched to show validation errors\n   */\n  markFormGroupTouched() {\n    Object.keys(this.signinForm.controls).forEach(key => {\n      const control = this.signinForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Get form control for easier access in template\n   */\n  getFormControl(controlName) {\n    return this.signinForm.get(controlName);\n  }\n  /**\n   * Check if form control has error\n   */\n  hasError(controlName, errorType) {\n    const control = this.getFormControl(controlName);\n    return !!(control?.hasError(errorType) && control?.touched);\n  }\n  /**\n   * Get error message for form control\n   */\n  getErrorMessage(controlName) {\n    const control = this.getFormControl(controlName);\n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(controlName)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;\n    }\n    return '';\n  }\n  /**\n   * Get display name for form field\n   */\n  getFieldDisplayName(controlName) {\n    const fieldNames = {\n      agency: 'Agency',\n      username: 'Username',\n      password: 'Password'\n    };\n    return fieldNames[controlName] || controlName;\n  }\n  /**\n   * Clear error message\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function SigninComponent_Factory(t) {\n      return new (t || SigninComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SigninComponent,\n      selectors: [[\"app-signin\"]],\n      decls: 56,\n      vars: 20,\n      consts: [[1, \"signin-container\"], [1, \"signin-card\"], [1, \"signin-header\"], [1, \"logo-container\"], [1, \"logo\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 2L2 7L12 12L22 7L12 2Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M2 17L12 22L22 17\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M2 12L12 17L22 12\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"signin-title\"], [1, \"signin-subtitle\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"signin-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"agency\", 1, \"form-label\"], [1, \"input-container\"], [\"type\", \"text\", \"id\", \"agency\", \"formControlName\", \"agency\", \"placeholder\", \"Enter your agency code\", \"autocomplete\", \"organization\", 1, \"form-input\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"input-icon\"], [\"d\", \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"cx\", \"12\", \"cy\", \"7\", \"r\", \"4\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"for\", \"username\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"username\", \"formControlName\", \"username\", \"placeholder\", \"Enter your username\", \"autocomplete\", \"username\", 1, \"form-input\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 1, \"form-input\", 3, \"type\"], [\"x\", \"3\", \"y\", \"11\", \"width\", \"18\", \"height\", \"11\", \"rx\", \"2\", \"ry\", \"2\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"cx\", \"12\", \"cy\", \"16\", \"r\", \"1\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"signin-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"signin-footer\"], [1, \"footer-text\"], [1, \"background-decoration\"], [1, \"decoration-circle\", \"decoration-circle-1\"], [1, \"decoration-circle\", \"decoration-circle-2\"], [1, \"decoration-circle\", \"decoration-circle-3\"], [1, \"error-container\"], [1, \"error-message\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"error-icon\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"15\", \"y1\", \"9\", \"x2\", \"9\", \"y2\", \"15\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"9\", \"y1\", \"9\", \"x2\", \"15\", \"y2\", \"15\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [1, \"error-text\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"3\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M1 1L23 23\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"loading-content\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"loading-spinner\"], [\"d\", \"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M12 3C16.9706 3 21 7.02944 21 12\", \"stroke\", \"url(#spinner-gradient)\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\"], [\"id\", \"spinner-gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", 2, \"stop-color\", \"currentColor\", \"stop-opacity\", \"0\"], [\"offset\", \"100%\", 2, \"stop-color\", \"currentColor\", \"stop-opacity\", \"1\"]],\n      template: function SigninComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(5, \"svg\", 5);\n          i0.ɵɵelement(6, \"path\", 6)(7, \"path\", 7)(8, \"path\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(9, \"h1\", 9);\n          i0.ɵɵtext(10, \"Agency Portal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 10);\n          i0.ɵɵtext(12, \"Sign in to your agency account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SigninComponent_div_13_Template, 12, 1, \"div\", 11);\n          i0.ɵɵelementStart(14, \"form\", 12);\n          i0.ɵɵlistener(\"ngSubmit\", function SigninComponent_Template_form_ngSubmit_14_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"label\", 14);\n          i0.ɵɵtext(17, \"Agency Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵelement(19, \"input\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(20, \"svg\", 17);\n          i0.ɵɵelement(21, \"path\", 18)(22, \"circle\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, SigninComponent_div_23_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 21);\n          i0.ɵɵtext(26, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 15);\n          i0.ɵɵelement(28, \"input\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 17);\n          i0.ɵɵelement(30, \"path\", 18)(31, \"circle\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, SigninComponent_div_32_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(33, \"div\", 13)(34, \"label\", 23);\n          i0.ɵɵtext(35, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 15);\n          i0.ɵɵelement(37, \"input\", 24);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 17);\n          i0.ɵɵelement(39, \"rect\", 25)(40, \"circle\", 26)(41, \"path\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_42_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtemplate(43, SigninComponent__svg_svg_43_Template, 3, 0, \"svg\", 29);\n          i0.ɵɵtemplate(44, SigninComponent__svg_svg_44_Template, 5, 0, \"svg\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, SigninComponent_div_45_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 30);\n          i0.ɵɵtemplate(47, SigninComponent_span_47_Template, 2, 0, \"span\", 31);\n          i0.ɵɵtemplate(48, SigninComponent_span_48_Template, 9, 0, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 33)(50, \"p\", 34);\n          i0.ɵɵtext(51, \" Need help? Contact your system administrator \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 35);\n          i0.ɵɵelement(53, \"div\", 36)(54, \"div\", 37)(55, \"div\", 38);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.signinForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"username\", \"required\") || ctx.hasError(\"username\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"username\", \"required\") || ctx.hasError(\"username\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-label\", ctx.showPassword ? \"Hide password\" : \"Show password\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.signinForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-color: #3b82f6;\\n  --primary-hover: #2563eb;\\n  --primary-light: #dbeafe;\\n  --secondary-color: #64748b;\\n  --success-color: #10b981;\\n  --error-color: #ef4444;\\n  --warning-color: #f59e0b;\\n  --background-color: #f8fafc;\\n  --surface-color: #ffffff;\\n  --text-primary: #1e293b;\\n  --text-secondary: #64748b;\\n  --text-muted: #94a3b8;\\n  --border-color: #e2e8f0;\\n  --border-focus: #3b82f6;\\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --radius-sm: 0.375rem;\\n  --radius-md: 0.5rem;\\n  --radius-lg: 0.75rem;\\n  --radius-xl: 1rem;\\n}\\n\\n\\n\\n.signin-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 1rem;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n@media (max-width: 640px) {\\n  .signin-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n}\\n\\n\\n\\n.background-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.decoration-circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n\\n.decoration-circle.decoration-circle-1[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 10%;\\n  left: 10%;\\n  animation-delay: 0s;\\n}\\n\\n.decoration-circle.decoration-circle-2[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 60%;\\n  right: 15%;\\n  animation-delay: 2s;\\n}\\n\\n.decoration-circle.decoration-circle-3[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  bottom: 20%;\\n  left: 20%;\\n  animation-delay: 4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-20px);\\n  }\\n}\\n\\n\\n\\n.signin-card[_ngcontent-%COMP%] {\\n  background: var(--surface-color);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-xl);\\n  padding: 2.5rem;\\n  width: 100%;\\n  max-width: 420px;\\n  position: relative;\\n  z-index: 1;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n@media (max-width: 640px) {\\n  .signin-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    max-width: 100%;\\n    margin: 0.5rem;\\n  }\\n}\\n\\n\\n\\n.signin-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 64px;\\n  height: 64px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  border-radius: var(--radius-xl);\\n  color: white;\\n  box-shadow: var(--shadow-lg);\\n  margin: 0 auto;\\n}\\n\\n.signin-title[_ngcontent-%COMP%] {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.2;\\n}\\n\\n.signin-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.error-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  background: #fef2f2;\\n  border: 1px solid #fecaca;\\n  border-radius: var(--radius-md);\\n  color: #dc2626;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  position: relative;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: var(--error-color);\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #dc2626;\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: var(--radius-sm);\\n  margin-left: auto;\\n  flex-shrink: 0;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 38, 38, 0.1);\\n}\\n\\n.error-close[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--error-color);\\n  outline-offset: 2px;\\n}\\n\\n\\n\\n.signin-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1rem 0.875rem 3rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  font-size: 1rem;\\n  line-height: 1.5;\\n  color: var(--text-primary);\\n  background: var(--surface-color);\\n  transition: all 0.2s ease;\\n}\\n\\n.form-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-muted);\\n}\\n\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--border-focus);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n\\n.form-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n}\\n\\n.form-input.error[_ngcontent-%COMP%]:focus {\\n  border-color: var(--error-color);\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n\\n.form-input[_ngcontent-%COMP%]:disabled {\\n  background: #f8fafc;\\n  color: var(--text-muted);\\n  cursor: not-allowed;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  color: var(--text-muted);\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  color: var(--text-muted);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: var(--radius-sm);\\n  transition: color 0.2s ease;\\n  z-index: 1;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--text-secondary);\\n}\\n\\n.password-toggle[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--border-focus);\\n  outline-offset: 2px;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--error-color);\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n\\n\\n.signin-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1.5rem;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  color: white;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: var(--shadow-sm);\\n  margin-top: 0.5rem;\\n}\\n\\n.signin-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--primary-hover), #1d4ed8);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n\\n.signin-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n\\n.signin-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.signin-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--text-muted);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n\\n.signin-button.loading[_ngcontent-%COMP%] {\\n  background: var(--text-muted);\\n  cursor: not-allowed;\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n\\n.signin-footer[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  text-align: center;\\n}\\n\\n.footer-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-muted);\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .signin-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n\\n  .signin-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .form-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem 0.875rem 0.75rem 2.75rem;\\n  }\\n\\n  .input-icon[_ngcontent-%COMP%] {\\n    left: 0.875rem;\\n  }\\n\\n  .password-toggle[_ngcontent-%COMP%] {\\n    right: 0.875rem;\\n  }\\n}\\n\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  [_ngcontent-%COMP%]:root {\\n    --background-color: #0f172a;\\n    --surface-color: #1e293b;\\n    --text-primary: #f1f5f9;\\n    --text-secondary: #cbd5e1;\\n    --text-muted: #64748b;\\n    --border-color: #334155;\\n  }\\n\\n  .signin-card[_ngcontent-%COMP%] {\\n    background: rgba(30, 41, 59, 0.95);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n\\n  .form-input[_ngcontent-%COMP%] {\\n    background: var(--surface-color);\\n    border-color: var(--border-color);\\n  }\\n}\\n\\n\\n\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%] {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n\\n\\n\\n.signin-button[_ngcontent-%COMP%]:focus-visible, .form-input[_ngcontent-%COMP%]:focus-visible, .password-toggle[_ngcontent-%COMP%]:focus-visible, .error-close[_ngcontent-%COMP%]:focus-visible {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "SigninComponent_div_13_Template_button_click_8_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r2", "ctx_r5", "SigninComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "showPassword", "signinForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "username", "password", "ngOnInit", "isAuthenticated", "navigate", "onSubmit", "valid", "credentials", "value", "trim", "login", "subscribe", "next", "response", "success", "error", "message", "patchValue", "complete", "markFormGroupTouched", "togglePasswordVisibility", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFormControl", "controlName", "<PERSON><PERSON><PERSON><PERSON>", "errorType", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "fieldNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SigninComponent_Template", "rf", "ctx", "ɵɵtemplate", "SigninComponent_div_13_Template", "SigninComponent_Template_form_ngSubmit_14_listener", "SigninComponent_div_23_Template", "SigninComponent_div_32_Template", "SigninComponent_Template_button_click_42_listener", "SigninComponent__svg_svg_43_Template", "SigninComponent__svg_svg_44_Template", "SigninComponent_div_45_Template", "SigninComponent_span_47_Template", "SigninComponent_span_48_Template", "ɵɵproperty", "ɵɵclassProp", "ɵɵattribute", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\signin\\signin.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\signin\\signin.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { LoginCredentials } from '../../models/auth.models';\n\n@Component({\n  selector: 'app-signin',\n  templateUrl: './signin.component.html',\n  styleUrls: ['./signin.component.css']\n})\nexport class SigninComponent implements OnInit {\n  signinForm: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.signinForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // If user is already authenticated, redirect to dashboard\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.signinForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      const credentials: LoginCredentials = {\n        agency: this.signinForm.value.agency.trim(),\n        username: this.signinForm.value.username.trim(),\n        password: this.signinForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          if (response.success) {\n            // Redirect to dashboard or intended route\n            this.router.navigate(['/dashboard']);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n\n          // Clear password field on error\n          this.signinForm.patchValue({ password: '' });\n        },\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Mark all form fields as touched to show validation errors\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.signinForm.controls).forEach(key => {\n      const control = this.signinForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Get form control for easier access in template\n   */\n  getFormControl(controlName: string) {\n    return this.signinForm.get(controlName);\n  }\n\n  /**\n   * Check if form control has error\n   */\n  hasError(controlName: string, errorType: string): boolean {\n    const control = this.getFormControl(controlName);\n    return !!(control?.hasError(errorType) && control?.touched);\n  }\n\n  /**\n   * Get error message for form control\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.getFormControl(controlName);\n\n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(controlName)} is required`;\n    }\n\n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;\n    }\n\n    return '';\n  }\n\n  /**\n   * Get display name for form field\n   */\n  private getFieldDisplayName(controlName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      agency: 'Agency',\n      username: 'Username',\n      password: 'Password'\n    };\n    return fieldNames[controlName] || controlName;\n  }\n\n  /**\n   * Clear error message\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"signin-container\">\n  <div class=\"signin-card\">\n    <!-- Header -->\n    <div class=\"signin-header\">\n      <div class=\"logo-container\">\n        <div class=\"logo\">\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n      </div>\n      <h1 class=\"signin-title\">Agency Portal</h1>\n      <p class=\"signin-subtitle\">Sign in to your agency account</p>\n    </div>\n\n    <!-- Error Message -->\n    <div class=\"error-container\" *ngIf=\"errorMessage\">\n      <div class=\"error-message\">\n        <svg class=\"error-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Sign-in Form -->\n    <form [formGroup]=\"signinForm\" (ngSubmit)=\"onSubmit()\" class=\"signin-form\">\n      <!-- Agency Field -->\n      <div class=\"form-group\">\n        <label for=\"agency\" class=\"form-label\">Agency Code</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"agency\"\n            formControlName=\"agency\"\n            class=\"form-input\"\n            [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n            placeholder=\"Enter your agency code\"\n            autocomplete=\"organization\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('agency', 'required') || hasError('agency', 'minlength')\">\n          {{ getErrorMessage('agency') }}\n        </div>\n      </div>\n\n      <!-- Username Field -->\n      <div class=\"form-group\">\n        <label for=\"username\" class=\"form-label\">Username</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"username\"\n            formControlName=\"username\"\n            class=\"form-input\"\n            [class.error]=\"hasError('username', 'required') || hasError('username', 'minlength')\"\n            placeholder=\"Enter your username\"\n            autocomplete=\"username\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('username', 'required') || hasError('username', 'minlength')\">\n          {{ getErrorMessage('username') }}\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"form-group\">\n        <label for=\"password\" class=\"form-label\">Password</label>\n        <div class=\"input-container\">\n          <input\n            [type]=\"showPassword ? 'text' : 'password'\"\n            id=\"password\"\n            formControlName=\"password\"\n            class=\"form-input\"\n            [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n            placeholder=\"Enter your password\"\n            autocomplete=\"current-password\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n          <button\n            type=\"button\"\n            class=\"password-toggle\"\n            (click)=\"togglePasswordVisibility()\"\n            [attr.aria-label]=\"showPassword ? 'Hide password' : 'Show password'\"\n          >\n            <svg *ngIf=\"!showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            </svg>\n            <svg *ngIf=\"showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <path d=\"M1 1L23 23\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </button>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('password', 'required') || hasError('password', 'minlength')\">\n          {{ getErrorMessage('password') }}\n        </div>\n      </div>\n\n      <!-- Submit Button -->\n      <button\n        type=\"submit\"\n        class=\"signin-button\"\n        [disabled]=\"isLoading || signinForm.invalid\"\n        [class.loading]=\"isLoading\"\n      >\n        <span *ngIf=\"!isLoading\">Sign In</span>\n        <span *ngIf=\"isLoading\" class=\"loading-content\">\n          <svg class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M12 3C16.9706 3 21 7.02944 21 12\" stroke=\"url(#spinner-gradient)\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n            <defs>\n              <linearGradient id=\"spinner-gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                <stop offset=\"0%\" style=\"stop-color:currentColor;stop-opacity:0\" />\n                <stop offset=\"100%\" style=\"stop-color:currentColor;stop-opacity:1\" />\n              </linearGradient>\n            </defs>\n          </svg>\n          Signing in...\n        </span>\n      </button>\n    </form>\n\n    <!-- Footer -->\n    <div class=\"signin-footer\">\n      <p class=\"footer-text\">\n        Need help? Contact your system administrator\n      </p>\n    </div>\n  </div>\n\n  <!-- Background decoration -->\n  <div class=\"background-decoration\">\n    <div class=\"decoration-circle decoration-circle-1\"></div>\n    <div class=\"decoration-circle decoration-circle-2\"></div>\n    <div class=\"decoration-circle decoration-circle-3\"></div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICiB/DC,EAAA,CAAAC,cAAA,cAAkD;IAE9CD,EAAA,CAAAE,cAAA,EAAkH;IAAlHF,EAAA,CAAAC,cAAA,cAAkH;IAChHD,EAAA,CAAAG,SAAA,iBAAuE;IAGzEH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,wDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC9Dd,EAAA,CAAAE,cAAA,EAA+F;IAA/FF,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAG,SAAA,gBAA4E;IAE9EH,EAAA,CAAAI,YAAA,EAAM;;;;IALFJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IA8BxBlB,EAAA,CAAAK,eAAA,EAAkG;IAAlGL,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,gBACF;;;;;;IAqBArB,EAAA,CAAAK,eAAA,EAAsG;IAAtGL,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,kBACF;;;;;IA2BIrB,EAAA,CAAAE,cAAA,EAAqH;IAArHF,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAG,SAAA,eAAoG;IAEtGH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,EAAoH;IAApHF,EAAA,CAAAC,cAAA,cAAoH;IAClHD,EAAA,CAAAG,SAAA,eAA0M;IAI5MH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGVJ,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAI,MAAA,CAAAF,eAAA,kBACF;;;;;IAUArB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,cAAO;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IACvCJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAoK;IAEpKH,EAAA,CAAAC,cAAA,WAAM;IAEFD,EAAA,CAAAG,SAAA,eAAmE;IAErEH,EAAA,CAAAI,YAAA,EAAiB;IAGrBJ,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADrIf,OAAM,MAAOoB,eAAe;EAM1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAX,YAAY,GAAG,EAAE;IACjB,KAAAY,YAAY,GAAG,KAAK;IAOlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC;MACvCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE,EAAE;MACtC,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,UAAU,CAACW,KAAK,EAAE;MACzB,IAAI,CAACb,SAAS,GAAG,IAAI;MACrB,IAAI,CAACX,YAAY,GAAG,EAAE;MAEtB,MAAMyB,WAAW,GAAqB;QACpCV,MAAM,EAAE,IAAI,CAACF,UAAU,CAACa,KAAK,CAACX,MAAM,CAACY,IAAI,EAAE;QAC3CT,QAAQ,EAAE,IAAI,CAACL,UAAU,CAACa,KAAK,CAACR,QAAQ,CAACS,IAAI,EAAE;QAC/CR,QAAQ,EAAE,IAAI,CAACN,UAAU,CAACa,KAAK,CAACP;OACjC;MAED,IAAI,CAACV,WAAW,CAACmB,KAAK,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA,IAAI,CAACtB,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;QAExC,CAAC;QACDW,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACX,YAAY,GAAGiC,KAAK,CAACC,OAAO,IAAI,iCAAiC;UAEtE;UACA,IAAI,CAACrB,UAAU,CAACsB,UAAU,CAAC;YAAEhB,QAAQ,EAAE;UAAE,CAAE,CAAC;QAC9C,CAAC;QACDiB,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACzB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC0B,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAAC1B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGQyB,oBAAoBA,CAAA;IAC1BE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC/B,UAAU,CAACgC,GAAG,CAACF,GAAG,CAAC;MACxCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,cAAcA,CAACC,WAAmB;IAChC,OAAO,IAAI,CAACnC,UAAU,CAACgC,GAAG,CAACG,WAAW,CAAC;EACzC;EAEA;;;EAGAC,QAAQA,CAACD,WAAmB,EAAEE,SAAiB;IAC7C,MAAMN,OAAO,GAAG,IAAI,CAACG,cAAc,CAACC,WAAW,CAAC;IAChD,OAAO,CAAC,EAAEJ,OAAO,EAAEK,QAAQ,CAACC,SAAS,CAAC,IAAIN,OAAO,EAAEO,OAAO,CAAC;EAC7D;EAEA;;;EAGAhD,eAAeA,CAAC6C,WAAmB;IACjC,MAAMJ,OAAO,GAAG,IAAI,CAACG,cAAc,CAACC,WAAW,CAAC;IAEhD,IAAIJ,OAAO,EAAEK,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACG,mBAAmB,CAACJ,WAAW,CAAC,cAAc;;IAG/D,IAAIJ,OAAO,EAAEK,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMI,cAAc,GAAGT,OAAO,CAACU,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MACpE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACJ,WAAW,CAAC,qBAAqBK,cAAc,aAAa;;IAGjG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACJ,WAAmB;IAC7C,MAAMO,UAAU,GAA8B;MAC5CxC,MAAM,EAAE,QAAQ;MAChBG,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX;IACD,OAAOoC,UAAU,CAACP,WAAW,CAAC,IAAIA,WAAW;EAC/C;EAEA;;;EAGApD,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBAjIWM,eAAe,EAAAxB,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5E,EAAA,CAAA0E,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAA0E,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfxD,eAAe;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5BvF,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,cAAA,EAA+F;UAA/FF,EAAA,CAAAC,cAAA,aAA+F;UAC7FD,EAAA,CAAAG,SAAA,cAA4H;UAG9HH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAK,eAAA,EAAyB;UAAzBL,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAM,MAAA,qBAAa;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC3CJ,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAM,MAAA,sCAA8B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI/DJ,EAAA,CAAAyF,UAAA,KAAAC,+BAAA,mBAeM;UAGN1F,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAAO,UAAA,sBAAAoF,mDAAA;YAAA,OAAYH,GAAA,CAAA/C,QAAA,EAAU;UAAA,EAAC;UAEpDzC,EAAA,CAAAC,cAAA,eAAwB;UACiBD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC1DJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAsQ;UAExQH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAyF,UAAA,KAAAG,+BAAA,kBAEM;UACR5F,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAwB;UAAxBL,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAsQ;UAExQH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAyF,UAAA,KAAAI,+BAAA,kBAEM;UACR7F,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAwB;UAAxBL,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAgG;UAGlGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAKC;UALDL,EAAA,CAAAC,cAAA,kBAKC;UAFCD,EAAA,CAAAO,UAAA,mBAAAuF,kDAAA;YAAA,OAASN,GAAA,CAAAhC,wBAAA,EAA0B;UAAA,EAAC;UAGpCxD,EAAA,CAAAyF,UAAA,KAAAM,oCAAA,kBAGM;UACN/F,EAAA,CAAAyF,UAAA,KAAAO,oCAAA,kBAKM;UACRhG,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAyF,UAAA,KAAAQ,+BAAA,kBAEM;UACRjG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAyF,UAAA,KAAAS,gCAAA,mBAAuC;UACvClG,EAAA,CAAAyF,UAAA,KAAAU,gCAAA,mBAYO;UACTnG,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,eAA2B;UAEvBD,EAAA,CAAAM,MAAA,sDACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAG,SAAA,eAAyD;UAG3DH,EAAA,CAAAI,YAAA,EAAM;;;UA/I0BJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAAtE,YAAA,CAAkB;UAkB1ClB,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAAoG,UAAA,cAAAZ,GAAA,CAAAzD,UAAA,CAAwB;UAUtB/B,EAAA,CAAAe,SAAA,GAAiF;UAAjFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,0BAAAqB,GAAA,CAAArB,QAAA,wBAAiF;UAS5DnE,EAAA,CAAAe,SAAA,GAAuE;UAAvEf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,0BAAAqB,GAAA,CAAArB,QAAA,wBAAuE;UAc5FnE,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAAqF;UAShEnE,EAAA,CAAAe,SAAA,GAA2E;UAA3Ef,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAA2E;UAchGnE,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAAqF;UAJrFnE,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA1D,YAAA,uBAA2C;UAiB3C9B,EAAA,CAAAe,SAAA,GAAoE;UAApEf,EAAA,CAAAsG,WAAA,eAAAd,GAAA,CAAA1D,YAAA,qCAAoE;UAE9D9B,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAAoG,UAAA,UAAAZ,GAAA,CAAA1D,YAAA,CAAmB;UAInB9B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA1D,YAAA,CAAkB;UAQH9B,EAAA,CAAAe,SAAA,GAA2E;UAA3Ef,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAA2E;UAUpGnE,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAqG,WAAA,YAAAb,GAAA,CAAA3D,SAAA,CAA2B;UAD3B7B,EAAA,CAAAoG,UAAA,aAAAZ,GAAA,CAAA3D,SAAA,IAAA2D,GAAA,CAAAzD,UAAA,CAAAwE,OAAA,CAA4C;UAGrCvG,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAAoG,UAAA,UAAAZ,GAAA,CAAA3D,SAAA,CAAgB;UAChB7B,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA3D,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}