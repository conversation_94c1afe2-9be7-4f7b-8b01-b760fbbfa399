{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';\nimport { TokenExpiryAlertComponent } from './components/shared/token-expiry-alert/token-expiry-alert.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, SigninComponent, DashboardComponent, FlightComponent, FlightResultsComponent, AutocompleteComponent, TokenExpiryAlertComponent],\n  imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, FormsModule, HttpClientModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppRoutingModule", "AppComponent", "SigninComponent", "DashboardComponent", "FlightComponent", "FlightResultsComponent", "AutocompleteComponent", "TokenExpiryAlertComponent", "AuthInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';\nimport { TokenExpiryAlertComponent } from './components/shared/token-expiry-alert/token-expiry-alert.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    SigninComponent,\n    DashboardComponent,\n    FlightComponent,\n    FlightResultsComponent,\n    AutocompleteComponent,\n    TokenExpiryAlertComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    FormsModule,\n    HttpClientModule\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,yDAAyD;AAC/F,SAASC,yBAAyB,QAAQ,qEAAqE;AAC/G,SAASC,eAAe,QAAQ,iCAAiC;AA4B1D,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EA1BrBhB,QAAQ,CAAC;EACRiB,YAAY,EAAE,CACZV,YAAY,EACZC,eAAe,EACfC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,qBAAqB,EACrBC,yBAAyB,CAC1B;EACDK,OAAO,EAAE,CACPjB,aAAa,EACbK,gBAAgB,EAChBJ,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,CACjB;EACDe,SAAS,EAAE,CACT;IACEC,OAAO,EAAEf,iBAAiB;IAC1BgB,QAAQ,EAAEP,eAAe;IACzBQ,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAAChB,YAAY;CACzB,CAAC,C,EACWQ,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}