import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FlightService } from '../../services/flight.service';
import { FlightSearchResponse, Flight, FlightSearchForm } from '../../models/flight.models';
import { Observable } from 'rxjs';

interface AirlineFilter {
  code: string;
  name: string;
  logo: string;
  active: boolean;
}

interface FlightResult {
  airline: string;
  airlineLogo: string;
  price: number;
  currency: string;
  stops: number;
  duration: string;
  departureTime: string;
  arrivalTime: string;
  departureDate: string;
  arrivalDate: string;
  departureAirport: string;
  arrivalAirport: string;
  flightNumber: string;
  aircraftType?: string;
  availableSeats?: string;
  baggageAllowance?: string;
  refundable: boolean;
  restricted?: boolean;
}

@Component({
  selector: 'app-flight-results',
  templateUrl: './flight-results.component.html',
  styleUrls: ['./flight-results.component.css']
})
export class FlightResultsComponent implements OnInit {
  searchResults: FlightSearchResponse | null = null;
  searchForm: FormGroup;
  isLoading = false;
  error: string | null = null;
  flights: FlightResult[] = [];
  searchSummary = '';
  originalSearchParams: any = null;

  // Filter options
  airlineFilters: AirlineFilter[] = [
    { code: 'TK', name: 'Turkish Airlines', logo: 'assets/airlines/turkish-airlines.png', active: false },
    { code: 'LH', name: 'Lufthansa', logo: 'assets/airlines/lufthansa.png', active: false },
    { code: 'EY', name: 'Etihad', logo: 'assets/airlines/etihad.png', active: false },
    { code: 'MS', name: 'EgyptAir', logo: 'assets/airlines/egyptair.png', active: false },
    { code: 'AH', name: 'Air Algerie', logo: 'assets/airlines/air-algerie.png', active: false },
    { code: 'TU', name: 'Tunisair', logo: 'assets/airlines/tunisair.png', active: false }
  ];

  // Organized flight results by stops
  flightsByStops = {
    nonStop: [] as FlightResult[],
    oneStop: [] as FlightResult[],
    twoOrMoreStops: [] as FlightResult[]
  };

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private flightService: FlightService
  ) {
    this.searchForm = this.createSearchForm();
  }

  ngOnInit(): void {
    // Get search results from navigation state or route params
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state?.['results']) {
      this.searchResults = navigation.extras.state['results'];
      this.originalSearchParams = navigation.extras.state['searchParams'];
      this.processSearchResults();
    } else {
      // For demo purposes, create sample data
      this.createSampleData();
    }

    // Initialize form with original search parameters
    if (this.originalSearchParams) {
      this.initializeSearchForm();
    }
  }

  private createSampleData(): void {
    // Create sample flight data for demonstration
    this.flights = [
      {
        airline: 'Turkish Airlines',
        airlineLogo: 'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png',
        price: 684,
        currency: 'TND',
        stops: 0,
        duration: '02H 55M',
        departureTime: '22:20',
        arrivalTime: '23:15',
        departureDate: '19/06/2025',
        arrivalDate: '19/06/2025',
        departureAirport: 'IST ISTANBUL',
        arrivalAirport: 'TUN TUNIS',
        flightNumber: 'TK657',
        aircraftType: 'V Economy Standard',
        availableSeats: '32B',
        baggageAllowance: '2PC',
        refundable: false,
        restricted: true
      },
      {
        airline: 'Pegasus Airlines',
        airlineLogo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/6/6c/Pegasus_Airlines_logo.svg/1200px-Pegasus_Airlines_logo.svg.png',
        price: 710,
        currency: 'TND',
        stops: 1,
        duration: '07H 05M',
        departureTime: '12:00',
        arrivalTime: '17:05',
        departureDate: '19/06/2025',
        arrivalDate: '19/06/2025',
        departureAirport: 'SAW ISTANBUL',
        arrivalAirport: 'TUN TUNIS',
        flightNumber: 'AZ7049',
        aircraftType: 'L Economy Standard',
        availableSeats: '32I',
        baggageAllowance: '1PC',
        refundable: false,
        restricted: false
      }
    ];

    this.searchSummary = 'IST - TUN, 19/06/2025, 1 Adult(s)';
    this.originalSearchParams = {
      from: 'IST - Istanbul Airport',
      to: 'TUN - Carthage Airport',
      departureDate: '2025-06-19',
      passengers: { adults: 1, children: 0, infants: 0 }
    };
  }

  private createSearchForm(): FormGroup {
    return this.fb.group({
      from: ['', Validators.required],
      to: ['', Validators.required],
      departureDate: ['', Validators.required],
      adults: [1, [Validators.required, Validators.min(1)]],
      children: [0, [Validators.min(0)]],
      infants: [0, [Validators.min(0)]]
    });
  }

  private initializeSearchForm(): void {
    if (this.originalSearchParams) {
      this.searchForm.patchValue({
        from: this.originalSearchParams.from,
        to: this.originalSearchParams.to,
        departureDate: this.originalSearchParams.departureDate,
        adults: this.originalSearchParams.passengers?.adults || 1,
        children: this.originalSearchParams.passengers?.children || 0,
        infants: this.originalSearchParams.passengers?.infants || 0
      });
    }
  }

  private processSearchResults(): void {
    if (!this.searchResults?.body?.flights) {
      return;
    }

    // Reset flight arrays
    this.flightsByStops.nonStop = [];
    this.flightsByStops.oneStop = [];
    this.flightsByStops.twoOrMoreStops = [];
    this.flights = [];

    // Process each flight and categorize by stops
    this.searchResults.body.flights.forEach(flight => {
      const flightResult = this.convertToFlightResult(flight);
      this.flights.push(flightResult);

      // Get stop count from the first flight item
      const stopCount = flight.items[0]?.stopCount || 0;

      if (stopCount === 0) {
        this.flightsByStops.nonStop.push(flightResult);
      } else if (stopCount === 1) {
        this.flightsByStops.oneStop.push(flightResult);
      } else {
        this.flightsByStops.twoOrMoreStops.push(flightResult);
      }
    });

    // Sort by price within each category
    this.flightsByStops.nonStop.sort((a, b) => a.price - b.price);
    this.flightsByStops.oneStop.sort((a, b) => a.price - b.price);
    this.flightsByStops.twoOrMoreStops.sort((a, b) => a.price - b.price);
    this.flights.sort((a, b) => a.price - b.price);

    // Update search summary
    this.searchSummary = this.getSearchSummary();
  }

  private updateFlightsList(): void {
    // This method is called after retrySearch to update the flights list
    this.processSearchResults();
  }

  private convertToFlightResult(flight: Flight): FlightResult {
    const firstItem = flight.items[0];
    const firstOffer = flight.offers[0];

    return {
      airline: firstItem?.airline?.name || 'Unknown',
      airlineLogo: this.getAirlineLogo(firstItem?.airline?.id || ''),
      price: firstOffer?.price?.amount || 0,
      currency: firstOffer?.price?.currency || 'TND',
      stops: firstItem?.stopCount || 0,
      duration: this.formatDuration(firstItem?.duration || 0),
      departureTime: this.extractTimeFromDate(firstItem?.flightDate || ''),
      arrivalTime: this.extractTimeFromDate(firstItem?.flightDate || ''),
      departureDate: this.extractDateFromDate(firstItem?.flightDate || ''),
      arrivalDate: this.extractDateFromDate(firstItem?.flightDate || ''),
      departureAirport: firstItem?.departure?.airport?.code || firstItem?.departure?.city?.name || '',
      arrivalAirport: firstItem?.arrival?.airport?.code || firstItem?.arrival?.city?.name || '',
      flightNumber: firstItem?.flightNo || '',
      aircraftType: firstItem?.flightClass?.name || 'Economy Standard',
      availableSeats: firstOffer?.seatInfo?.availableSeats?.toString() + 'B' || '32B',
      baggageAllowance: this.getBaggageAllowance(firstOffer?.baggageInformations || []),
      refundable: this.isRefundable(firstOffer?.services || []),
      restricted: false
    };
  }

  private getAirlineLogo(airlineCode: string): string {
    const airline = this.airlineFilters.find(a => a.code === airlineCode);
    return airline?.logo || 'assets/airlines/default.png';
  }

  private formatDuration(durationMinutes: number): string {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return `${hours}h ${minutes}m`;
  }

  private formatTime(timeString: string): string {
    // Simple time formatting - in real app, this would parse actual time
    return timeString || '--:--';
  }

  private extractTimeFromDate(dateString: string): string {
    if (!dateString) return '--:--';
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    } catch {
      return '--:--';
    }
  }

  private extractDateFromDate(dateString: string): string {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return '';
    }
  }

  private getBaggageAllowance(baggageInfo: any[]): string {
    if (!baggageInfo || baggageInfo.length === 0) return '2PC';
    const firstBaggage = baggageInfo[0];
    if (firstBaggage.piece) {
      return `${firstBaggage.piece}PC`;
    }
    if (firstBaggage.weight) {
      return `${firstBaggage.weight}KG`;
    }
    return '2PC';
  }

  private isRefundable(services: any[]): boolean {
    if (!services) return false;
    return services.some(service =>
      service.name?.toLowerCase().includes('refund') ||
      service.id?.toLowerCase().includes('refund')
    );
  }

  /**
   * Get search summary for display
   */
  getSearchSummary(): string {
    if (!this.originalSearchParams) return '';

    const from = this.originalSearchParams.from;
    const to = this.originalSearchParams.to;
    const date = new Date(this.originalSearchParams.departureDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    const adults = this.originalSearchParams.passengers?.adults || 1;

    return `${from} - ${to}, ${date}, ${adults}Adult(s)`;
  }

  /**
   * Format date for display
   */
  formatDate(dateString: string): string {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  }

  /**
   * Modify search - navigate back to search page
   */
  modifySearch(): void {
    this.router.navigate(['/flights']);
  }

  /**
   * Retry search with same parameters
   */
  retrySearch(): void {
    if (this.originalSearchParams) {
      this.isLoading = true;
      this.error = null;

      this.flightService.searchOneWayFlights(this.originalSearchParams).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.header.success) {
            this.searchResults = response;
            this.processSearchResults();
            this.updateFlightsList();
          } else {
            this.error = 'Search failed. Please try again.';
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.error = 'An error occurred while searching. Please try again.';
          console.error('Search error:', error);
        }
      });
    }
  }

  /**
   * Toggle airline filter
   */
  toggleAirlineFilter(airline: AirlineFilter): void {
    airline.active = !airline.active;
    // In a real app, this would filter the results
  }

  /**
   * Perform new search
   */
  onNewSearch(): void {
    this.router.navigate(['/flights']);
  }

  /**
   * Search again with current form values
   */
  onSearchAgain(): void {
    if (this.searchForm.valid) {
      this.isLoading = true;

      const formValue = this.searchForm.value;
      const searchForm: FlightSearchForm = {
        tripType: 'oneWay',
        from: formValue.from,
        to: formValue.to,
        departureDate: formValue.departureDate,
        passengers: {
          adults: formValue.adults,
          children: formValue.children,
          infants: formValue.infants
        },
        class: 'economy',
        directFlights: false,
        refundableFares: false,
        baggage: 'all',
        calendar: false
      };

      this.flightService.searchOneWayFlights(searchForm).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.header.success) {
            this.searchResults = response;
            this.originalSearchParams = searchForm;
            this.processSearchResults();
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Search error:', error);
        }
      });
    }
  }

  /**
   * Get flights for a specific stop category
   */
  getFlightsForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult[] {
    return this.flightsByStops[stopType];
  }

  /**
   * Get minimum price for a stop category
   */
  getMinPriceForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): number | null {
    const flights = this.flightsByStops[stopType];
    if (flights.length === 0) return null;
    return Math.min(...flights.map(f => f.price));
  }

  /**
   * Format price display
   */
  formatPrice(price: number, currency: string = 'TND'): string {
    return `${price} ${currency}`;
  }

  /**
   * Get stop label
   */
  getStopLabel(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): string {
    switch (stopType) {
      case 'nonStop': return 'Non Stop';
      case 'oneStop': return '1 Stop';
      case 'twoOrMoreStops': return '2+ Stops';
      default: return '';
    }
  }

  /**
   * Get flight for specific airline and stop type
   */
  getFlightForAirlineAndStops(airlineCode: string, stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult | null {
    const flights = this.flightsByStops[stopType];
    return flights.find(flight => flight.airline.includes(airlineCode)) || null;
  }
}
