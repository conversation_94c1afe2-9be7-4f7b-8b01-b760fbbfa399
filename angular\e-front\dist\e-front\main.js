"use strict";
(self["webpackChunke_front"] = self["webpackChunke_front"] || []).push([["main"],{

/***/ 4114:
/*!***************************************!*\
  !*** ./src/app/app-routing.module.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppRoutingModule: () => (/* binding */ AppRoutingModule)
/* harmony export */ });
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _components_signin_signin_component__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/signin/signin.component */ 423);
/* harmony import */ var _components_dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/dashboard/dashboard.component */ 4441);
/* harmony import */ var _components_flight_flight_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/flight/flight.component */ 2995);
/* harmony import */ var _components_flight_results_flight_results_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/flight-results/flight-results.component */ 9673);
/* harmony import */ var _guards_auth_guard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./guards/auth.guard */ 1620);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/core */ 7580);








const routes = [{
  path: '',
  redirectTo: '/signin',
  pathMatch: 'full'
}, {
  path: 'signin',
  component: _components_signin_signin_component__WEBPACK_IMPORTED_MODULE_0__.SigninComponent
}, {
  path: 'dashboard',
  component: _components_dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_1__.DashboardComponent,
  canActivate: [_guards_auth_guard__WEBPACK_IMPORTED_MODULE_4__.AuthGuard]
}, {
  path: 'flights',
  component: _components_flight_flight_component__WEBPACK_IMPORTED_MODULE_2__.FlightComponent,
  canActivate: [_guards_auth_guard__WEBPACK_IMPORTED_MODULE_4__.AuthGuard]
}, {
  path: 'flight-results',
  component: _components_flight_results_flight_results_component__WEBPACK_IMPORTED_MODULE_3__.FlightResultsComponent,
  canActivate: [_guards_auth_guard__WEBPACK_IMPORTED_MODULE_4__.AuthGuard]
}, {
  path: '**',
  redirectTo: '/signin'
}];
class AppRoutingModule {
  static {
    this.ɵfac = function AppRoutingModule_Factory(t) {
      return new (t || AppRoutingModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineNgModule"]({
      type: AppRoutingModule
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵdefineInjector"]({
      imports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule.forRoot(routes), _angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_5__["ɵɵsetNgModuleScope"](AppRoutingModule, {
    imports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule],
    exports: [_angular_router__WEBPACK_IMPORTED_MODULE_6__.RouterModule]
  });
})();

/***/ }),

/***/ 92:
/*!**********************************!*\
  !*** ./src/app/app.component.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppComponent: () => (/* binding */ AppComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/router */ 5072);


class AppComponent {
  constructor() {
    this.title = 'e-front';
  }
  static {
    this.ɵfac = function AppComponent_Factory(t) {
      return new (t || AppComponent)();
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵdefineComponent"]({
      type: AppComponent,
      selectors: [["app-root"]],
      decls: 1,
      vars: 0,
      template: function AppComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_0__["ɵɵelement"](0, "router-outlet");
        }
      },
      dependencies: [_angular_router__WEBPACK_IMPORTED_MODULE_1__.RouterOutlet],
      styles: ["/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */"]
    });
  }
}

/***/ }),

/***/ 635:
/*!*******************************!*\
  !*** ./src/app/app.module.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppModule: () => (/* binding */ AppModule)
/* harmony export */ });
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var _app_routing_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app-routing.module */ 4114);
/* harmony import */ var _app_component__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./app.component */ 92);
/* harmony import */ var _components_signin_signin_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/signin/signin.component */ 423);
/* harmony import */ var _components_dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/dashboard/dashboard.component */ 4441);
/* harmony import */ var _components_flight_flight_component__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/flight/flight.component */ 2995);
/* harmony import */ var _components_flight_results_flight_results_component__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/flight-results/flight-results.component */ 9673);
/* harmony import */ var _components_shared_autocomplete_autocomplete_component__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/shared/autocomplete/autocomplete.component */ 6161);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/core */ 7580);











class AppModule {
  static {
    this.ɵfac = function AppModule_Factory(t) {
      return new (t || AppModule)();
    };
  }
  static {
    this.ɵmod = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineNgModule"]({
      type: AppModule,
      bootstrap: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent]
    });
  }
  static {
    this.ɵinj = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵdefineInjector"]({
      imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_8__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule]
    });
  }
}
(function () {
  (typeof ngJitMode === "undefined" || ngJitMode) && _angular_core__WEBPACK_IMPORTED_MODULE_7__["ɵɵsetNgModuleScope"](AppModule, {
    declarations: [_app_component__WEBPACK_IMPORTED_MODULE_1__.AppComponent, _components_signin_signin_component__WEBPACK_IMPORTED_MODULE_2__.SigninComponent, _components_dashboard_dashboard_component__WEBPACK_IMPORTED_MODULE_3__.DashboardComponent, _components_flight_flight_component__WEBPACK_IMPORTED_MODULE_4__.FlightComponent, _components_flight_results_flight_results_component__WEBPACK_IMPORTED_MODULE_5__.FlightResultsComponent, _components_shared_autocomplete_autocomplete_component__WEBPACK_IMPORTED_MODULE_6__.AutocompleteComponent],
    imports: [_angular_platform_browser__WEBPACK_IMPORTED_MODULE_8__.BrowserModule, _app_routing_module__WEBPACK_IMPORTED_MODULE_0__.AppRoutingModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.ReactiveFormsModule, _angular_forms__WEBPACK_IMPORTED_MODULE_9__.FormsModule, _angular_common_http__WEBPACK_IMPORTED_MODULE_10__.HttpClientModule]
  });
})();

/***/ }),

/***/ 4441:
/*!*************************************************************!*\
  !*** ./src/app/components/dashboard/dashboard.component.ts ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardComponent: () => (/* binding */ DashboardComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common */ 316);




function DashboardComponent_p_7_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "p", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("Welcome back, ", ctx_r0.currentUser.name, "");
  }
}
function DashboardComponent_div_22_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 43)(1, "div", 44)(2, "span", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "span", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5, "Balance: 8500.48 TND");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "span", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7, "Due: -1399.52 TND");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"]("", ctx_r1.currentUser.agencyCode, " - Workspace for Demo");
  }
}
class DashboardComponent {
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
    this.currentUser = null;
  }
  ngOnInit() {
    // Subscribe to current user
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/signin']);
    }
  }
  /**
   * Navigate to flights page
   */
  navigateToFlights() {
    this.router.navigate(['/flights']);
  }
  /**
   * Logout user
   */
  logout() {
    this.authService.logout();
    this.router.navigate(['/signin']);
  }
  static {
    this.ɵfac = function DashboardComponent_Factory(t) {
      return new (t || DashboardComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_2__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: DashboardComponent,
      selectors: [["app-dashboard"]],
      decls: 79,
      vars: 2,
      consts: [[1, "dashboard-container"], [1, "dashboard-header"], [1, "header-content"], [1, "header-left"], [1, "brand-logo"], [1, "logo-text"], ["class", "dashboard-subtitle", 4, "ngIf"], [1, "header-right"], [1, "top-nav"], ["href", "#", 1, "nav-link"], [1, "fas", "fa-home"], [1, "fas", "fa-wrench"], [1, "fas", "fa-globe"], [1, "logout-button", 3, "click"], [1, "fas", "fa-sign-out-alt"], ["class", "user-info", 4, "ngIf"], [1, "dashboard-main"], [1, "navigation-grid"], [1, "row"], [1, "nav-card", "booking-queue"], [1, "card-icon"], [1, "fas", "fa-clipboard-list"], [1, "card-title"], [1, "nav-card", "commissions"], [1, "fas", "fa-percentage"], [1, "nav-card", "sub-agent"], [1, "fas", "fa-users"], [1, "nav-card", "profile"], [1, "fas", "fa-user"], [1, "nav-card", "finance"], [1, "fas", "fa-money-bill-wave"], [1, "nav-card", "agency-profile"], [1, "fas", "fa-building"], [1, "row", "featured-row"], [1, "nav-card", "featured", "flights", 3, "click"], [1, "fas", "fa-plane"], [1, "nav-card", "flight-info"], [1, "fas", "fa-info-circle"], [1, "nav-card", "passengers"], [1, "fas", "fa-user-friends"], [1, "nav-card", "credit-request"], [1, "fas", "fa-credit-card"], [1, "dashboard-subtitle"], [1, "user-info"], [1, "agency-info"], [1, "agency-id"], [1, "balance"], [1, "due-info"]],
      template: function DashboardComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "header", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4)(5, "span", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "BLOCK TO BOOK");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](7, DashboardComponent_p_7_Template, 2, 1, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "div", 7)(9, "nav", 8)(10, "a", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](11, "i", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](12, " Home");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](13, "a", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](14, "i", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](15, " Tools");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](16, "a", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](17, "i", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](18, " Languages");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](19, "button", 13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function DashboardComponent_Template_button_click_19_listener() {
            return ctx.logout();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](20, "i", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](21, " Logout ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](22, DashboardComponent_div_22_Template, 8, 1, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](23, "main", 16)(24, "div", 17)(25, "div", 18)(26, "div", 19)(27, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](28, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](30, "Booking Queue");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](31, "div", 23)(32, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](33, "i", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](35, "Commissions");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](36, "div", 25)(37, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](38, "i", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](39, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](40, "Sub Agent");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](41, "div", 18)(42, "div", 27)(43, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](44, "i", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](45, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](46, "Profile");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](47, "div", 29)(48, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](49, "i", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](50, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](51, "Finance");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](52, "div", 31)(53, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](54, "i", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](55, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](56, "Agency Profile");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](57, "div", 33)(58, "div", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function DashboardComponent_Template_div_click_58_listener() {
            return ctx.navigateToFlights();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](59, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](60, "i", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](61, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](62, "Flights");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](63, "div", 36)(64, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](65, "i", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](66, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](67, "Flight Info");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](68, "div", 18)(69, "div", 38)(70, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](71, "i", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](72, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](73, "Passengers");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](74, "div", 40)(75, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](76, "i", 41);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](77, "span", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](78, "Credit Request");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.currentUser);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.currentUser);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_3__.NgIf],
      styles: ["\n\n\n\n\n[_ngcontent-%COMP%]:root {\n  --primary-color: #4a63a2; \n\n  --primary-color-dark: #3a4f82; \n\n  --secondary-color: #5b9bd5; \n\n  --accent-color: #3cc7b7; \n\n  --light-bg: #f8f9fa;\n  --dark-text: #333;\n  --light-text: #fff;\n  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  --transition-speed: 0.3s;\n}\n\n\n\n.dashboard-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background-color: var(--light-bg);\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n\n\n.dashboard-header[_ngcontent-%COMP%] {\n  background: linear-gradient(90deg, var(--primary-color), var(--primary-color-dark));\n  color: var(--light-text);\n  padding: 15px 20px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .header-left[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  font-weight: bold;\n  margin-right: 20px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .brand-logo[_ngcontent-%COMP%]   .logo-text[_ngcontent-%COMP%] {\n  font-size: 20px;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .dashboard-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: 14px;\n  opacity: 0.9;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  color: var(--light-text);\n  text-decoration: none;\n  padding: 6px 12px;\n  border-radius: 4px;\n  transition: background-color var(--transition-speed);\n  font-size: 14px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  margin-right: 6px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%] {\n  background-color: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: var(--light-text);\n  padding: 6px 12px;\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  transition: all var(--transition-speed);\n  font-size: 14px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  margin-right: 6px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\n  background-color: rgba(0, 0, 0, 0.1);\n  padding: 10px 20px;\n  margin-top: 15px;\n  border-radius: 4px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 25px;\n  align-items: center;\n  font-size: 14px;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .agency-id[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .balance[_ngcontent-%COMP%] {\n  color: var(--accent-color);\n}\n\n.dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%]   .due-info[_ngcontent-%COMP%] {\n  color: #ff9f43; \n\n}\n\n\n\n.dashboard-main[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 20px;\n  max-width: 1400px;\n  margin: 0 auto;\n  width: 100%;\n}\n\n\n\n.navigation-grid[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.navigation-grid[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.navigation-grid[_ngcontent-%COMP%]   .row.featured-row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\n  flex: 1;\n  min-height: 180px;\n}\n\n\n\n.nav-card[_ngcontent-%COMP%] {\n  background-color: white;\n  border-radius: 10px;\n  box-shadow: var(--card-shadow);\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 150px;\n  flex: 1;\n  transition: all var(--transition-speed);\n  min-height: 140px;\n  text-align: center;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n\n.nav-card[_ngcontent-%COMP%]::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 5px;\n  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));\n  opacity: 0.7;\n}\n\n.nav-card[_ngcontent-%COMP%]:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);\n}\n\n.nav-card[_ngcontent-%COMP%]:hover::before {\n  opacity: 1;\n}\n\n.nav-card[_ngcontent-%COMP%]:hover   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  transform: scale(1.1);\n}\n\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  font-size: 36px;\n  margin-bottom: 15px;\n  color: var(--primary-color);\n  height: 50px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  transition: transform var(--transition-speed);\n}\n\n.nav-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: var(--dark-text);\n}\n\n\n\n.nav-card.booking-queue[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #4a63a2;\n}\n\n.nav-card.commissions[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #5b9bd5;\n}\n\n.nav-card.sub-agent[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #ff9f43;\n}\n\n.nav-card.profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #6c757d;\n}\n\n.nav-card.finance[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #28c76f;\n}\n\n.nav-card.agency-profile[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #7367f0;\n}\n\n.nav-card.flights[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #4a63a2;\n}\n\n.nav-card.flight-info[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #5b9bd5;\n}\n\n.nav-card.passengers[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #7367f0;\n}\n\n.nav-card.credit-request[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  color: #28c76f;\n}\n\n.nav-card.featured[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.18);\n}\n\n.nav-card.featured[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\n  font-size: 48px;\n}\n\n.nav-card.featured[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  font-size: 18px;\n}\n\n\n\n@media (max-width: 1200px) {\n  .row[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n  }\n  \n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\n    min-width: calc(33.333% - 20px);\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 15px;\n  }\n\n  .dashboard-header[_ngcontent-%COMP%]   .top-nav[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n  }\n\n  .dashboard-header[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   .agency-info[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\n    min-width: calc(50% - 10px);\n  }\n}\n\n@media (max-width: 576px) {\n  .row[_ngcontent-%COMP%]   .nav-card[_ngcontent-%COMP%] {\n    min-width: 100%;\n  }\n}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 9673:
/*!***********************************************************************!*\
  !*** ./src/app/components/flight-results/flight-results.component.ts ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlightResultsComponent: () => (/* binding */ FlightResultsComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _services_flight_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/flight.service */ 2228);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function FlightResultsComponent_span_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "SEARCH AGAIN");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function FlightResultsComponent_span_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, " Searching... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function FlightResultsComponent_button_37_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "button", 42);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function FlightResultsComponent_button_37_Template_button_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r10);
      const airline_r8 = restoredCtx.$implicit;
      const ctx_r9 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r9.toggleAirlineFilter(airline_r8));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 43)(2, "span", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const airline_r8 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("active", airline_r8.active);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](airline_r8.code);
  }
}
function FlightResultsComponent_div_41_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 33)(1, "div", 34)(2, "span", 35);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const airline_r11 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](airline_r11.code);
  }
}
function FlightResultsComponent_div_46_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 37)(2, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5, "TND");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const flight_r15 = ctx.ngIf;
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r13.formatPrice(flight_r15.price));
  }
}
function FlightResultsComponent_div_46_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "-");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function FlightResultsComponent_div_46_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, FlightResultsComponent_div_46_ng_container_1_Template, 6, 1, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, FlightResultsComponent_div_46_ng_container_2_Template, 3, 0, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const airline_r12 = ctx.$implicit;
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r4.getFlightForAirlineAndStops(airline_r12.code, "nonStop"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx_r4.getFlightForAirlineAndStops(airline_r12.code, "nonStop"));
  }
}
function FlightResultsComponent_div_51_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 37)(2, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5, "TND");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const flight_r19 = ctx.ngIf;
    const ctx_r17 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r17.formatPrice(flight_r19.price));
  }
}
function FlightResultsComponent_div_51_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "-");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function FlightResultsComponent_div_51_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, FlightResultsComponent_div_51_ng_container_1_Template, 6, 1, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, FlightResultsComponent_div_51_ng_container_2_Template, 3, 0, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const airline_r16 = ctx.$implicit;
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r5.getFlightForAirlineAndStops(airline_r16.code, "oneStop"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx_r5.getFlightForAirlineAndStops(airline_r16.code, "oneStop"));
  }
}
function FlightResultsComponent_div_56_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 37)(2, "span", 38);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "span", 39);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5, "TND");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
  if (rf & 2) {
    const flight_r23 = ctx.ngIf;
    const ctx_r21 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r21.formatPrice(flight_r23.price));
  }
}
function FlightResultsComponent_div_56_ng_container_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerStart"](0);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](2, "-");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementContainerEnd"]();
  }
}
function FlightResultsComponent_div_56_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 36);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](1, FlightResultsComponent_div_56_ng_container_1_Template, 6, 1, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, FlightResultsComponent_div_56_ng_container_2_Template, 3, 0, "ng-container", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const airline_r20 = ctx.$implicit;
    const ctx_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r6.getFlightForAirlineAndStops(airline_r20.code, "twoOrMoreStops"));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx_r6.getFlightForAirlineAndStops(airline_r20.code, "twoOrMoreStops"));
  }
}
function FlightResultsComponent_div_57_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 45)(1, "div", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "i", 47);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "h3");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](4, "No flights found");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](6, "Try adjusting your search criteria");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
}
class FlightResultsComponent {
  constructor(router, route, fb, flightService) {
    this.router = router;
    this.route = route;
    this.fb = fb;
    this.flightService = flightService;
    this.searchResults = null;
    this.isLoading = false;
    this.originalSearchParams = null;
    // Filter options
    this.airlineFilters = [{
      code: 'TK',
      name: 'Turkish Airlines',
      logo: 'assets/airlines/turkish-airlines.png',
      active: false
    }, {
      code: 'LH',
      name: 'Lufthansa',
      logo: 'assets/airlines/lufthansa.png',
      active: false
    }, {
      code: 'EY',
      name: 'Etihad',
      logo: 'assets/airlines/etihad.png',
      active: false
    }, {
      code: 'MS',
      name: 'EgyptAir',
      logo: 'assets/airlines/egyptair.png',
      active: false
    }, {
      code: 'AH',
      name: 'Air Algerie',
      logo: 'assets/airlines/air-algerie.png',
      active: false
    }, {
      code: 'TU',
      name: 'Tunisair',
      logo: 'assets/airlines/tunisair.png',
      active: false
    }];
    // Organized flight results by stops
    this.flightsByStops = {
      nonStop: [],
      oneStop: [],
      twoOrMoreStops: []
    };
    this.searchForm = this.createSearchForm();
  }
  ngOnInit() {
    // Get search results from navigation state or route params
    const navigation = this.router.getCurrentNavigation();
    if (navigation?.extras.state?.['results']) {
      this.searchResults = navigation.extras.state['results'];
      this.originalSearchParams = navigation.extras.state['searchParams'];
      this.processSearchResults();
    } else {
      // If no results in state, redirect back to search
      this.router.navigate(['/flights']);
    }
    // Initialize form with original search parameters
    if (this.originalSearchParams) {
      this.initializeSearchForm();
    }
  }
  createSearchForm() {
    return this.fb.group({
      from: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      to: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      departureDate: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required],
      adults: [1, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(1)]],
      children: [0, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0)]],
      infants: [0, [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.min(0)]]
    });
  }
  initializeSearchForm() {
    if (this.originalSearchParams) {
      this.searchForm.patchValue({
        from: this.originalSearchParams.from,
        to: this.originalSearchParams.to,
        departureDate: this.originalSearchParams.departureDate,
        adults: this.originalSearchParams.passengers?.adults || 1,
        children: this.originalSearchParams.passengers?.children || 0,
        infants: this.originalSearchParams.passengers?.infants || 0
      });
    }
  }
  processSearchResults() {
    if (!this.searchResults?.body?.flights) {
      return;
    }
    // Reset flight arrays
    this.flightsByStops.nonStop = [];
    this.flightsByStops.oneStop = [];
    this.flightsByStops.twoOrMoreStops = [];
    // Process each flight and categorize by stops
    this.searchResults.body.flights.forEach(flight => {
      const flightResult = this.convertToFlightResult(flight);
      // Get stop count from the first flight item
      const stopCount = flight.items[0]?.stopCount || 0;
      if (stopCount === 0) {
        this.flightsByStops.nonStop.push(flightResult);
      } else if (stopCount === 1) {
        this.flightsByStops.oneStop.push(flightResult);
      } else {
        this.flightsByStops.twoOrMoreStops.push(flightResult);
      }
    });
    // Sort by price within each category
    this.flightsByStops.nonStop.sort((a, b) => a.price - b.price);
    this.flightsByStops.oneStop.sort((a, b) => a.price - b.price);
    this.flightsByStops.twoOrMoreStops.sort((a, b) => a.price - b.price);
  }
  convertToFlightResult(flight) {
    const firstItem = flight.items[0];
    const firstOffer = flight.offers[0];
    return {
      airline: firstItem?.airline?.name || 'Unknown',
      airlineLogo: this.getAirlineLogo(firstItem?.airline?.code || ''),
      price: firstOffer?.price?.amount || 0,
      currency: firstOffer?.price?.currency || 'TND',
      stops: firstItem?.stopCount || 0,
      duration: this.formatDuration(firstItem?.duration || 0),
      departureTime: this.formatTime(firstItem?.departure?.code || ''),
      arrivalTime: this.formatTime(firstItem?.arrival?.code || ''),
      flightNumber: firstItem?.flightNo || ''
    };
  }
  getAirlineLogo(airlineCode) {
    const airline = this.airlineFilters.find(a => a.code === airlineCode);
    return airline?.logo || 'assets/airlines/default.png';
  }
  formatDuration(durationMinutes) {
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;
    return `${hours}h ${minutes}m`;
  }
  formatTime(timeString) {
    // Simple time formatting - in real app, this would parse actual time
    return timeString || '--:--';
  }
  /**
   * Get search summary for display
   */
  getSearchSummary() {
    if (!this.originalSearchParams) return '';
    const from = this.originalSearchParams.from;
    const to = this.originalSearchParams.to;
    const date = new Date(this.originalSearchParams.departureDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    const adults = this.originalSearchParams.passengers?.adults || 1;
    return `${from} - ${to}, ${date}, ${adults}Adult(s)`;
  }
  /**
   * Toggle airline filter
   */
  toggleAirlineFilter(airline) {
    airline.active = !airline.active;
    // In a real app, this would filter the results
  }
  /**
   * Perform new search
   */
  onNewSearch() {
    this.router.navigate(['/flights']);
  }
  /**
   * Search again with current form values
   */
  onSearchAgain() {
    if (this.searchForm.valid) {
      this.isLoading = true;
      const formValue = this.searchForm.value;
      const searchForm = {
        tripType: 'oneWay',
        from: formValue.from,
        to: formValue.to,
        departureDate: formValue.departureDate,
        passengers: {
          adults: formValue.adults,
          children: formValue.children,
          infants: formValue.infants
        },
        class: 'economy',
        directFlights: false,
        refundableFares: false,
        baggage: 'all',
        calendar: false
      };
      this.flightService.searchOneWayFlights(searchForm).subscribe({
        next: response => {
          this.isLoading = false;
          if (response.header.success) {
            this.searchResults = response;
            this.originalSearchParams = searchForm;
            this.processSearchResults();
          }
        },
        error: error => {
          this.isLoading = false;
          console.error('Search error:', error);
        }
      });
    }
  }
  /**
   * Get flights for a specific stop category
   */
  getFlightsForStops(stopType) {
    return this.flightsByStops[stopType];
  }
  /**
   * Get minimum price for a stop category
   */
  getMinPriceForStops(stopType) {
    const flights = this.flightsByStops[stopType];
    if (flights.length === 0) return null;
    return Math.min(...flights.map(f => f.price));
  }
  /**
   * Format price display
   */
  formatPrice(price, currency = 'TND') {
    return `${price} ${currency}`;
  }
  /**
   * Get stop label
   */
  getStopLabel(stopType) {
    switch (stopType) {
      case 'nonStop':
        return 'Non Stop';
      case 'oneStop':
        return '1 Stop';
      case 'twoOrMoreStops':
        return '2+ Stops';
      default:
        return '';
    }
  }
  /**
   * Get flight for specific airline and stop type
   */
  getFlightForAirlineAndStops(airlineCode, stopType) {
    const flights = this.flightsByStops[stopType];
    return flights.find(flight => flight.airline.includes(airlineCode)) || null;
  }
  static {
    this.ɵfac = function FlightResultsComponent_Factory(t) {
      return new (t || FlightResultsComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.ActivatedRoute), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_flight_service__WEBPACK_IMPORTED_MODULE_0__.FlightService));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: FlightResultsComponent,
      selectors: [["app-flight-results"]],
      decls: 178,
      vars: 11,
      consts: [[1, "flight-results-container"], [1, "search-summary-header"], [1, "search-title"], [1, "search-modification-panel"], [1, "search-form", 3, "formGroup"], [1, "form-row"], [1, "form-group"], ["type", "text", "formControlName", "from", "placeholder", "IST - Istanbul Airport", 1, "form-control"], ["type", "text", "formControlName", "to", "placeholder", "TUN - Carthage Arpt", 1, "form-control"], [1, "date-input-wrapper"], ["type", "date", "formControlName", "departureDate", 1, "form-control", "date-input"], [1, "date-controls"], ["type", "button", 1, "date-btn"], [1, "action-buttons"], ["type", "button", 1, "btn", "btn-secondary", "email-btn"], [1, "fas", "fa-envelope"], ["type", "button", 1, "btn", "btn-primary", "new-search-btn", 3, "click"], ["type", "button", 1, "btn", "btn-primary", "search-again-btn", 3, "disabled", "click"], [4, "ngIf"], [1, "results-section"], [1, "airline-filters"], [1, "filter-tabs"], ["class", "airline-tab", 3, "active", "click", 4, "ngFor", "ngForOf"], [1, "flight-results-grid"], [1, "grid-header"], [1, "stop-category-header"], ["class", "airline-header", 4, "ngFor", "ngForOf"], [1, "grid-row"], [1, "stop-category"], [1, "stop-label"], ["class", "price-cell", 4, "ngFor", "ngForOf"], ["class", "flight-results-list", 4, "ngIf"], [1, "sample-price-grid"], [1, "airline-header"], [1, "airline-logo-placeholder-small"], [1, "airline-code-small"], [1, "price-cell"], [1, "price-info"], [1, "price"], [1, "currency"], [1, "no-flight"], [1, "fas", "fa-spinner", "fa-spin"], [1, "airline-tab", 3, "click"], [1, "airline-logo-placeholder"], [1, "airline-code"], [1, "flight-results-list"], [1, "no-results"], [1, "fas", "fa-plane"]],
      template: function FlightResultsComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "h1", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "div", 3)(5, "form", 4)(6, "div", 5)(7, "div", 6)(8, "label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9, "From");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](10, "input", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "div", 6)(12, "label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](13, "To");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](14, "input", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "div", 6)(16, "label");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](17, "Departure on");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](18, "div", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "input", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](20, "div", 11)(21, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](22, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](23, "button", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](24, "+");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](25, "div", 13)(26, "button", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](27, "i", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](28, " Email Itineraries ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "button", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function FlightResultsComponent_Template_button_click_29_listener() {
            return ctx.onNewSearch();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](30, " NEW SEARCH ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](31, "button", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function FlightResultsComponent_Template_button_click_31_listener() {
            return ctx.onSearchAgain();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](32, FlightResultsComponent_span_32_Template, 2, 0, "span", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](33, FlightResultsComponent_span_33_Template, 3, 0, "span", 18);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](34, "div", 19)(35, "div", 20)(36, "div", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](37, FlightResultsComponent_button_37_Template, 4, 3, "button", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](38, "div", 23)(39, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](40, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](41, FlightResultsComponent_div_41_Template, 4, 1, "div", 26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](42, "div", 27)(43, "div", 28)(44, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](45, "Non Stop");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](46, FlightResultsComponent_div_46_Template, 3, 2, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](47, "div", 27)(48, "div", 28)(49, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](50, "1 Stop");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](51, FlightResultsComponent_div_51_Template, 3, 2, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](52, "div", 27)(53, "div", 28)(54, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](55, "2+ Stops");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](56, FlightResultsComponent_div_56_Template, 3, 2, "div", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](57, FlightResultsComponent_div_57_Template, 7, 0, "div", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](58, "div", 32)(59, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](60, "div", 25);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](61, "div", 33)(62, "div", 34)(63, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](64, "TK");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](65, "div", 33)(66, "div", 34)(67, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](68, "LH");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](69, "div", 33)(70, "div", 34)(71, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](72, "EY");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](73, "div", 33)(74, "div", 34)(75, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](76, "MS");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](77, "div", 33)(78, "div", 34)(79, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](80, "AH");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](81, "div", 33)(82, "div", 34)(83, "span", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](84, "TU");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](85, "div", 27)(86, "div", 28)(87, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](88, "Non Stop");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](89, "div", 36)(90, "div", 37)(91, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](92, "531");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](93, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](94, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](95, "div", 36)(96, "div", 37)(97, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](98, "555");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](99, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](100, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](101, "div", 36)(102, "div", 37)(103, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](104, "684");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](105, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](106, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](107, "div", 36)(108, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](109, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](110, "div", 36)(111, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](112, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](113, "div", 36)(114, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](115, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](116, "div", 27)(117, "div", 28)(118, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](119, "1 Stop");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](120, "div", 36)(121, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](122, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](123, "div", 36)(124, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](125, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](126, "div", 36)(127, "div", 37)(128, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](129, "710");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](130, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](131, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](132, "div", 36)(133, "div", 37)(134, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](135, "1047");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](136, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](137, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](138, "div", 36)(139, "div", 37)(140, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](141, "1062");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](142, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](143, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](144, "div", 36)(145, "div", 37)(146, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](147, "1220");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](148, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](149, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](150, "div", 27)(151, "div", 28)(152, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](153, "2+ Stops");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](154, "div", 36)(155, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](156, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](157, "div", 36)(158, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](159, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](160, "div", 36)(161, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](162, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](163, "div", 36)(164, "div", 37)(165, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](166, "1372");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](167, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](168, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](169, "div", 36)(170, "div", 37)(171, "span", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](172, "1855");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](173, "span", 39);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](174, "TND");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](175, "div", 36)(176, "div", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](177, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()()()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx.getSearchSummary());
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.searchForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](26);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.airlineFilters);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.airlineFilters);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.airlineFilters);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.airlineFilters);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx.airlineFilters);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", (ctx.searchResults == null ? null : ctx.searchResults.body == null ? null : ctx.searchResults.body.flights == null ? null : ctx.searchResults.body.flights.length) === 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName],
      styles: ["\n\n\n.flight-results-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n\n\n.search-summary-header[_ngcontent-%COMP%] {\n  background: white;\n  padding: 20px 30px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.search-title[_ngcontent-%COMP%] {\n  font-size: 24px;\n  font-weight: 600;\n  color: #333;\n  margin: 0;\n}\n\n\n\n.search-modification-panel[_ngcontent-%COMP%] {\n  background: white;\n  padding: 25px 30px;\n  border-radius: 8px;\n  margin-bottom: 20px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.search-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 20px;\n  align-items: end;\n  margin-bottom: 20px;\n}\n\n.form-group[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 14px;\n  font-weight: 500;\n  color: #666;\n  margin-bottom: 8px;\n}\n\n.form-control[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 12px 15px;\n  border: 2px solid #e0e0e0;\n  border-radius: 6px;\n  font-size: 14px;\n  transition: border-color 0.3s ease;\n}\n\n.form-control[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #4a90e2;\n}\n\n.date-input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.date-input[_ngcontent-%COMP%] {\n  flex: 1;\n  padding-right: 60px;\n}\n\n.date-controls[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 10px;\n  display: flex;\n  gap: 5px;\n}\n\n.date-btn[_ngcontent-%COMP%] {\n  width: 24px;\n  height: 24px;\n  border: 1px solid #ddd;\n  background: white;\n  border-radius: 4px;\n  font-size: 14px;\n  font-weight: bold;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.date-btn[_ngcontent-%COMP%]:hover {\n  background: #f0f0f0;\n}\n\n\n\n.action-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n.btn[_ngcontent-%COMP%] {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n}\n\n.btn-secondary[_ngcontent-%COMP%] {\n  background: #6c757d;\n  color: white;\n}\n\n.btn-secondary[_ngcontent-%COMP%]:hover {\n  background: #5a6268;\n}\n\n.btn-primary[_ngcontent-%COMP%] {\n  background: #4a90e2;\n  color: white;\n}\n\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: #357abd;\n}\n\n.btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.email-btn[_ngcontent-%COMP%] {\n  background: #28a745;\n}\n\n.email-btn[_ngcontent-%COMP%]:hover {\n  background: #218838;\n}\n\n\n\n.results-section[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n\n\n.airline-filters[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  padding: 20px 30px;\n  border-bottom: 1px solid #e0e0e0;\n}\n\n.filter-tabs[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n.airline-tab[_ngcontent-%COMP%] {\n  padding: 10px 15px;\n  border: 2px solid #e0e0e0;\n  background: white;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.airline-tab[_ngcontent-%COMP%]:hover {\n  border-color: #4a90e2;\n}\n\n.airline-tab.active[_ngcontent-%COMP%] {\n  border-color: #4a90e2;\n  background: #e3f2fd;\n}\n\n.airline-logo[_ngcontent-%COMP%] {\n  height: 30px;\n  width: auto;\n  object-fit: contain;\n}\n\n.airline-logo-placeholder[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 30px;\n  background: #f0f0f0;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #ddd;\n}\n\n.airline-code[_ngcontent-%COMP%] {\n  font-size: 10px;\n  font-weight: 600;\n  color: #666;\n}\n\n\n\n.flight-results-grid[_ngcontent-%COMP%], .sample-price-grid[_ngcontent-%COMP%] {\n  padding: 30px;\n}\n\n.grid-header[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 150px repeat(6, 1fr);\n  gap: 15px;\n  margin-bottom: 20px;\n  padding-bottom: 15px;\n  border-bottom: 2px solid #e0e0e0;\n}\n\n.stop-category-header[_ngcontent-%COMP%] {\n  \n\n}\n\n.airline-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 10px;\n  background: #f8f9fa;\n  border-radius: 6px;\n}\n\n.airline-logo-small[_ngcontent-%COMP%] {\n  height: 25px;\n  width: auto;\n  object-fit: contain;\n}\n\n.airline-logo-placeholder-small[_ngcontent-%COMP%] {\n  width: 35px;\n  height: 25px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: 1px solid #e0e0e0;\n}\n\n.airline-code-small[_ngcontent-%COMP%] {\n  font-size: 9px;\n  font-weight: 600;\n  color: #666;\n}\n\n.grid-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 150px repeat(6, 1fr);\n  gap: 15px;\n  margin-bottom: 15px;\n  align-items: center;\n}\n\n.stop-category[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 15px 0;\n}\n\n.stop-label[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-weight: 600;\n  color: #333;\n}\n\n.price-cell[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 15px 10px;\n  border: 1px solid #e0e0e0;\n  border-radius: 6px;\n  min-height: 60px;\n  background: white;\n  transition: all 0.3s ease;\n}\n\n.price-cell[_ngcontent-%COMP%]:hover {\n  border-color: #4a90e2;\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);\n  cursor: pointer;\n}\n\n.price-info[_ngcontent-%COMP%] {\n  text-align: center;\n}\n\n.price[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 18px;\n  font-weight: 700;\n  color: #333;\n  line-height: 1.2;\n}\n\n.currency[_ngcontent-%COMP%] {\n  display: block;\n  font-size: 12px;\n  color: #666;\n  margin-top: 2px;\n}\n\n.no-flight[_ngcontent-%COMP%] {\n  color: #999;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n\n\n.no-results[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 60px 20px;\n  color: #666;\n}\n\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 48px;\n  color: #ddd;\n  margin-bottom: 20px;\n}\n\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  font-size: 24px;\n  margin-bottom: 10px;\n  color: #333;\n}\n\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin: 0;\n}\n\n\n\n@media (max-width: 1200px) {\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\n    grid-template-columns: 120px repeat(6, 1fr);\n    gap: 10px;\n  }\n\n  .airline-logo-small[_ngcontent-%COMP%] {\n    height: 20px;\n  }\n\n  .price[_ngcontent-%COMP%] {\n    font-size: 16px;\n  }\n}\n\n@media (max-width: 992px) {\n  .search-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .action-buttons[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\n    grid-template-columns: 100px repeat(3, 1fr);\n  }\n\n  \n\n  .grid-header[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+5), .grid-row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+5) {\n    display: none;\n  }\n}\n\n@media (max-width: 768px) {\n  .flight-results-container[_ngcontent-%COMP%] {\n    padding: 15px;\n  }\n\n  .search-modification-panel[_ngcontent-%COMP%], .results-section[_ngcontent-%COMP%] {\n    padding: 20px 15px;\n  }\n\n  .search-title[_ngcontent-%COMP%] {\n    font-size: 20px;\n  }\n\n  .filter-tabs[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n\n  .airline-logo[_ngcontent-%COMP%] {\n    height: 25px;\n  }\n\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\n    grid-template-columns: 80px repeat(2, 1fr);\n    gap: 8px;\n  }\n\n  \n\n  .grid-header[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+4), .grid-row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+4) {\n    display: none;\n  }\n\n  .price-cell[_ngcontent-%COMP%] {\n    padding: 10px 5px;\n    min-height: 50px;\n  }\n\n  .price[_ngcontent-%COMP%] {\n    font-size: 14px;\n  }\n\n  .currency[_ngcontent-%COMP%] {\n    font-size: 11px;\n  }\n}\n\n\n\n.btn[_ngcontent-%COMP%]   .fa-spinner[_ngcontent-%COMP%] {\n  margin-right: 8px;\n}\n\n\n\n.price-cell[_ngcontent-%COMP%]:hover   .price[_ngcontent-%COMP%] {\n  color: #4a90e2;\n}\n\n.price-cell[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n}\n\n\n\n.form-control[_ngcontent-%COMP%]:focus {\n  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);\n}\n\n.btn[_ngcontent-%COMP%]:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);\n}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 2995:
/*!*******************************************************!*\
  !*** ./src/app/components/flight/flight.component.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlightComponent: () => (/* binding */ FlightComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_flight_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/flight.service */ 2228);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/common */ 316);
/* harmony import */ var _shared_autocomplete_autocomplete_component__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/autocomplete/autocomplete.component */ 6161);








function FlightComponent_div_24_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r0.getErrorMessage("from"), " ");
  }
}
function FlightComponent_div_27_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r1.getErrorMessage("to"), " ");
  }
}
function FlightComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r2.getErrorMessage("departureDate"), " ");
  }
}
function FlightComponent_div_33_div_10_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 70);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r13 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", ctx_r13.getErrorMessage("returnDate"), " ");
  }
}
function FlightComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 12)(1, "div", 13)(2, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](3, "app-autocomplete", 71);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "app-autocomplete", 72);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 18)(7, "div", 19);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](8, "input", 73)(9, "i", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](10, FlightComponent_div_33_div_10_Template, 2, 1, "div", 16);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    let tmp_0_0;
    let tmp_2_0;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", (tmp_0_0 = ctx_r3.flightForm.get("to")) == null ? null : tmp_0_0.value)("readonly", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", (tmp_2_0 = ctx_r3.flightForm.get("from")) == null ? null : tmp_2_0.value)("readonly", true);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx_r3.getErrorMessage("returnDate"));
  }
}
function FlightComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r17 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 12)(1, "div", 13)(2, "div", 14)(3, "app-autocomplete", 74);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngModelChange", function FlightComponent_div_34_Template_app_autocomplete_ngModelChange_3_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const segment_r14 = restoredCtx.$implicit;
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](segment_r14.from = $event);
    })("locationSelected", function FlightComponent_div_34_Template_app_autocomplete_locationSelected_3_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const i_r15 = restoredCtx.index;
      const ctx_r18 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r18.onSegmentLocationSelected($event, i_r15, "from"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "div", 14)(5, "app-autocomplete", 75);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngModelChange", function FlightComponent_div_34_Template_app_autocomplete_ngModelChange_5_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const segment_r14 = restoredCtx.$implicit;
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](segment_r14.to = $event);
    })("locationSelected", function FlightComponent_div_34_Template_app_autocomplete_locationSelected_5_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const i_r15 = restoredCtx.index;
      const ctx_r20 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r20.onSegmentLocationSelected($event, i_r15, "to"));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "div", 18)(7, "div", 19)(8, "input", 76);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngModelChange", function FlightComponent_div_34_Template_input_ngModelChange_8_listener($event) {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const segment_r14 = restoredCtx.$implicit;
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](segment_r14.date = $event);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](9, "i", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "button", 77);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_div_34_Template_button_click_10_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r17);
      const i_r15 = restoredCtx.index;
      const ctx_r22 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r22.removeSegment(i_r15));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](11, "i", 78);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const segment_r14 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", segment_r14.from);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", segment_r14.to);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngModel", segment_r14.date);
  }
}
function FlightComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r24 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 79)(1, "button", 80);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_div_35_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r24);
      const ctx_r23 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r23.addSegment());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 81);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Add Sector ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function FlightComponent_option_106_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "option", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r25 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", option_r25.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", option_r25.label, " ");
  }
}
function FlightComponent_span_114_option_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "option", 82);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const option_r27 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("value", option_r27.value);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" ", option_r27.label, " ");
  }
}
function FlightComponent_span_114_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span", 83)(1, "select", 84);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](2, FlightComponent_span_114_option_2_Template, 2, 2, "option", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
  if (rf & 2) {
    const ctx_r7 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx_r7.calendarDays);
  }
}
function FlightComponent_span_117_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](1, "SEARCH NOW");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function FlightComponent_span_118_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 85);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](2, " Searching... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
  }
}
function FlightComponent_div_126_Template(rf, ctx) {
  if (rf & 1) {
    const _r30 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 86);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_div_126_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r30);
      const search_r28 = restoredCtx.$implicit;
      const ctx_r29 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r29.loadLatestSearch(search_r28));
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](1, "div", 87);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 5);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](3, "div", 88)(4, "div", 89);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, " Coming from ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](8, " - ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](9, "strong");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](10);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](11);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](12, "date");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const search_r28 = ctx.$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](search_r28.from);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](search_r28.to);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate1"](" on ", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind2"](12, 3, search_r28.date, "MMM d, yyyy"), " ");
  }
}
function FlightComponent_div_128_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 90);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](1, "i", 91);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](2, "p");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, "No recent searches");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](4, "small");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](5, "Your recent flight searches will appear here");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
function FlightComponent_div_130_Template(rf, ctx) {
  if (rf & 1) {
    const _r32 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 92)(1, "button", 93);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_div_130_Template_button_click_1_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵrestoreView"](_r32);
      const ctx_r31 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵresetView"](ctx_r31.clearLatestSearches());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](2, "i", 94);
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](3, " Clear All ");
    _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
  }
}
class FlightComponent {
  constructor(fb, flightService, authService, router) {
    this.fb = fb;
    this.flightService = flightService;
    this.authService = authService;
    this.router = router;
    this.isLoading = false;
    this.currentUser = null;
    // Form state
    this.tripType = 'oneWay';
    this.showReturnDate = false;
    this.showCalendar = false;
    this.additionalSegments = [];
    // Passenger counts
    this.adultCount = 1;
    this.childCount = 0;
    this.infantCount = 0;
    // Class selection
    this.selectedClass = 'economy';
    // Baggage options
    this.baggageOptions = [{
      value: 'all',
      label: '--All--'
    }, {
      value: '20kg',
      label: '20kg'
    }, {
      value: '30kg',
      label: '30kg'
    }, {
      value: 'extra',
      label: 'Extra'
    }];
    // Calendar options
    this.calendarDays = [{
      value: '1',
      label: '+/- 1 Days'
    }, {
      value: '3',
      label: '+/- 3 Days'
    }, {
      value: '7',
      label: '+/- 7 Days'
    }];
    this.flightForm = this.createForm();
    this.latestSearches$ = this.flightService.latestSearches$;
  }
  ngOnInit() {
    // Check authentication
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/signin']);
      return;
    }
    // Set default dates
    this.setDefaultDates();
  }
  /**
   * Create reactive form
   */
  createForm() {
    return this.fb.group({
      tripType: ['oneWay', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      from: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      to: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      departureDate: ['', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      returnDate: [''],
      adults: [1, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.min(1)]],
      children: [0, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.min(0)]],
      infants: [0, [_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.min(0)]],
      class: ['economy', _angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required],
      preferredAirline: [''],
      directFlights: [false],
      refundableFares: [false],
      baggage: ['all'],
      calendar: [false],
      calendarDays: ['3']
    });
  }
  /**
   * Set default dates (today and tomorrow)
   */
  setDefaultDates() {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);
    this.flightForm.patchValue({
      departureDate: this.formatDate(tomorrow),
      returnDate: this.formatDate(nextWeek)
    });
  }
  /**
   * Format date for input field
   */
  formatDate(date) {
    return date.toISOString().split('T')[0];
  }
  /**
   * Handle trip type change
   */
  onTripTypeChange(type) {
    this.tripType = type;
    this.showReturnDate = type === 'roundTrip';
    this.flightForm.patchValue({
      tripType: type
    });
    if (type === 'roundTrip') {
      this.flightForm.get('returnDate')?.setValidators([_angular_forms__WEBPACK_IMPORTED_MODULE_4__.Validators.required]);
    } else {
      this.flightForm.get('returnDate')?.clearValidators();
    }
    this.flightForm.get('returnDate')?.updateValueAndValidity();
    // Clear additional segments when switching away from multi-city
    if (type !== 'multiCity') {
      this.additionalSegments = [];
    }
  }
  /**
   * Add a new flight segment for multi-city trips
   */
  addSegment() {
    const newSegment = {
      from: '',
      to: '',
      date: ''
    };
    this.additionalSegments.push(newSegment);
  }
  /**
   * Remove a flight segment
   */
  removeSegment(index) {
    this.additionalSegments.splice(index, 1);
  }
  /**
   * Handle location selection for main form fields
   */
  onFromLocationSelected(location) {
    this.flightForm.patchValue({
      from: location.displayText
    });
  }
  onToLocationSelected(location) {
    this.flightForm.patchValue({
      to: location.displayText
    });
  }
  /**
   * Handle location selection for additional segments
   */
  onSegmentLocationSelected(location, segmentIndex, field) {
    if (this.additionalSegments[segmentIndex]) {
      this.additionalSegments[segmentIndex][field] = location.displayText;
    }
  }
  /**
   * Handle passenger count changes
   */
  updatePassengerCount(type, increment) {
    const currentValue = this.flightForm.get(type)?.value || 0;
    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);
    // Ensure at least 1 adult
    if (type === 'adults' && newValue < 1) {
      newValue = 1;
    }
    this.flightForm.patchValue({
      [type]: newValue
    });
    // Update component properties for display
    if (type === 'adults') this.adultCount = newValue;
    if (type === 'children') this.childCount = newValue;
    if (type === 'infants') this.infantCount = newValue;
  }
  /**
   * Get total passenger count
   */
  getTotalPassengers() {
    const adults = this.flightForm.get('adults')?.value || 0;
    const children = this.flightForm.get('children')?.value || 0;
    const infants = this.flightForm.get('infants')?.value || 0;
    return adults + children + infants;
  }
  /**
   * Handle class selection
   */
  onClassChange(flightClass) {
    this.selectedClass = flightClass;
    this.flightForm.patchValue({
      class: flightClass
    });
  }
  /**
   * Toggle calendar option
   */
  toggleCalendar() {
    this.showCalendar = !this.showCalendar;
    this.flightForm.patchValue({
      calendar: this.showCalendar
    });
  }
  /**
   * Swap from and to locations
   */
  swapLocations() {
    const from = this.flightForm.get('from')?.value;
    const to = this.flightForm.get('to')?.value;
    this.flightForm.patchValue({
      from: to,
      to: from
    });
  }
  /**
   * Handle form submission
   */
  onSubmit() {
    if (this.flightForm.valid) {
      this.isLoading = true;
      const formValue = this.flightForm.value;
      const searchForm = {
        tripType: formValue.tripType,
        from: formValue.from,
        to: formValue.to,
        departureDate: formValue.departureDate,
        returnDate: formValue.returnDate,
        passengers: {
          adults: formValue.adults,
          children: formValue.children,
          infants: formValue.infants
        },
        class: formValue.class,
        preferredAirline: formValue.preferredAirline,
        directFlights: formValue.directFlights,
        refundableFares: formValue.refundableFares,
        baggage: formValue.baggage,
        calendar: formValue.calendar
      };
      // Save to latest searches
      this.flightService.saveLatestSearch(searchForm);
      // Perform search based on trip type
      let searchObservable;
      switch (searchForm.tripType) {
        case 'oneWay':
          searchObservable = this.flightService.searchOneWayFlights(searchForm);
          break;
        case 'roundTrip':
          searchObservable = this.flightService.searchRoundTripFlights(searchForm);
          break;
        case 'multiCity':
          searchObservable = this.flightService.searchMultiCityFlights(searchForm);
          break;
        default:
          searchObservable = this.flightService.searchOneWayFlights(searchForm);
      }
      searchObservable.subscribe({
        next: response => {
          this.isLoading = false;
          if (response.header.success) {
            console.log('Flight search results:', response);
            // Navigate to results page with search results and parameters
            this.router.navigate(['/flight-results'], {
              state: {
                results: response,
                searchParams: searchForm
              }
            });
          } else {
            console.error('Search failed:', response.header.messages);
            // TODO: Show error message to user
          }
        },

        error: error => {
          this.isLoading = false;
          console.error('Search error:', error);
          // TODO: Show error message to user
        }
      });
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.flightForm.controls).forEach(key => {
        this.flightForm.get(key)?.markAsTouched();
      });
    }
  }
  /**
   * Load a previous search
   */
  loadLatestSearch(search) {
    this.flightForm.patchValue({
      from: search.from,
      to: search.to,
      departureDate: search.date,
      adults: search.passengers,
      children: 0,
      infants: 0
    });
  }
  /**
   * Clear latest searches
   */
  clearLatestSearches() {
    this.flightService.clearLatestSearches();
  }
  /**
   * Get form control error message
   */
  getErrorMessage(controlName) {
    const control = this.flightForm.get(controlName);
    if (control?.errors && control.touched) {
      if (control.errors['required']) {
        return `${controlName} is required`;
      }
      if (control.errors['min']) {
        return `Minimum value is ${control.errors['min'].min}`;
      }
    }
    return '';
  }
  static {
    this.ɵfac = function FlightComponent_Factory(t) {
      return new (t || FlightComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_flight_service__WEBPACK_IMPORTED_MODULE_0__.FlightService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_1__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_5__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineComponent"]({
      type: FlightComponent,
      selectors: [["app-flight"]],
      decls: 132,
      vars: 32,
      consts: [[1, "flight-container"], [1, "flight-content"], [1, "flight-search-panel"], [1, "search-header"], [1, "search-title"], [1, "fas", "fa-plane"], [1, "search-subtitle"], [1, "flight-form", 3, "formGroup", "ngSubmit"], [1, "trip-type-selector"], ["type", "button", 1, "trip-type-btn", 3, "click"], [1, "location-date-section"], [1, "flight-segments"], [1, "flight-segment"], [1, "segment-row"], [1, "form-group", "location-group"], ["formControlName", "from", "placeholder", "Leaving from (City, Country Or Specific Airport)", "icon", "fas fa-plane-departure", 3, "locationSelected"], ["class", "error-message", 4, "ngIf"], ["formControlName", "to", "placeholder", "Going to (City, Country Or Specific Airport)", "icon", "fas fa-plane-arrival", 3, "locationSelected"], [1, "form-group", "date-group"], [1, "date-input-wrapper"], ["type", "date", "formControlName", "departureDate", "placeholder", "Choose A Date", 1, "date-input"], [1, "fas", "fa-calendar-alt", "date-icon"], ["class", "flight-segment", 4, "ngIf"], ["class", "flight-segment", 4, "ngFor", "ngForOf"], ["class", "add-sector-section", 4, "ngIf"], [1, "passenger-class-section"], [1, "form-group", "passenger-group"], [1, "passenger-controls"], [1, "passenger-type"], [1, "passenger-icon"], [1, "fas", "fa-user"], [1, "passenger-count"], [1, "counter-controls"], ["type", "button", 1, "counter-btn", 3, "click"], [1, "fas", "fa-child"], [1, "fas", "fa-baby"], [1, "class-selection"], ["type", "button", 1, "class-btn", 3, "click"], [1, "fas", "fa-chair"], [1, "form-group", "airline-group"], ["for", "preferredAirline"], ["id", "preferredAirline", "formControlName", "preferredAirline", 1, "airline-select"], ["value", ""], ["value", "TK"], ["value", "AF"], ["value", "LH"], ["value", "EK"], ["value", "QR"], [1, "additional-options"], [1, "option-group"], [1, "option-label"], [1, "option-controls"], ["formControlName", "refundableFares", 1, "option-select"], ["value", "false"], ["value", "true"], ["formControlName", "baggage", 1, "option-select"], [3, "value", 4, "ngFor", "ngForOf"], [1, "calendar-toggle"], ["type", "checkbox", "id", "calendar", "formControlName", "calendar", 3, "change"], ["for", "calendar", 1, "calendar-label"], ["class", "calendar-days", 4, "ngIf"], [1, "search-button-section"], ["type", "submit", 1, "search-btn", 3, "disabled"], [4, "ngIf"], [1, "latest-searches-panel"], [1, "searches-header"], [1, "searches-list"], ["class", "search-item", 3, "click", 4, "ngFor", "ngForOf"], ["class", "empty-searches", 4, "ngIf"], ["class", "searches-actions", 4, "ngIf"], [1, "error-message"], ["placeholder", "Leaving from (City, Country Or Specific Airport)", "icon", "fas fa-plane-departure", 3, "ngModel", "readonly"], ["placeholder", "Going to (City, Country Or Specific Airport)", "icon", "fas fa-plane-arrival", 3, "ngModel", "readonly"], ["type", "date", "formControlName", "returnDate", "placeholder", "Choose A Date", 1, "date-input"], ["placeholder", "Leaving from (City, Country Or Specific Airport)", "icon", "fas fa-plane-departure", 3, "ngModel", "ngModelChange", "locationSelected"], ["placeholder", "Going to (City, Country Or Specific Airport)", "icon", "fas fa-plane-arrival", 3, "ngModel", "ngModelChange", "locationSelected"], ["type", "date", "placeholder", "Choose A Date", 1, "date-input", 3, "ngModel", "ngModelChange"], ["type", "button", 1, "remove-segment-btn", 3, "click"], [1, "fas", "fa-times"], [1, "add-sector-section"], ["type", "button", 1, "add-sector-btn", 3, "click"], [1, "fas", "fa-plus"], [3, "value"], [1, "calendar-days"], ["formControlName", "calendarDays", 1, "calendar-select"], [1, "fas", "fa-spinner", "fa-spin"], [1, "search-item", 3, "click"], [1, "search-icon"], [1, "search-details"], [1, "search-route"], [1, "empty-searches"], [1, "fas", "fa-search"], [1, "searches-actions"], ["type", "button", 1, "clear-btn", 3, "click"], [1, "fas", "fa-trash"]],
      template: function FlightComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](5, "i", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](6, "h2");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](7, "Search and Book Flights");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](8, "p", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](9, "We're bringing you a new level of comfort");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](10, "form", 7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("ngSubmit", function FlightComponent_Template_form_ngSubmit_10_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](11, "div", 8)(12, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_12_listener() {
            return ctx.onTripTypeChange("oneWay");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](13, " One way ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](14, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_14_listener() {
            return ctx.onTripTypeChange("roundTrip");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](15, " Round Trip ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](16, "button", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_16_listener() {
            return ctx.onTripTypeChange("multiCity");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](17, " Multi-City/Stop-Overs ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](18, "div", 10)(19, "div", 11)(20, "div", 12)(21, "div", 13)(22, "div", 14)(23, "app-autocomplete", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("locationSelected", function FlightComponent_Template_app_autocomplete_locationSelected_23_listener($event) {
            return ctx.onFromLocationSelected($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](24, FlightComponent_div_24_Template, 2, 1, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](25, "div", 14)(26, "app-autocomplete", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("locationSelected", function FlightComponent_Template_app_autocomplete_locationSelected_26_listener($event) {
            return ctx.onToLocationSelected($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](27, FlightComponent_div_27_Template, 2, 1, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](28, "div", 18)(29, "div", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](30, "input", 20)(31, "i", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](32, FlightComponent_div_32_Template, 2, 1, "div", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](33, FlightComponent_div_33_Template, 11, 5, "div", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](34, FlightComponent_div_34_Template, 12, 3, "div", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](35, FlightComponent_div_35_Template, 4, 0, "div", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](36, "div", 25)(37, "div", 26)(38, "label");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](39, "Passenger & Class of travel");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](40, "div", 27)(41, "div", 28)(42, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](43, "i", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](44, "span", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](45);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](46, "div", 32)(47, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_47_listener() {
            return ctx.updatePassengerCount("adults", false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](48, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](49, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_49_listener() {
            return ctx.updatePassengerCount("adults", true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](50, "+");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](51, "div", 28)(52, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](53, "i", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](54, "span", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](55);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](56, "div", 32)(57, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_57_listener() {
            return ctx.updatePassengerCount("children", false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](58, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](59, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_59_listener() {
            return ctx.updatePassengerCount("children", true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](60, "+");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](61, "div", 28)(62, "span", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](63, "i", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](64, "span", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](65);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](66, "div", 32)(67, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_67_listener() {
            return ctx.updatePassengerCount("infants", false);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](68, "-");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](69, "button", 33);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_69_listener() {
            return ctx.updatePassengerCount("infants", true);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](70, "+");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](71, "div", 36)(72, "button", 37);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("click", function FlightComponent_Template_button_click_72_listener() {
            return ctx.onClassChange("economy");
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelement"](73, "i", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](74, " Economy ");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](75, "div", 39)(76, "label", 40);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](77, "Preferred Airline");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](78, "select", 41)(79, "option", 42);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](80, "Preferred Airline");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](81, "option", 43);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](82, "Turkish Airlines");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](83, "option", 44);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](84, "Air France");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](85, "option", 45);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](86, "Lufthansa");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](87, "option", 46);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](88, "Emirates");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](89, "option", 47);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](90, "Qatar Airways");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](91, "div", 48)(92, "div", 49)(93, "label", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](94, "Refundable fares");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](95, "div", 51)(96, "select", 52)(97, "option", 53);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](98, "--All--");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](99, "option", 54);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](100, "Refundable Only");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](101, "div", 49)(102, "label", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](103, "Baggage");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](104, "div", 51)(105, "select", 55);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](106, FlightComponent_option_106_Template, 2, 2, "option", 56);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](107, "div", 49)(108, "label", 50);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](109, "Calendar");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](110, "div", 51)(111, "div", 57)(112, "input", 58);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵlistener"]("change", function FlightComponent_Template_input_change_112_listener() {
            return ctx.toggleCalendar();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](113, "label", 59);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](114, FlightComponent_span_114_Template, 3, 1, "span", 60);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](115, "div", 61)(116, "button", 62);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](117, FlightComponent_span_117_Template, 2, 0, "span", 63);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](118, FlightComponent_span_118_Template, 3, 0, "span", 63);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](119, "div", 64)(120, "div", 65)(121, "h3");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](122, "Latest Searches");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](123, "p");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtext"](124, "We're bringing you a new level of comfort");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementStart"](125, "div", 66);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](126, FlightComponent_div_126_Template, 13, 6, "div", 67);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](127, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](128, FlightComponent_div_128_Template, 6, 0, "div", 68);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](129, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtemplate"](130, FlightComponent_div_130_Template, 4, 0, "div", 69);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipe"](131, "async");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵelementEnd"]()()();
        }
        if (rf & 2) {
          let tmp_10_0;
          let tmp_11_0;
          let tmp_12_0;
          let tmp_20_0;
          let tmp_21_0;
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("formGroup", ctx.flightForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx.tripType === "oneWay");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx.tripType === "roundTrip");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx.tripType === "multiCity");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.getErrorMessage("from"));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](3);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.getErrorMessage("to"));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.getErrorMessage("departureDate"));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showReturnDate);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.additionalSegments);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.tripType === "multiCity");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](((tmp_10_0 = ctx.flightForm.get("adults")) == null ? null : tmp_10_0.value) || 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](((tmp_11_0 = ctx.flightForm.get("children")) == null ? null : tmp_11_0.value) || 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](10);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵtextInterpolate"](((tmp_12_0 = ctx.flightForm.get("infants")) == null ? null : tmp_12_0.value) || 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](7);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵclassProp"]("active", ctx.selectedClass === "economy");
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](34);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", ctx.baggageOptions);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.showCalendar);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("disabled", ctx.isLoading || !ctx.flightForm.valid);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](8);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngForOf", _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](127, 26, ctx.latestSearches$));
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ((tmp_20_0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](129, 28, ctx.latestSearches$)) == null ? null : tmp_20_0.length) === 0);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵproperty"]("ngIf", ((tmp_21_0 = _angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵpipeBind1"](131, 30, ctx.latestSearches$)) == null ? null : tmp_21_0.length) > 0);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_6__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_6__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgSelectOption, _angular_forms__WEBPACK_IMPORTED_MODULE_4__["ɵNgSelectMultipleOption"], _angular_forms__WEBPACK_IMPORTED_MODULE_4__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.CheckboxControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.SelectControlValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.FormControlName, _angular_forms__WEBPACK_IMPORTED_MODULE_4__.NgModel, _shared_autocomplete_autocomplete_component__WEBPACK_IMPORTED_MODULE_2__.AutocompleteComponent, _angular_common__WEBPACK_IMPORTED_MODULE_6__.AsyncPipe, _angular_common__WEBPACK_IMPORTED_MODULE_6__.DatePipe],
      styles: ["\n\n.flight-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n}\n\n.flight-content[_ngcontent-%COMP%] {\n  max-width: 1200px;\n  margin: 0 auto;\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 30px;\n  align-items: start;\n}\n\n\n\n.flight-search-panel[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 15px;\n  padding: 30px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n}\n\n.search-header[_ngcontent-%COMP%] {\n  margin-bottom: 30px;\n}\n\n.search-title[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.search-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\n  color: white;\n  padding: 12px;\n  border-radius: 50%;\n  font-size: 20px;\n}\n\n.search-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\n  color: #2d3748;\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.search-subtitle[_ngcontent-%COMP%] {\n  color: #718096;\n  font-size: 14px;\n  margin: 0;\n  margin-left: 55px;\n}\n\n\n\n.trip-type-selector[_ngcontent-%COMP%] {\n  display: flex;\n  background: #f7fafc;\n  border-radius: 8px;\n  padding: 4px;\n  margin-bottom: 25px;\n}\n\n.trip-type-btn[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: 12px 16px;\n  border: none;\n  background: transparent;\n  color: #4a5568;\n  font-size: 14px;\n  font-weight: 500;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.trip-type-btn.active[_ngcontent-%COMP%] {\n  background: #3b4371;\n  color: white;\n  box-shadow: 0 2px 8px rgba(59, 67, 113, 0.3);\n}\n\n.trip-type-btn[_ngcontent-%COMP%]:hover:not(.active) {\n  background: #e2e8f0;\n}\n\n\n\n.location-date-section[_ngcontent-%COMP%], .passenger-class-section[_ngcontent-%COMP%], .additional-options[_ngcontent-%COMP%] {\n  margin-bottom: 25px;\n}\n\n\n\n.form-group[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\n  display: block;\n  color: #2d3748;\n  font-size: 14px;\n  font-weight: 500;\n  margin-bottom: 8px;\n}\n\n\n\n.location-input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.location-input-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 15px;\n  color: #a0aec0;\n  z-index: 2;\n}\n\n.location-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 15px 15px 15px 45px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: border-color 0.2s ease;\n}\n\n.location-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b4371;\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\n}\n\n.swap-btn[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 15px;\n  background: #f7fafc;\n  border: 1px solid #e2e8f0;\n  border-radius: 6px;\n  padding: 8px;\n  cursor: pointer;\n  color: #4a5568;\n  transition: all 0.2s ease;\n}\n\n.swap-btn[_ngcontent-%COMP%]:hover {\n  background: #3b4371;\n  color: white;\n  border-color: #3b4371;\n}\n\n\n\n.flight-segments[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.flight-segment[_ngcontent-%COMP%] {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 15px;\n  border: 1px solid #e9ecef;\n}\n\n.segment-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr auto;\n  gap: 15px;\n  align-items: end;\n}\n\n.date-input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.date-input-wrapper[_ngcontent-%COMP%]   .date-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 15px;\n  color: #a0aec0;\n  z-index: 2;\n}\n\n\n\n.date-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 15px 45px 15px 15px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: border-color 0.2s ease;\n  background: white;\n}\n\n.date-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b4371;\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\n}\n\n.location-input[readonly][_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  color: #6c757d;\n}\n\n.remove-segment-btn[_ngcontent-%COMP%] {\n  background: #dc3545;\n  border: none;\n  border-radius: 50%;\n  width: 35px;\n  height: 35px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  color: white;\n}\n\n.remove-segment-btn[_ngcontent-%COMP%]:hover {\n  background: #c82333;\n  transform: scale(1.05);\n}\n\n.add-sector-section[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: center;\n  margin-top: 15px;\n}\n\n.add-sector-btn[_ngcontent-%COMP%] {\n  background: #28a745;\n  color: white;\n  border: none;\n  border-radius: 6px;\n  padding: 10px 20px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.add-sector-btn[_ngcontent-%COMP%]:hover {\n  background: #218838;\n  transform: translateY(-1px);\n}\n\n.add-sector-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 12px;\n}\n\n\n\n.passenger-controls[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  padding: 15px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  background: #f7fafc;\n}\n\n.passenger-type[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.passenger-icon[_ngcontent-%COMP%] {\n  color: #4a5568;\n  font-size: 16px;\n  width: 20px;\n  text-align: center;\n}\n\n.passenger-count[_ngcontent-%COMP%] {\n  font-weight: 600;\n  color: #2d3748;\n  min-width: 20px;\n  text-align: center;\n}\n\n.counter-controls[_ngcontent-%COMP%] {\n  display: flex;\n  gap: 4px;\n}\n\n.counter-btn[_ngcontent-%COMP%] {\n  width: 24px;\n  height: 24px;\n  border: 1px solid #cbd5e0;\n  background: white;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 600;\n  color: #4a5568;\n  transition: all 0.2s ease;\n}\n\n.counter-btn[_ngcontent-%COMP%]:hover {\n  background: #3b4371;\n  color: white;\n  border-color: #3b4371;\n}\n\n.class-selection[_ngcontent-%COMP%] {\n  margin-left: auto;\n}\n\n.class-btn[_ngcontent-%COMP%] {\n  padding: 8px 16px;\n  border: 2px solid #e2e8f0;\n  background: white;\n  border-radius: 6px;\n  cursor: pointer;\n  font-size: 14px;\n  color: #4a5568;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.class-btn.active[_ngcontent-%COMP%] {\n  background: #3b4371;\n  color: white;\n  border-color: #3b4371;\n}\n\n.class-btn[_ngcontent-%COMP%]:hover:not(.active) {\n  border-color: #cbd5e0;\n  background: #f7fafc;\n}\n\n\n\n.airline-select[_ngcontent-%COMP%], .option-select[_ngcontent-%COMP%], .calendar-select[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 15px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  background: white;\n  cursor: pointer;\n  transition: border-color 0.2s ease;\n}\n\n.airline-select[_ngcontent-%COMP%]:focus, .option-select[_ngcontent-%COMP%]:focus, .calendar-select[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: #3b4371;\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\n}\n\n\n\n.additional-options[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20px;\n}\n\n.option-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n}\n\n.option-label[_ngcontent-%COMP%] {\n  color: #2d3748;\n  font-size: 14px;\n  font-weight: 500;\n  margin-bottom: 8px;\n}\n\n.calendar-toggle[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.calendar-toggle[_ngcontent-%COMP%]   input[type=\"checkbox\"][_ngcontent-%COMP%] {\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.calendar-days[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.calendar-select[_ngcontent-%COMP%] {\n  padding: 8px 12px;\n  font-size: 12px;\n}\n\n\n\n.search-button-section[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-top: 30px;\n}\n\n.search-btn[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #48bb78, #38a169);\n  color: white;\n  border: none;\n  padding: 18px 60px;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);\n}\n\n.search-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);\n}\n\n.search-btn[_ngcontent-%COMP%]:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n\n\n.latest-searches-panel[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: 15px;\n  padding: 25px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n  height: -moz-fit-content;\n  height: fit-content;\n}\n\n.searches-header[_ngcontent-%COMP%] {\n  margin-bottom: 20px;\n}\n\n.searches-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  color: #2d3748;\n  font-size: 20px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n}\n\n.searches-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  color: #718096;\n  font-size: 14px;\n  margin: 0;\n}\n\n\n\n.searches-list[_ngcontent-%COMP%] {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.search-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  margin-bottom: 10px;\n  border: 1px solid #f7fafc;\n}\n\n.search-item[_ngcontent-%COMP%]:hover {\n  background: #f7fafc;\n  border-color: #e2e8f0;\n  transform: translateX(5px);\n}\n\n.search-icon[_ngcontent-%COMP%] {\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\n  color: white;\n  padding: 10px;\n  border-radius: 50%;\n  font-size: 14px;\n  flex-shrink: 0;\n}\n\n.search-details[_ngcontent-%COMP%] {\n  flex: 1;\n}\n\n.search-route[_ngcontent-%COMP%] {\n  color: #4a5568;\n  font-size: 14px;\n  line-height: 1.4;\n}\n\n.search-route[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\n  color: #2d3748;\n  font-weight: 600;\n}\n\n\n\n.empty-searches[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: 40px 20px;\n  color: #a0aec0;\n}\n\n.empty-searches[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 48px;\n  margin-bottom: 15px;\n  opacity: 0.5;\n}\n\n.empty-searches[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  font-size: 16px;\n  margin: 0 0 5px 0;\n  color: #718096;\n}\n\n.empty-searches[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #a0aec0;\n}\n\n\n\n.searches-actions[_ngcontent-%COMP%] {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.clear-btn[_ngcontent-%COMP%] {\n  background: #fed7d7;\n  color: #c53030;\n  border: 1px solid #feb2b2;\n  padding: 8px 16px;\n  border-radius: 6px;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.clear-btn[_ngcontent-%COMP%]:hover {\n  background: #fc8181;\n  color: white;\n  border-color: #fc8181;\n}\n\n\n\n.error-message[_ngcontent-%COMP%] {\n  color: #e53e3e;\n  font-size: 12px;\n  margin-top: 5px;\n}\n\n\n\n@media (max-width: 768px) {\n  .flight-content[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n\n  .flight-search-panel[_ngcontent-%COMP%], .latest-searches-panel[_ngcontent-%COMP%] {\n    padding: 20px;\n  }\n\n  .additional-options[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 15px;\n  }\n\n  .passenger-controls[_ngcontent-%COMP%] {\n    flex-wrap: wrap;\n    gap: 15px;\n  }\n\n  .search-title[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n\n  .search-subtitle[_ngcontent-%COMP%] {\n    margin-left: 0;\n  }\n\n  .segment-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: 10px;\n  }\n\n  .remove-segment-btn[_ngcontent-%COMP%] {\n    justify-self: center;\n    margin-top: 10px;\n  }\n}\n\n@media (max-width: 480px) {\n  .flight-container[_ngcontent-%COMP%] {\n    padding: 10px;\n  }\n\n  .trip-type-selector[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: 4px;\n  }\n\n  .trip-type-btn[_ngcontent-%COMP%] {\n    text-align: center;\n  }\n\n  .search-btn[_ngcontent-%COMP%] {\n    width: 100%;\n    padding: 15px;\n  }\n}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 6161:
/*!**************************************************************************!*\
  !*** ./src/app/components/shared/autocomplete/autocomplete.component.ts ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AutocompleteComponent: () => (/* binding */ AutocompleteComponent)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 819);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 2575);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 1817);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 6647);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs/operators */ 3900);
/* harmony import */ var _services_autocomplete_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../services/autocomplete.service */ 74);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/common */ 316);







const _c0 = ["inputElement"];
function AutocompleteComponent_i_2_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](0, "i", 7);
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassMap"](ctx_r0.icon);
  }
}
function AutocompleteComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 8);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function AutocompleteComponent_div_6_div_2_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 21);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const suggestion_r6 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]().$implicit;
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", suggestion_r6.country, " ");
  }
}
function AutocompleteComponent_div_6_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 14);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function AutocompleteComponent_div_6_div_2_Template_div_click_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r11);
      const suggestion_r6 = restoredCtx.$implicit;
      const ctx_r10 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r10.selectSuggestion(suggestion_r6));
    })("mouseenter", function AutocompleteComponent_div_6_div_2_Template_div_mouseenter_0_listener() {
      const restoredCtx = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r11);
      const i_r7 = restoredCtx.index;
      const ctx_r12 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r12.selectedIndex = i_r7);
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "div", 15);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "i");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "div", 16)(4, "div", 17);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](5);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, AutocompleteComponent_div_6_div_2_div_6_Template, 2, 1, "div", 18);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](7, "div", 19)(8, "span", 20);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](9);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
  }
  if (rf & 2) {
    const suggestion_r6 = ctx.$implicit;
    const i_r7 = ctx.index;
    const ctx_r4 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("selected", i_r7 === ctx_r4.selectedIndex);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassMap"](ctx_r4.getLocationTypeIcon(suggestion_r6.type));
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](3);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](suggestion_r6.displayText);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", suggestion_r6.type === "airport" && suggestion_r6.country);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassMap"]("type-" + suggestion_r6.type);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", suggestion_r6.type, " ");
  }
}
function AutocompleteComponent_div_6_div_3_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 22);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "i", 23);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](3, "No locations found");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
  }
}
function AutocompleteComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 10)(1, "div", 11);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, AutocompleteComponent_div_6_div_2_Template, 10, 9, "div", 12);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](3, AutocompleteComponent_div_6_div_3_Template, 4, 0, "div", 13);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r3 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngForOf", ctx_r3.suggestions);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx_r3.suggestions.length === 0 && !ctx_r3.isLoading);
  }
}
class AutocompleteComponent {
  constructor(autocompleteService, elementRef) {
    this.autocompleteService = autocompleteService;
    this.elementRef = elementRef;
    this.placeholder = '';
    this.icon = '';
    this.readonly = false;
    this.locationSelected = new _angular_core__WEBPACK_IMPORTED_MODULE_1__.EventEmitter();
    this.value = '';
    this.suggestions = [];
    this.showSuggestions = false;
    this.selectedIndex = -1;
    this.isLoading = false;
    this.searchSubject = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Subject();
    this.destroy$ = new rxjs__WEBPACK_IMPORTED_MODULE_2__.Subject();
    this.onChange = value => {};
    this.onTouched = () => {};
  }
  ngOnInit() {
    // Setup search with debouncing
    this.searchSubject.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.debounceTime)(300), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.distinctUntilChanged)(), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.switchMap)(query => this.autocompleteService.searchLocations(query)), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.takeUntil)(this.destroy$)).subscribe(suggestions => {
      this.suggestions = suggestions;
      this.showSuggestions = suggestions.length > 0;
      this.isLoading = false;
    });
    // Close suggestions when clicking outside
    document.addEventListener('click', this.onDocumentClick.bind(this));
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    document.removeEventListener('click', this.onDocumentClick.bind(this));
  }
  // ControlValueAccessor implementation
  writeValue(value) {
    this.value = value || '';
    if (this.inputElement) {
      this.inputElement.nativeElement.value = this.value;
    }
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled) {
    if (this.inputElement) {
      this.inputElement.nativeElement.disabled = isDisabled;
    }
  }
  onInput(event) {
    const target = event.target;
    this.value = target.value;
    this.onChange(this.value);
    if (this.value.length >= 2) {
      this.isLoading = true;
      this.searchSubject.next(this.value);
    } else {
      this.suggestions = [];
      this.showSuggestions = false;
      this.isLoading = false;
    }
    this.selectedIndex = -1;
  }
  onFocus() {
    this.onTouched();
    if (this.value.length >= 2) {
      this.showSuggestions = this.suggestions.length > 0;
    } else if (this.value.length === 0) {
      // Show popular destinations when focusing on empty input
      this.autocompleteService.getPopularDestinations().subscribe(destinations => {
        this.suggestions = destinations;
        this.showSuggestions = true;
      });
    }
  }
  onKeyDown(event) {
    if (!this.showSuggestions) return;
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        break;
      case 'Enter':
        event.preventDefault();
        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
          this.selectSuggestion(this.suggestions[this.selectedIndex]);
        }
        break;
      case 'Escape':
        this.hideSuggestions();
        break;
    }
  }
  selectSuggestion(location) {
    this.value = location.displayText;
    this.onChange(this.value);
    this.locationSelected.emit(location);
    this.hideSuggestions();
    if (this.inputElement) {
      this.inputElement.nativeElement.value = this.value;
    }
  }
  hideSuggestions() {
    this.showSuggestions = false;
    this.selectedIndex = -1;
  }
  onDocumentClick(event) {
    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.hideSuggestions();
    }
  }
  getLocationTypeIcon(type) {
    switch (type) {
      case 'airport':
        return 'fas fa-plane';
      case 'city':
        return 'fas fa-city';
      case 'country':
        return 'fas fa-flag';
      default:
        return 'fas fa-map-marker-alt';
    }
  }
  static {
    this.ɵfac = function AutocompleteComponent_Factory(t) {
      return new (t || AutocompleteComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_autocomplete_service__WEBPACK_IMPORTED_MODULE_0__.AutocompleteService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_1__.ElementRef));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: AutocompleteComponent,
      selectors: [["app-autocomplete"]],
      viewQuery: function AutocompleteComponent_Query(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵviewQuery"](_c0, 7);
        }
        if (rf & 2) {
          let _t;
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵqueryRefresh"](_t = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵloadQuery"]()) && (ctx.inputElement = _t.first);
        }
      },
      inputs: {
        placeholder: "placeholder",
        icon: "icon",
        readonly: "readonly"
      },
      outputs: {
        locationSelected: "locationSelected"
      },
      features: [_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵProvidersFeature"]([{
        provide: _angular_forms__WEBPACK_IMPORTED_MODULE_7__.NG_VALUE_ACCESSOR,
        useExisting: (0,_angular_core__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(() => AutocompleteComponent),
        multi: true
      }])],
      decls: 7,
      vars: 9,
      consts: [[1, "autocomplete-wrapper"], [1, "input-wrapper"], ["class", "input-icon", 3, "class", 4, "ngIf"], ["type", "text", "autocomplete", "off", 1, "autocomplete-input", 3, "placeholder", "readonly", "input", "focus", "keydown"], ["inputElement", ""], ["class", "loading-spinner", 4, "ngIf"], ["class", "suggestions-dropdown", 4, "ngIf"], [1, "input-icon"], [1, "loading-spinner"], [1, "fas", "fa-spinner", "fa-spin"], [1, "suggestions-dropdown"], [1, "suggestions-list"], ["class", "suggestion-item", 3, "selected", "click", "mouseenter", 4, "ngFor", "ngForOf"], ["class", "no-results", 4, "ngIf"], [1, "suggestion-item", 3, "click", "mouseenter"], [1, "suggestion-icon"], [1, "suggestion-content"], [1, "suggestion-main"], ["class", "suggestion-details", 4, "ngIf"], [1, "suggestion-type"], [1, "type-badge"], [1, "suggestion-details"], [1, "no-results"], [1, "fas", "fa-search"]],
      template: function AutocompleteComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](2, AutocompleteComponent_i_2_Template, 1, 2, "i", 2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](3, "input", 3, 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("input", function AutocompleteComponent_Template_input_input_3_listener($event) {
            return ctx.onInput($event);
          })("focus", function AutocompleteComponent_Template_input_focus_3_listener() {
            return ctx.onFocus();
          })("keydown", function AutocompleteComponent_Template_input_keydown_3_listener($event) {
            return ctx.onKeyDown($event);
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](5, AutocompleteComponent_div_5_Template, 2, 0, "div", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](6, AutocompleteComponent_div_6_Template, 4, 2, "div", 6);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.icon);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("with-icon", ctx.icon)("readonly", ctx.readonly);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("placeholder", ctx.placeholder)("readonly", ctx.readonly);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](2);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.showSuggestions);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_8__.NgForOf, _angular_common__WEBPACK_IMPORTED_MODULE_8__.NgIf],
      styles: [".autocomplete-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  width: 100%;\n}\n\n.input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.input-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 15px;\n  color: #a0aec0;\n  z-index: 2;\n  font-size: 14px;\n}\n\n.autocomplete-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 15px 45px 15px 15px;\n  border: 2px solid #e2e8f0;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: border-color 0.2s ease;\n  background: white;\n  outline: none;\n}\n\n.autocomplete-input.with-icon[_ngcontent-%COMP%] {\n  padding-left: 45px;\n}\n\n.autocomplete-input.readonly[_ngcontent-%COMP%] {\n  background-color: #f8f9fa;\n  color: #6c757d;\n}\n\n.autocomplete-input[_ngcontent-%COMP%]:focus {\n  border-color: #3b4371;\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\n}\n\n.loading-spinner[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 15px;\n  color: #a0aec0;\n  font-size: 14px;\n}\n\n.suggestions-dropdown[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid #e2e8f0;\n  border-radius: 8px;\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  max-height: 300px;\n  overflow-y: auto;\n  margin-top: 4px;\n}\n\n.suggestions-list[_ngcontent-%COMP%] {\n  padding: 8px 0;\n}\n\n.suggestion-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  border-bottom: 1px solid #f7fafc;\n}\n\n.suggestion-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n\n.suggestion-item[_ngcontent-%COMP%]:hover, .suggestion-item.selected[_ngcontent-%COMP%] {\n  background-color: #f7fafc;\n}\n\n.suggestion-icon[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: #e2e8f0;\n  border-radius: 50%;\n  margin-right: 12px;\n  flex-shrink: 0;\n}\n\n.suggestion-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  color: #4a5568;\n  font-size: 14px;\n}\n\n.suggestion-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n\n.suggestion-main[_ngcontent-%COMP%] {\n  font-size: 14px;\n  font-weight: 500;\n  color: #2d3748;\n  line-height: 1.4;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.suggestion-details[_ngcontent-%COMP%] {\n  font-size: 12px;\n  color: #718096;\n  margin-top: 2px;\n}\n\n.suggestion-type[_ngcontent-%COMP%] {\n  margin-left: 8px;\n  flex-shrink: 0;\n}\n\n.type-badge[_ngcontent-%COMP%] {\n  display: inline-block;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.type-badge.type-airport[_ngcontent-%COMP%] {\n  background: #e6fffa;\n  color: #234e52;\n}\n\n.type-badge.type-city[_ngcontent-%COMP%] {\n  background: #fef5e7;\n  color: #744210;\n}\n\n.type-badge.type-country[_ngcontent-%COMP%] {\n  background: #f0f4ff;\n  color: #3c4fe0;\n}\n\n.no-results[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  color: #a0aec0;\n  font-size: 14px;\n  gap: 8px;\n}\n\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\n  font-size: 16px;\n}\n\n\n\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 6px;\n}\n\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.suggestions-dropdown[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n\n\n.suggestions-dropdown[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\n}\n\n@keyframes _ngcontent-%COMP%_slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n\n\n@media (max-width: 768px) {\n  .suggestion-main[_ngcontent-%COMP%] {\n    font-size: 13px;\n  }\n  \n  .suggestion-details[_ngcontent-%COMP%] {\n    font-size: 11px;\n  }\n  \n  .suggestion-item[_ngcontent-%COMP%] {\n    padding: 10px 12px;\n  }\n  \n  .suggestion-icon[_ngcontent-%COMP%] {\n    width: 28px;\n    height: 28px;\n    margin-right: 10px;\n  }\n}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 423:
/*!*******************************************************!*\
  !*** ./src/app/components/signin/signin.component.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SigninComponent: () => (/* binding */ SigninComponent)
/* harmony export */ });
/* harmony import */ var _angular_forms__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/forms */ 4456);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/router */ 5072);
/* harmony import */ var _angular_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common */ 316);






function SigninComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵgetCurrentView"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 39)(1, "div", 40);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](2, "svg", 41);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](3, "circle", 42)(4, "line", 43)(5, "line", 44);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](6, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](8, "button", 45);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function SigninComponent_div_13_Template_button_click_8_listener() {
      _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵrestoreView"](_r9);
      const ctx_r8 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
      return _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵresetView"](ctx_r8.clearError());
    });
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "svg", 46);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](10, "line", 47)(11, "line", 48);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()()();
  }
  if (rf & 2) {
    const ctx_r0 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](7);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate"](ctx_r0.errorMessage);
  }
}
function SigninComponent_div_23_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r1 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r1.getErrorMessage("agency"), " ");
  }
}
function SigninComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r2 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r2.getErrorMessage("username"), " ");
  }
}
function SigninComponent__svg_svg_43_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "svg", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "path", 51)(2, "circle", 52);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function SigninComponent__svg_svg_44_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "svg", 50);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](1, "path", 53)(2, "path", 54)(3, "circle", 52)(4, "path", 55);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function SigninComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 49);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
  if (rf & 2) {
    const ctx_r5 = _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnextContext"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtextInterpolate1"](" ", ctx_r5.getErrorMessage("password"), " ");
  }
}
function SigninComponent_span_47_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](1, "Sign In");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
function SigninComponent_span_48_Template(rf, ctx) {
  if (rf & 1) {
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "span", 56);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](1, "svg", 57);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](2, "path", 58)(3, "path", 59);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](4, "defs")(5, "linearGradient", 60);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "stop", 61)(7, "stop", 62);
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](8, " Signing in... ");
    _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
  }
}
class SigninComponent {
  constructor(formBuilder, authService, router) {
    this.formBuilder = formBuilder;
    this.authService = authService;
    this.router = router;
    this.isLoading = false;
    this.errorMessage = '';
    this.showPassword = false;
    this.signinForm = this.formBuilder.group({
      agency: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(2)]],
      username: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(3)]],
      password: ['', [_angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.required, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.Validators.minLength(6)]]
    });
  }
  ngOnInit() {
    // If user is already authenticated, redirect to dashboard
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }
  /**
   * Handle form submission
   */
  onSubmit() {
    if (this.signinForm.valid) {
      this.isLoading = true;
      this.errorMessage = '';
      const credentials = {
        agency: this.signinForm.value.agency.trim(),
        username: this.signinForm.value.username.trim(),
        password: this.signinForm.value.password
      };
      this.authService.login(credentials).subscribe({
        next: response => {
          if (response.success) {
            // Redirect to dashboard or intended route
            this.router.navigate(['/dashboard']);
          }
        },
        error: error => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Login failed. Please try again.';
          // Clear password field on error
          this.signinForm.patchValue({
            password: ''
          });
        },
        complete: () => {
          this.isLoading = false;
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }
  /**
   * Toggle password visibility
   */
  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }
  /**
   * Mark all form fields as touched to show validation errors
   */
  markFormGroupTouched() {
    Object.keys(this.signinForm.controls).forEach(key => {
      const control = this.signinForm.get(key);
      control?.markAsTouched();
    });
  }
  /**
   * Get form control for easier access in template
   */
  getFormControl(controlName) {
    return this.signinForm.get(controlName);
  }
  /**
   * Check if form control has error
   */
  hasError(controlName, errorType) {
    const control = this.getFormControl(controlName);
    return !!(control?.hasError(errorType) && control?.touched);
  }
  /**
   * Get error message for form control
   */
  getErrorMessage(controlName) {
    const control = this.getFormControl(controlName);
    if (control?.hasError('required')) {
      return `${this.getFieldDisplayName(controlName)} is required`;
    }
    if (control?.hasError('minlength')) {
      const requiredLength = control.errors?.['minlength']?.requiredLength;
      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;
    }
    return '';
  }
  /**
   * Get display name for form field
   */
  getFieldDisplayName(controlName) {
    const fieldNames = {
      agency: 'Agency',
      username: 'Username',
      password: 'Password'
    };
    return fieldNames[controlName] || controlName;
  }
  /**
   * Clear error message
   */
  clearError() {
    this.errorMessage = '';
  }
  static {
    this.ɵfac = function SigninComponent_Factory(t) {
      return new (t || SigninComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormBuilder), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdirectiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_3__.Router));
    };
  }
  static {
    this.ɵcmp = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineComponent"]({
      type: SigninComponent,
      selectors: [["app-signin"]],
      decls: 56,
      vars: 20,
      consts: [[1, "signin-container"], [1, "signin-card"], [1, "signin-header"], [1, "logo-container"], [1, "logo"], ["width", "48", "height", "48", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg"], ["d", "M12 2L2 7L12 12L22 7L12 2Z", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M2 17L12 22L22 17", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M2 12L12 17L22 12", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], [1, "signin-title"], [1, "signin-subtitle"], ["class", "error-container", 4, "ngIf"], [1, "signin-form", 3, "formGroup", "ngSubmit"], [1, "form-group"], ["for", "agency", 1, "form-label"], [1, "input-container"], ["type", "text", "id", "agency", "formControlName", "agency", "placeholder", "Enter your agency code", "autocomplete", "organization", 1, "form-input"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 1, "input-icon"], ["d", "M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["cx", "12", "cy", "7", "r", "4", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["class", "error-text", 4, "ngIf"], ["for", "username", 1, "form-label"], ["type", "text", "id", "username", "formControlName", "username", "placeholder", "Enter your username", "autocomplete", "username", 1, "form-input"], ["for", "password", 1, "form-label"], ["id", "password", "formControlName", "password", "placeholder", "Enter your password", "autocomplete", "current-password", 1, "form-input", 3, "type"], ["x", "3", "y", "11", "width", "18", "height", "11", "rx", "2", "ry", "2", "stroke", "currentColor", "stroke-width", "2"], ["cx", "12", "cy", "16", "r", "1", "stroke", "currentColor", "stroke-width", "2"], ["d", "M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11", "stroke", "currentColor", "stroke-width", "2"], ["type", "button", 1, "password-toggle", 3, "click"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 4, "ngIf"], ["type", "submit", 1, "signin-button", 3, "disabled"], [4, "ngIf"], ["class", "loading-content", 4, "ngIf"], [1, "signin-footer"], [1, "footer-text"], [1, "background-decoration"], [1, "decoration-circle", "decoration-circle-1"], [1, "decoration-circle", "decoration-circle-2"], [1, "decoration-circle", "decoration-circle-3"], [1, "error-container"], [1, "error-message"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "2"], ["x1", "15", "y1", "9", "x2", "9", "y2", "15", "stroke", "currentColor", "stroke-width", "2"], ["x1", "9", "y1", "9", "x2", "15", "y2", "15", "stroke", "currentColor", "stroke-width", "2"], ["type", "button", 1, "error-close", 3, "click"], ["width", "16", "height", "16", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18", "stroke", "currentColor", "stroke-width", "2"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18", "stroke", "currentColor", "stroke-width", "2"], [1, "error-text"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg"], ["d", "M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z", "stroke", "currentColor", "stroke-width", "2"], ["cx", "12", "cy", "12", "r", "3", "stroke", "currentColor", "stroke-width", "2"], ["d", "M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], ["d", "M1 1L23 23", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-linejoin", "round"], [1, "loading-content"], ["width", "20", "height", "20", "viewBox", "0 0 24 24", "fill", "none", "xmlns", "http://www.w3.org/2000/svg", 1, "loading-spinner"], ["d", "M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z", "stroke", "currentColor", "stroke-width", "2"], ["d", "M12 3C16.9706 3 21 7.02944 21 12", "stroke", "url(#spinner-gradient)", "stroke-width", "2", "stroke-linecap", "round"], ["id", "spinner-gradient", "x1", "0%", "y1", "0%", "x2", "100%", "y2", "0%"], ["offset", "0%", 2, "stop-color", "currentColor", "stop-opacity", "0"], ["offset", "100%", 2, "stop-color", "currentColor", "stop-opacity", "1"]],
      template: function SigninComponent_Template(rf, ctx) {
        if (rf & 1) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "div", 4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](5, "svg", 5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](6, "path", 6)(7, "path", 7)(8, "path", 8);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](9, "h1", 9);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](10, "Agency Portal");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](11, "p", 10);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](12, "Sign in to your agency account");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](13, SigninComponent_div_13_Template, 12, 1, "div", 11);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](14, "form", 12);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("ngSubmit", function SigninComponent_Template_form_ngSubmit_14_listener() {
            return ctx.onSubmit();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](15, "div", 13)(16, "label", 14);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](17, "Agency Code");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](18, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](19, "input", 16);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](20, "svg", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](21, "path", 18)(22, "circle", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](23, SigninComponent_div_23_Template, 2, 1, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](24, "div", 13)(25, "label", 21);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](26, "Username");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](27, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](28, "input", 22);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](29, "svg", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](30, "path", 18)(31, "circle", 19);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](32, SigninComponent_div_32_Template, 2, 1, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](33, "div", 13)(34, "label", 23);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](35, "Password");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](36, "div", 15);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](37, "input", 24);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceSVG"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](38, "svg", 17);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](39, "rect", 25)(40, "circle", 26)(41, "path", 27);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵnamespaceHTML"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](42, "button", 28);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵlistener"]("click", function SigninComponent_Template_button_click_42_listener() {
            return ctx.togglePasswordVisibility();
          });
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](43, SigninComponent__svg_svg_43_Template, 3, 0, "svg", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](44, SigninComponent__svg_svg_44_Template, 5, 0, "svg", 29);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](45, SigninComponent_div_45_Template, 2, 1, "div", 20);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](46, "button", 30);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](47, SigninComponent_span_47_Template, 2, 0, "span", 31);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtemplate"](48, SigninComponent_span_48_Template, 9, 0, "span", 32);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](49, "div", 33)(50, "p", 34);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵtext"](51, " Need help? Contact your system administrator ");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()()();
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementStart"](52, "div", 35);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelement"](53, "div", 36)(54, "div", 37)(55, "div", 38);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵelementEnd"]()();
        }
        if (rf & 2) {
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](13);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.errorMessage);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("formGroup", ctx.signinForm);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("error", ctx.hasError("agency", "required") || ctx.hasError("agency", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.hasError("agency", "required") || ctx.hasError("agency", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("error", ctx.hasError("username", "required") || ctx.hasError("username", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](4);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.hasError("username", "required") || ctx.hasError("username", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("error", ctx.hasError("password", "required") || ctx.hasError("password", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("type", ctx.showPassword ? "text" : "password");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](5);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵattribute"]("aria-label", ctx.showPassword ? "Hide password" : "Show password");
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.showPassword);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.showPassword);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.hasError("password", "required") || ctx.hasError("password", "minlength"));
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵclassProp"]("loading", ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("disabled", ctx.isLoading || ctx.signinForm.invalid);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", !ctx.isLoading);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵadvance"](1);
          _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵproperty"]("ngIf", ctx.isLoading);
        }
      },
      dependencies: [_angular_common__WEBPACK_IMPORTED_MODULE_4__.NgIf, _angular_forms__WEBPACK_IMPORTED_MODULE_2__["ɵNgNoValidate"], _angular_forms__WEBPACK_IMPORTED_MODULE_2__.DefaultValueAccessor, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatus, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.NgControlStatusGroup, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormGroupDirective, _angular_forms__WEBPACK_IMPORTED_MODULE_2__.FormControlName],
      styles: ["\n\n[_ngcontent-%COMP%]:root {\n  --primary-color: #3b82f6;\n  --primary-hover: #2563eb;\n  --primary-light: #dbeafe;\n  --secondary-color: #64748b;\n  --success-color: #10b981;\n  --error-color: #ef4444;\n  --warning-color: #f59e0b;\n  --background-color: #f8fafc;\n  --surface-color: #ffffff;\n  --text-primary: #1e293b;\n  --text-secondary: #64748b;\n  --text-muted: #94a3b8;\n  --border-color: #e2e8f0;\n  --border-focus: #3b82f6;\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --radius-sm: 0.375rem;\n  --radius-md: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n}\n\n\n\n.signin-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 1rem;\n  position: relative;\n  overflow: hidden;\n}\n\n@media (max-width: 640px) {\n  .signin-container[_ngcontent-%COMP%] {\n    padding: 0.5rem;\n  }\n}\n\n\n\n.background-decoration[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  z-index: 0;\n}\n\n.decoration-circle[_ngcontent-%COMP%] {\n  position: absolute;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\n}\n\n.decoration-circle.decoration-circle-1[_ngcontent-%COMP%] {\n  width: 200px;\n  height: 200px;\n  top: 10%;\n  left: 10%;\n  animation-delay: 0s;\n}\n\n.decoration-circle.decoration-circle-2[_ngcontent-%COMP%] {\n  width: 150px;\n  height: 150px;\n  top: 60%;\n  right: 15%;\n  animation-delay: 2s;\n}\n\n.decoration-circle.decoration-circle-3[_ngcontent-%COMP%] {\n  width: 100px;\n  height: 100px;\n  bottom: 20%;\n  left: 20%;\n  animation-delay: 4s;\n}\n\n@keyframes _ngcontent-%COMP%_float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n\n\n.signin-card[_ngcontent-%COMP%] {\n  background: var(--surface-color);\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n  padding: 2.5rem;\n  width: 100%;\n  max-width: 420px;\n  position: relative;\n  z-index: 1;\n  -webkit-backdrop-filter: blur(10px);\n          backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n}\n\n@media (max-width: 640px) {\n  .signin-card[_ngcontent-%COMP%] {\n    padding: 2rem 1.5rem;\n    max-width: 100%;\n    margin: 0.5rem;\n  }\n}\n\n\n\n.signin-header[_ngcontent-%COMP%] {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.logo-container[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n\n.logo[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 64px;\n  height: 64px;\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\n  border-radius: var(--radius-xl);\n  color: white;\n  box-shadow: var(--shadow-lg);\n  margin: 0 auto;\n}\n\n.signin-title[_ngcontent-%COMP%] {\n  font-size: 1.875rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin: 0 0 0.5rem 0;\n  line-height: 1.2;\n}\n\n.signin-subtitle[_ngcontent-%COMP%] {\n  font-size: 1rem;\n  color: var(--text-secondary);\n  margin: 0;\n  line-height: 1.5;\n}\n\n\n\n.error-container[_ngcontent-%COMP%] {\n  margin-bottom: 1.5rem;\n}\n\n.error-message[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n  padding: 0.875rem 1rem;\n  background: #fef2f2;\n  border: 1px solid #fecaca;\n  border-radius: var(--radius-md);\n  color: #dc2626;\n  font-size: 0.875rem;\n  line-height: 1.4;\n  position: relative;\n}\n\n.error-icon[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n  color: var(--error-color);\n}\n\n.error-close[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: #dc2626;\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: var(--radius-sm);\n  margin-left: auto;\n  flex-shrink: 0;\n  transition: background-color 0.2s ease;\n}\n\n.error-close[_ngcontent-%COMP%]:hover {\n  background: rgba(220, 38, 38, 0.1);\n}\n\n.error-close[_ngcontent-%COMP%]:focus {\n  outline: 2px solid var(--error-color);\n  outline-offset: 2px;\n}\n\n\n\n.signin-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: 0.5rem;\n}\n\n.form-label[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0;\n}\n\n.input-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.form-input[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.875rem 1rem 0.875rem 3rem;\n  border: 2px solid var(--border-color);\n  border-radius: var(--radius-md);\n  font-size: 1rem;\n  line-height: 1.5;\n  color: var(--text-primary);\n  background: var(--surface-color);\n  transition: all 0.2s ease;\n}\n\n.form-input[_ngcontent-%COMP%]::placeholder {\n  color: var(--text-muted);\n}\n\n.form-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--border-focus);\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\n}\n\n.form-input.error[_ngcontent-%COMP%] {\n  border-color: var(--error-color);\n}\n\n.form-input.error[_ngcontent-%COMP%]:focus {\n  border-color: var(--error-color);\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n.form-input[_ngcontent-%COMP%]:disabled {\n  background: #f8fafc;\n  color: var(--text-muted);\n  cursor: not-allowed;\n}\n\n.input-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: 1rem;\n  color: var(--text-muted);\n  pointer-events: none;\n  z-index: 1;\n}\n\n.password-toggle[_ngcontent-%COMP%] {\n  position: absolute;\n  right: 1rem;\n  background: none;\n  border: none;\n  color: var(--text-muted);\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: var(--radius-sm);\n  transition: color 0.2s ease;\n  z-index: 1;\n}\n\n.password-toggle[_ngcontent-%COMP%]:hover {\n  color: var(--text-secondary);\n}\n\n.password-toggle[_ngcontent-%COMP%]:focus {\n  outline: 2px solid var(--border-focus);\n  outline-offset: 2px;\n}\n\n.error-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: var(--error-color);\n  margin: 0;\n  line-height: 1.4;\n}\n\n\n\n.signin-button[_ngcontent-%COMP%] {\n  width: 100%;\n  padding: 0.875rem 1.5rem;\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\n  color: white;\n  border: none;\n  border-radius: var(--radius-md);\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  box-shadow: var(--shadow-sm);\n  margin-top: 0.5rem;\n}\n\n.signin-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: linear-gradient(135deg, var(--primary-hover), #1d4ed8);\n  box-shadow: var(--shadow-md);\n  transform: translateY(-1px);\n}\n\n.signin-button[_ngcontent-%COMP%]:focus {\n  outline: 2px solid var(--primary-color);\n  outline-offset: 2px;\n}\n\n.signin-button[_ngcontent-%COMP%]:active:not(:disabled) {\n  transform: translateY(0);\n  box-shadow: var(--shadow-sm);\n}\n\n.signin-button[_ngcontent-%COMP%]:disabled {\n  background: var(--text-muted);\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.signin-button.loading[_ngcontent-%COMP%] {\n  background: var(--text-muted);\n  cursor: not-allowed;\n}\n\n.loading-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n}\n\n.loading-spinner[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n\n\n.signin-footer[_ngcontent-%COMP%] {\n  margin-top: 2rem;\n  text-align: center;\n}\n\n.footer-text[_ngcontent-%COMP%] {\n  font-size: 0.875rem;\n  color: var(--text-muted);\n  margin: 0;\n  line-height: 1.4;\n}\n\n\n\n@media (max-width: 480px) {\n  .signin-card[_ngcontent-%COMP%] {\n    padding: 1.5rem 1rem;\n  }\n\n  .signin-title[_ngcontent-%COMP%] {\n    font-size: 1.5rem;\n  }\n\n  .form-input[_ngcontent-%COMP%] {\n    padding: 0.75rem 0.875rem 0.75rem 2.75rem;\n  }\n\n  .input-icon[_ngcontent-%COMP%] {\n    left: 0.875rem;\n  }\n\n  .password-toggle[_ngcontent-%COMP%] {\n    right: 0.875rem;\n  }\n}\n\n\n\n@media (prefers-color-scheme: dark) {\n  [_ngcontent-%COMP%]:root {\n    --background-color: #0f172a;\n    --surface-color: #1e293b;\n    --text-primary: #f1f5f9;\n    --text-secondary: #cbd5e1;\n    --text-muted: #64748b;\n    --border-color: #334155;\n  }\n\n  .signin-card[_ngcontent-%COMP%] {\n    background: rgba(30, 41, 59, 0.95);\n    border: 1px solid rgba(255, 255, 255, 0.1);\n  }\n\n  .form-input[_ngcontent-%COMP%] {\n    background: var(--surface-color);\n    border-color: var(--border-color);\n  }\n}\n\n\n\n@media (prefers-reduced-motion: reduce) {\n  *[_ngcontent-%COMP%] {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n  }\n}\n\n\n\n.signin-button[_ngcontent-%COMP%]:focus-visible, .form-input[_ngcontent-%COMP%]:focus-visible, .password-toggle[_ngcontent-%COMP%]:focus-visible, .error-close[_ngcontent-%COMP%]:focus-visible {\n  outline: 2px solid var(--primary-color);\n  outline-offset: 2px;\n}\n\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */"]
    });
  }
}

/***/ }),

/***/ 1620:
/*!**************************************!*\
  !*** ./src/app/guards/auth.guard.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard)
/* harmony export */ });
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../services/auth.service */ 4796);
/* harmony import */ var _angular_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/router */ 5072);



class AuthGuard {
  constructor(authService, router) {
    this.authService = authService;
    this.router = router;
  }
  canActivate() {
    if (this.authService.isAuthenticated()) {
      return true;
    } else {
      // Redirect to signin page if not authenticated
      return this.router.createUrlTree(['/signin']);
    }
  }
  static {
    this.ɵfac = function AuthGuard_Factory(t) {
      return new (t || AuthGuard)(_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_services_auth_service__WEBPACK_IMPORTED_MODULE_0__.AuthService), _angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵinject"](_angular_router__WEBPACK_IMPORTED_MODULE_2__.Router));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_1__["ɵɵdefineInjectable"]({
      token: AuthGuard,
      factory: AuthGuard.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 4796:
/*!******************************************!*\
  !*** ./src/app/services/auth.service.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthService: () => (/* binding */ AuthService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @angular/core */ 7580);






class AuthService {
  constructor(http) {
    this.http = http;
    this.API_URL = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.apiUrl}${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.authEndpoint}`;
    this.TOKEN_KEY = 'auth_token';
    this.USER_KEY = 'user_data';
    this.isAuthenticatedSubject = new rxjs__WEBPACK_IMPORTED_MODULE_1__.BehaviorSubject(this.hasToken());
    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
    this.currentUserSubject = new rxjs__WEBPACK_IMPORTED_MODULE_1__.BehaviorSubject(this.getCurrentUser());
    this.currentUser$ = this.currentUserSubject.asObservable();
  }
  /**
   * Authenticate user with agency credentials
   */
  login(credentials) {
    const authRequest = {
      Agency: credentials.agency,
      User: credentials.username,
      Password: credentials.password
    };
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(this.API_URL, authRequest, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_3__.map)(response => {
      if (response.header.success && response.body.token) {
        // Store token and user data
        this.setToken(response.body.token);
        const userData = {
          id: response.body.user.id,
          name: response.body.user.name,
          email: response.body.user.email,
          agency: response.body.agency.name,
          agencyCode: response.body.agency.code
        };
        this.setUserData(userData);
        // Update subjects
        this.isAuthenticatedSubject.next(true);
        this.currentUserSubject.next(userData);
        return {
          success: true,
          token: response.body.token,
          user: userData,
          message: 'Login successful'
        };
      } else {
        throw new Error(response.header.responseMessage || 'Authentication failed');
      }
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.catchError)(error => {
      console.error('Login error:', error);
      let errorMessage = 'An error occurred during login';
      if (error.error?.header?.responseMessage) {
        errorMessage = error.error.header.responseMessage;
      } else if (error.message) {
        errorMessage = error.message;
      } else if (error.status === 401) {
        errorMessage = 'Invalid credentials';
      } else if (error.status === 0) {
        errorMessage = 'Unable to connect to server';
      }
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_5__.throwError)(() => ({
        success: false,
        message: errorMessage
      }));
    }));
  }
  /**
   * Logout user
   */
  logout() {
    this.removeToken();
    this.removeUserData();
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
  }
  /**
   * Check if user is authenticated
   */
  isAuthenticated() {
    return this.hasToken() && !this.isTokenExpired();
  }
  /**
   * Get current user data
   */
  getCurrentUser() {
    const userData = localStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }
  /**
   * Get authentication token
   */
  getToken() {
    return localStorage.getItem(this.TOKEN_KEY);
  }
  /**
   * Set authentication token
   */
  setToken(token) {
    localStorage.setItem(this.TOKEN_KEY, token);
  }
  /**
   * Remove authentication token
   */
  removeToken() {
    localStorage.removeItem(this.TOKEN_KEY);
  }
  /**
   * Set user data
   */
  setUserData(userData) {
    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
  }
  /**
   * Remove user data
   */
  removeUserData() {
    localStorage.removeItem(this.USER_KEY);
  }
  /**
   * Check if token exists
   */
  hasToken() {
    return !!localStorage.getItem(this.TOKEN_KEY);
  }
  /**
   * Check if token is expired (basic check)
   * Note: This is a simple implementation. In production, you should decode the JWT token
   * and check the expiration time properly.
   */
  isTokenExpired() {
    // For now, we'll assume the token is valid if it exists
    // In a real implementation, you would decode the JWT and check the exp claim
    return false;
  }
  /**
   * Get authorization headers for API calls
   */
  getAuthHeaders() {
    const token = this.getToken();
    return new _angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
  static {
    this.ɵfac = function AuthService_Factory(t) {
      return new (t || AuthService)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_2__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_6__["ɵɵdefineInjectable"]({
      token: AuthService,
      factory: AuthService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 74:
/*!**************************************************!*\
  !*** ./src/app/services/autocomplete.service.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AutocompleteService: () => (/* binding */ AutocompleteService)
/* harmony export */ });
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 9452);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/core */ 7580);
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @angular/common/http */ 6443);





class AutocompleteService {
  constructor(http) {
    this.http = http;
    this.API_URL = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.apiUrl}/location`;
    // Mock data for demonstration - in real app, this would come from API
    this.mockLocations = [
    // Major airports
    {
      id: 'JFK',
      name: 'John F. Kennedy International Airport',
      code: 'JFK',
      type: 'airport',
      country: 'United States',
      city: 'New York',
      airport: 'JFK',
      displayText: 'New York (JFK) - John F. Kennedy International Airport'
    }, {
      id: 'LAX',
      name: 'Los Angeles International Airport',
      code: 'LAX',
      type: 'airport',
      country: 'United States',
      city: 'Los Angeles',
      airport: 'LAX',
      displayText: 'Los Angeles (LAX) - Los Angeles International Airport'
    }, {
      id: 'LHR',
      name: 'London Heathrow Airport',
      code: 'LHR',
      type: 'airport',
      country: 'United Kingdom',
      city: 'London',
      airport: 'LHR',
      displayText: 'London (LHR) - London Heathrow Airport'
    }, {
      id: 'CDG',
      name: 'Charles de Gaulle Airport',
      code: 'CDG',
      type: 'airport',
      country: 'France',
      city: 'Paris',
      airport: 'CDG',
      displayText: 'Paris (CDG) - Charles de Gaulle Airport'
    }, {
      id: 'DXB',
      name: 'Dubai International Airport',
      code: 'DXB',
      type: 'airport',
      country: 'United Arab Emirates',
      city: 'Dubai',
      airport: 'DXB',
      displayText: 'Dubai (DXB) - Dubai International Airport'
    }, {
      id: 'NRT',
      name: 'Narita International Airport',
      code: 'NRT',
      type: 'airport',
      country: 'Japan',
      city: 'Tokyo',
      airport: 'NRT',
      displayText: 'Tokyo (NRT) - Narita International Airport'
    }, {
      id: 'SIN',
      name: 'Singapore Changi Airport',
      code: 'SIN',
      type: 'airport',
      country: 'Singapore',
      city: 'Singapore',
      airport: 'SIN',
      displayText: 'Singapore (SIN) - Singapore Changi Airport'
    }, {
      id: 'FRA',
      name: 'Frankfurt Airport',
      code: 'FRA',
      type: 'airport',
      country: 'Germany',
      city: 'Frankfurt',
      airport: 'FRA',
      displayText: 'Frankfurt (FRA) - Frankfurt Airport'
    }, {
      id: 'AMS',
      name: 'Amsterdam Airport Schiphol',
      code: 'AMS',
      type: 'airport',
      country: 'Netherlands',
      city: 'Amsterdam',
      airport: 'AMS',
      displayText: 'Amsterdam (AMS) - Amsterdam Airport Schiphol'
    }, {
      id: 'IST',
      name: 'Istanbul Airport',
      code: 'IST',
      type: 'airport',
      country: 'Turkey',
      city: 'Istanbul',
      airport: 'IST',
      displayText: 'Istanbul (IST) - Istanbul Airport'
    }, {
      id: 'TUN',
      name: 'Tunis-Carthage International Airport',
      code: 'TUN',
      type: 'airport',
      country: 'Tunisia',
      city: 'Tunis',
      airport: 'TUN',
      displayText: 'Tunis (TUN) - Tunis-Carthage International Airport'
    }, {
      id: 'CAI',
      name: 'Cairo International Airport',
      code: 'CAI',
      type: 'airport',
      country: 'Egypt',
      city: 'Cairo',
      airport: 'CAI',
      displayText: 'Cairo (CAI) - Cairo International Airport'
    }, {
      id: 'CMN',
      name: 'Mohammed V International Airport',
      code: 'CMN',
      type: 'airport',
      country: 'Morocco',
      city: 'Casablanca',
      airport: 'CMN',
      displayText: 'Casablanca (CMN) - Mohammed V International Airport'
    }, {
      id: 'ALG',
      name: 'Houari Boumediene Airport',
      code: 'ALG',
      type: 'airport',
      country: 'Algeria',
      city: 'Algiers',
      airport: 'ALG',
      displayText: 'Algiers (ALG) - Houari Boumediene Airport'
    },
    // Cities
    {
      id: 'NYC',
      name: 'New York',
      code: 'NYC',
      type: 'city',
      country: 'United States',
      city: 'New York',
      displayText: 'New York, United States'
    }, {
      id: 'LON',
      name: 'London',
      code: 'LON',
      type: 'city',
      country: 'United Kingdom',
      city: 'London',
      displayText: 'London, United Kingdom'
    }, {
      id: 'PAR',
      name: 'Paris',
      code: 'PAR',
      type: 'city',
      country: 'France',
      city: 'Paris',
      displayText: 'Paris, France'
    }, {
      id: 'DUB',
      name: 'Dubai',
      code: 'DUB',
      type: 'city',
      country: 'United Arab Emirates',
      city: 'Dubai',
      displayText: 'Dubai, United Arab Emirates'
    }, {
      id: 'TOK',
      name: 'Tokyo',
      code: 'TOK',
      type: 'city',
      country: 'Japan',
      city: 'Tokyo',
      displayText: 'Tokyo, Japan'
    }, {
      id: 'TUN_CITY',
      name: 'Tunis',
      code: 'TUN',
      type: 'city',
      country: 'Tunisia',
      city: 'Tunis',
      displayText: 'Tunis, Tunisia'
    },
    // Countries
    {
      id: 'US',
      name: 'United States',
      code: 'US',
      type: 'country',
      country: 'United States',
      displayText: 'United States'
    }, {
      id: 'UK',
      name: 'United Kingdom',
      code: 'UK',
      type: 'country',
      country: 'United Kingdom',
      displayText: 'United Kingdom'
    }, {
      id: 'FR',
      name: 'France',
      code: 'FR',
      type: 'country',
      country: 'France',
      displayText: 'France'
    }, {
      id: 'TN',
      name: 'Tunisia',
      code: 'TN',
      type: 'country',
      country: 'Tunisia',
      displayText: 'Tunisia'
    }, {
      id: 'AE',
      name: 'United Arab Emirates',
      code: 'AE',
      type: 'country',
      country: 'United Arab Emirates',
      displayText: 'United Arab Emirates'
    }, {
      id: 'JP',
      name: 'Japan',
      code: 'JP',
      type: 'country',
      country: 'Japan',
      displayText: 'Japan'
    }];
  }
  /**
   * Search locations based on query string
   */
  searchLocations(query) {
    if (!query || query.length < 2) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)([]);
    }
    // For now, use mock data. In production, replace with actual API call
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.mockLocations).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.map)(locations => locations.filter(location => location.name.toLowerCase().includes(query.toLowerCase()) || location.code.toLowerCase().includes(query.toLowerCase()) || location.city && location.city.toLowerCase().includes(query.toLowerCase()) || location.country && location.country.toLowerCase().includes(query.toLowerCase())).slice(0, 10) // Limit to 10 results
    ));
    // Uncomment this for actual API integration:
    /*
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return of([]);
    }
         const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });
         return this.http.get<any>(`${this.API_URL}/search`, {
      headers,
      params: { query, limit: '10' }
    }).pipe(
      map(response => this.mapApiResponseToLocations(response)),
      catchError(error => {
        console.error('Location search error:', error);
        return of([]);
      })
    );
    */
  }
  /**
   * Get popular destinations
   */
  getPopularDestinations() {
    return (0,rxjs__WEBPACK_IMPORTED_MODULE_1__.of)(this.mockLocations.filter(location => ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'TUN', 'IST', 'FRA'].includes(location.code)));
  }
  /**
   * Map API response to AutocompleteLocation format
   */
  mapApiResponseToLocations(response) {
    // This would map the actual API response to our interface
    // Implementation depends on the actual API structure
    return response.data?.map(item => ({
      id: item.id,
      name: item.name,
      code: item.code,
      type: item.type,
      country: item.country?.name,
      city: item.city?.name,
      airport: item.airport?.name,
      displayText: this.formatDisplayText(item)
    })) || [];
  }
  /**
   * Format display text for location
   */
  formatDisplayText(location) {
    if (location.type === 'airport') {
      return `${location.city?.name || location.name} (${location.code}) - ${location.name}`;
    } else if (location.type === 'city') {
      return `${location.name}, ${location.country?.name}`;
    } else {
      return location.name;
    }
  }
  static {
    this.ɵfac = function AutocompleteService_Factory(t) {
      return new (t || AutocompleteService)(_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_4__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_3__["ɵɵdefineInjectable"]({
      token: AutocompleteService,
      factory: AutocompleteService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 2228:
/*!********************************************!*\
  !*** ./src/app/services/flight.service.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FlightService: () => (/* binding */ FlightService)
/* harmony export */ });
/* harmony import */ var _angular_common_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @angular/common/http */ 6443);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rxjs */ 5797);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rxjs */ 7919);
/* harmony import */ var rxjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! rxjs */ 3942);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! rxjs/operators */ 8764);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! rxjs/operators */ 1318);
/* harmony import */ var rxjs_operators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! rxjs/operators */ 271);
/* harmony import */ var _environments_environment__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../environments/environment */ 5312);
/* harmony import */ var _angular_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @angular/core */ 7580);






class FlightService {
  constructor(http) {
    this.http = http;
    this.API_URL = `${_environments_environment__WEBPACK_IMPORTED_MODULE_0__.environment.apiUrl}/product`;
    this.LATEST_SEARCHES_KEY = 'flight_latest_searches';
    this.MAX_LATEST_SEARCHES = 6;
    this.latestSearchesSubject = new rxjs__WEBPACK_IMPORTED_MODULE_1__.BehaviorSubject(this.getLatestSearches());
    this.latestSearches$ = this.latestSearchesSubject.asObservable();
  }
  /**
   * Search for one-way flights
   */
  searchOneWayFlights(searchForm) {
    const request = this.buildOneWayRequest(searchForm);
    return this.searchFlights(request);
  }
  /**
   * Search for round-trip flights
   */
  searchRoundTripFlights(searchForm) {
    const request = this.buildRoundTripRequest(searchForm);
    return this.searchFlights(request);
  }
  /**
   * Search for multi-city flights
   */
  searchMultiCityFlights(searchForm) {
    const request = this.buildMultiCityRequest(searchForm);
    return this.searchFlights(request);
  }
  /**
   * Generic flight search method
   */
  searchFlights(request) {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)('Authentication token not found');
    }
    const headers = new _angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
    return this.http.post(`${this.API_URL}/pricesearch`, request, {
      headers
    }).pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_4__.tap)(response => {
      if (response.header.success) {
        console.log('Flight search successful:', response);
      } else {
        console.error('Flight search failed:', response.header.messages);
      }
    }), (0,rxjs_operators__WEBPACK_IMPORTED_MODULE_5__.catchError)(error => {
      console.error('Flight search error:', error);
      return (0,rxjs__WEBPACK_IMPORTED_MODULE_2__.throwError)(error);
    }));
  }
  /**
   * Build one-way flight request
   */
  buildOneWayRequest(searchForm) {
    return {
      ProductType: 2,
      ServiceTypes: ['1'],
      CheckIn: searchForm.departureDate,
      DepartureLocations: [this.parseLocation(searchForm.from)],
      ArrivalLocations: [this.parseLocation(searchForm.to)],
      Passengers: this.buildPassengers(searchForm.passengers),
      showOnlyNonStopFlight: searchForm.directFlights,
      acceptPendingProviders: false,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      calculateFlightFees: true,
      flightClasses: [this.getFlightClassCode(searchForm.class)],
      Culture: 'en-US',
      Currency: 'USD'
    };
  }
  /**
   * Build round-trip flight request
   */
  buildRoundTripRequest(searchForm) {
    const request = this.buildOneWayRequest(searchForm);
    request.ServiceTypes = ['2']; // Round-trip service type
    request.ReturnDate = searchForm.returnDate;
    request.Night = this.calculateNights(searchForm.departureDate, searchForm.returnDate);
    return request;
  }
  /**
   * Build multi-city flight request
   */
  buildMultiCityRequest(searchForm) {
    const request = this.buildOneWayRequest(searchForm);
    request.ServiceTypes = ['3']; // Multi-city service type
    return request;
  }
  /**
   * Parse location string to Location object
   */
  parseLocation(locationString) {
    // For now, assume the format is "City, Country or Specific Airport"
    // In a real implementation, you'd have a location search service
    const parts = locationString.split(',');
    const code = parts[0].trim().toUpperCase();
    return {
      type: 1,
      id: code,
      name: locationString,
      code: code
    };
  }
  /**
   * Build passengers array from form data
   */
  buildPassengers(passengers) {
    const passengerArray = [];
    if (passengers.adults > 0) {
      passengerArray.push({
        type: 1,
        count: passengers.adults
      }); // Adult
    }

    if (passengers.children > 0) {
      passengerArray.push({
        type: 2,
        count: passengers.children
      }); // Child
    }

    if (passengers.infants > 0) {
      passengerArray.push({
        type: 3,
        count: passengers.infants
      }); // Infant
    }

    return passengerArray;
  }
  /**
   * Get flight class code
   */
  getFlightClassCode(flightClass) {
    switch (flightClass) {
      case 'economy':
        return 1;
      case 'business':
        return 2;
      case 'first':
        return 3;
      default:
        return 1;
    }
  }
  /**
   * Calculate nights between dates
   */
  calculateNights(checkIn, returnDate) {
    const checkInDate = new Date(checkIn);
    const returnDateObj = new Date(returnDate);
    const timeDiff = returnDateObj.getTime() - checkInDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }
  /**
   * Save search to latest searches
   */
  saveLatestSearch(searchForm) {
    const search = {
      id: Date.now().toString(),
      from: searchForm.from,
      to: searchForm.to,
      date: searchForm.departureDate,
      passengers: searchForm.passengers.adults + searchForm.passengers.children + searchForm.passengers.infants,
      searchDate: new Date()
    };
    const searches = this.getLatestSearches();
    searches.unshift(search);
    // Keep only the latest searches
    const limitedSearches = searches.slice(0, this.MAX_LATEST_SEARCHES);
    localStorage.setItem(this.LATEST_SEARCHES_KEY, JSON.stringify(limitedSearches));
    this.latestSearchesSubject.next(limitedSearches);
  }
  /**
   * Get latest searches from localStorage
   */
  getLatestSearches() {
    try {
      const searches = localStorage.getItem(this.LATEST_SEARCHES_KEY);
      return searches ? JSON.parse(searches) : [];
    } catch (error) {
      console.error('Error parsing latest searches:', error);
      return [];
    }
  }
  /**
   * Clear latest searches
   */
  clearLatestSearches() {
    localStorage.removeItem(this.LATEST_SEARCHES_KEY);
    this.latestSearchesSubject.next([]);
  }
  /**
   * Get popular destinations (mock data for now)
   */
  getPopularDestinations() {
    const destinations = [{
      type: 1,
      id: 'TUN',
      name: 'Tunis, Tunisia',
      code: 'TUN'
    }, {
      type: 1,
      id: 'IST',
      name: 'Istanbul, Turkey',
      code: 'IST'
    }, {
      type: 1,
      id: 'CDG',
      name: 'Paris, France',
      code: 'CDG'
    }, {
      type: 1,
      id: 'LHR',
      name: 'London, UK',
      code: 'LHR'
    }, {
      type: 1,
      id: 'DXB',
      name: 'Dubai, UAE',
      code: 'DXB'
    }, {
      type: 1,
      id: 'JFK',
      name: 'New York, USA',
      code: 'JFK'
    }];
    return new rxjs__WEBPACK_IMPORTED_MODULE_6__.Observable(observer => {
      observer.next(destinations);
      observer.complete();
    });
  }
  /**
   * Search locations (mock implementation)
   */
  searchLocations(query) {
    return this.getPopularDestinations().pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.map)(destinations => destinations.filter(dest => dest.name.toLowerCase().includes(query.toLowerCase()) || dest.code.toLowerCase().includes(query.toLowerCase()))));
  }
  static {
    this.ɵfac = function FlightService_Factory(t) {
      return new (t || FlightService)(_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵinject"](_angular_common_http__WEBPACK_IMPORTED_MODULE_3__.HttpClient));
    };
  }
  static {
    this.ɵprov = /*@__PURE__*/_angular_core__WEBPACK_IMPORTED_MODULE_8__["ɵɵdefineInjectable"]({
      token: FlightService,
      factory: FlightService.ɵfac,
      providedIn: 'root'
    });
  }
}

/***/ }),

/***/ 5312:
/*!*****************************************!*\
  !*** ./src/environments/environment.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   environment: () => (/* binding */ environment)
/* harmony export */ });
const environment = {
  production: false,
  apiUrl: 'http://localhost:8080',
  authEndpoint: '/auth/login',
  flightEndpoint: '/product'
};

/***/ }),

/***/ 4429:
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @angular/platform-browser */ 436);
/* harmony import */ var _app_app_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./app/app.module */ 635);


_angular_platform_browser__WEBPACK_IMPORTED_MODULE_1__.platformBrowser().bootstrapModule(_app_app_module__WEBPACK_IMPORTED_MODULE_0__.AppModule).catch(err => console.error(err));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendor"], () => (__webpack_exec__(4429)));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=main.js.map