import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subscription, interval } from 'rxjs';
import { AuthService } from '../../../services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-token-expiry-alert',
  templateUrl: './token-expiry-alert.component.html',
  styleUrls: ['./token-expiry-alert.component.css']
})
export class TokenExpiryAlertComponent implements OnInit, OnDestroy {
  showAlert = false;
  timeRemaining = '';
  private subscription?: Subscription;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Check token expiry every 30 seconds
    this.subscription = interval(30000).subscribe(() => {
      this.checkTokenExpiry();
    });

    // Initial check
    this.checkTokenExpiry();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private checkTokenExpiry(): void {
    if (!this.authService.isAuthenticated()) {
      this.showAlert = false;
      return;
    }

    const expiryDate = this.authService.getTokenExpiry();
    if (!expiryDate) {
      this.showAlert = false;
      return;
    }

    const now = new Date();
    const timeDiff = expiryDate.getTime() - now.getTime();
    
    // Show alert if token expires within 10 minutes
    if (timeDiff > 0 && timeDiff <= 10 * 60 * 1000) {
      this.showAlert = true;
      this.timeRemaining = this.formatTimeRemaining(timeDiff);
    } else if (timeDiff <= 0) {
      // Token has expired
      this.showAlert = false;
      this.authService.logout();
      this.router.navigate(['/signin']);
    } else {
      this.showAlert = false;
    }
  }

  private formatTimeRemaining(milliseconds: number): string {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    return `${minutes}m ${seconds}s`;
  }

  onRefreshToken(): void {
    // Redirect to login to get a new token
    this.router.navigate(['/signin']);
  }

  onDismiss(): void {
    this.showAlert = false;
  }
}
