/* Flight Container */
.flight-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.flight-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: start;
}

/* Flight Search Panel */
.flight-search-panel {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-header {
  margin-bottom: 30px;
}

.search-title {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.search-title i {
  background: linear-gradient(135deg, #3b4371, #5a67d8);
  color: white;
  padding: 12px;
  border-radius: 50%;
  font-size: 20px;
}

.search-title h2 {
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.search-subtitle {
  color: #718096;
  font-size: 14px;
  margin: 0;
  margin-left: 55px;
}

/* Trip Type Selector */
.trip-type-selector {
  display: flex;
  background: #f7fafc;
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 25px;
}

.trip-type-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trip-type-btn.active {
  background: #3b4371;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 67, 113, 0.3);
}

.trip-type-btn:hover:not(.active) {
  background: #e2e8f0;
}

/* Form Sections */
.location-date-section,
.passenger-class-section,
.additional-options {
  margin-bottom: 25px;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

/* Location Inputs */
.location-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.location-input-wrapper i {
  position: absolute;
  left: 15px;
  color: #a0aec0;
  z-index: 2;
}

.location-input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.location-input:focus {
  outline: none;
  border-color: #3b4371;
  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);
}

.swap-btn {
  position: absolute;
  right: 15px;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  color: #4a5568;
  transition: all 0.2s ease;
}

.swap-btn:hover {
  background: #3b4371;
  color: white;
  border-color: #3b4371;
}

/* Flight Segments Layout */
.flight-segments {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.flight-segment {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.segment-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr auto;
  gap: 15px;
  align-items: end;
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input-wrapper .date-icon {
  position: absolute;
  right: 15px;
  color: #a0aec0;
  z-index: 2;
}

/* Date Inputs */
.date-input {
  width: 100%;
  padding: 15px 45px 15px 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.date-input:focus {
  outline: none;
  border-color: #3b4371;
  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);
}

.location-input[readonly] {
  background-color: #f8f9fa;
  color: #6c757d;
}

.remove-segment-btn {
  background: #dc3545;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.remove-segment-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}

.add-sector-section {
  display: flex;
  justify-content: center;
  margin-top: 15px;
}

.add-sector-btn {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-sector-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.add-sector-btn i {
  font-size: 12px;
}

/* Passenger Controls */
.passenger-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: #f7fafc;
}

.passenger-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.passenger-icon {
  color: #4a5568;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.passenger-count {
  font-weight: 600;
  color: #2d3748;
  min-width: 20px;
  text-align: center;
}

.counter-controls {
  display: flex;
  gap: 4px;
}

.counter-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #cbd5e0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  transition: all 0.2s ease;
}

.counter-btn:hover {
  background: #3b4371;
  color: white;
  border-color: #3b4371;
}

.class-selection {
  margin-left: auto;
}

.class-btn {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #4a5568;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.class-btn.active {
  background: #3b4371;
  color: white;
  border-color: #3b4371;
}

.class-btn:hover:not(.active) {
  border-color: #cbd5e0;
  background: #f7fafc;
}

/* Select Inputs */
.airline-select,
.option-select,
.calendar-select {
  width: 100%;
  padding: 15px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.airline-select:focus,
.option-select:focus,
.calendar-select:focus {
  outline: none;
  border-color: #3b4371;
  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);
}

/* Additional Options */
.additional-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.option-group {
  display: flex;
  flex-direction: column;
}

.option-label {
  color: #2d3748;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.calendar-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.calendar-toggle input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.calendar-days {
  flex: 1;
}

.calendar-select {
  padding: 8px 12px;
  font-size: 12px;
}

/* Search Button */
.search-button-section {
  text-align: center;
  margin-top: 30px;
}

.search-btn {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  border: none;
  padding: 18px 60px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Latest Searches Panel */
.latest-searches-panel {
  background: white;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.searches-header {
  margin-bottom: 20px;
}

.searches-header h3 {
  color: #2d3748;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.searches-header p {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

/* Search Items */
.searches-list {
  max-height: 400px;
  overflow-y: auto;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 10px;
  border: 1px solid #f7fafc;
}

.search-item:hover {
  background: #f7fafc;
  border-color: #e2e8f0;
  transform: translateX(5px);
}

.search-icon {
  background: linear-gradient(135deg, #3b4371, #5a67d8);
  color: white;
  padding: 10px;
  border-radius: 50%;
  font-size: 14px;
  flex-shrink: 0;
}

.search-details {
  flex: 1;
}

.search-route {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.4;
}

.search-route strong {
  color: #2d3748;
  font-weight: 600;
}

/* Empty State */
.empty-searches {
  text-align: center;
  padding: 40px 20px;
  color: #a0aec0;
}

.empty-searches i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-searches p {
  font-size: 16px;
  margin: 0 0 5px 0;
  color: #718096;
}

.empty-searches small {
  font-size: 12px;
  color: #a0aec0;
}

/* Clear Button */
.searches-actions {
  margin-top: 20px;
  text-align: center;
}

.clear-btn {
  background: #fed7d7;
  color: #c53030;
  border: 1px solid #feb2b2;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.clear-btn:hover {
  background: #fc8181;
  color: white;
  border-color: #fc8181;
}

/* Error Messages */
.error-message {
  color: #e53e3e;
  font-size: 12px;
  margin-top: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .flight-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .flight-search-panel,
  .latest-searches-panel {
    padding: 20px;
  }

  .additional-options {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .passenger-controls {
    flex-wrap: wrap;
    gap: 15px;
  }

  .search-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .search-subtitle {
    margin-left: 0;
  }

  .segment-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .remove-segment-btn {
    justify-self: center;
    margin-top: 10px;
  }
}

@media (max-width: 480px) {
  .flight-container {
    padding: 10px;
  }

  .trip-type-selector {
    flex-direction: column;
    gap: 4px;
  }

  .trip-type-btn {
    text-align: center;
  }

  .search-btn {
    width: 100%;
    padding: 15px;
  }
}
