{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction FlightComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"from\"), \" \");\n  }\n}\nfunction FlightComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"to\"), \" \");\n  }\n}\nfunction FlightComponent_span_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1, \"Choose A Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getErrorMessage(\"departureDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_43_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 76);\n    i0.ɵɵtext(1, \"Choose A Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_div_43_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getErrorMessage(\"returnDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25);\n    i0.ɵɵelement(2, \"input\", 77);\n    i0.ɵɵtemplate(3, FlightComponent_div_43_span_3_Template, 2, 0, \"span\", 27);\n    i0.ɵɵelement(4, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, FlightComponent_div_43_div_5_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"has-value\", (tmp_0_0 = ctx_r4.flightForm.get(\"returnDate\")) == null ? null : tmp_0_0.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !((tmp_1_0 = ctx_r4.flightForm.get(\"returnDate\")) == null ? null : tmp_1_0.value));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getErrorMessage(\"returnDate\"));\n  }\n}\nfunction FlightComponent_option_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r14.label, \" \");\n  }\n}\nfunction FlightComponent_span_122_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 78);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r16.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r16.label, \" \");\n  }\n}\nfunction FlightComponent_span_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79)(1, \"select\", 80);\n    i0.ɵɵtemplate(2, FlightComponent_span_122_option_2_Template, 2, 2, \"option\", 61);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.calendarDays);\n  }\n}\nfunction FlightComponent_span_125_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"SEARCH NOW\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_span_126_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 81);\n    i0.ɵɵtext(2, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_div_134_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_134_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const search_r17 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadLatestSearch(search_r17));\n    });\n    i0.ɵɵelementStart(1, \"div\", 83);\n    i0.ɵɵelement(2, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 84)(4, \"div\", 85);\n    i0.ɵɵtext(5, \" Coming from \");\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" - \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const search_r17 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(search_r17.from);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(search_r17.to);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" on \", i0.ɵɵpipeBind2(12, 3, search_r17.date, \"MMM d, yyyy\"), \" \");\n  }\n}\nfunction FlightComponent_div_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵelement(1, \"i\", 87);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No recent searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Your recent flight searches will appear here\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightComponent_div_138_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_138_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.clearLatestSearches());\n    });\n    i0.ɵɵelement(2, \"i\", 90);\n    i0.ɵɵtext(3, \" Clear All \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FlightComponent {\n  constructor(fb, flightService, authService, router) {\n    this.fb = fb;\n    this.flightService = flightService;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.currentUser = null;\n    // Form state\n    this.tripType = 'oneWay';\n    this.showReturnDate = false;\n    this.showCalendar = false;\n    // Passenger counts\n    this.adultCount = 1;\n    this.childCount = 0;\n    this.infantCount = 0;\n    // Class selection\n    this.selectedClass = 'economy';\n    // Baggage options\n    this.baggageOptions = [{\n      value: 'all',\n      label: '--All--'\n    }, {\n      value: '20kg',\n      label: '20kg'\n    }, {\n      value: '30kg',\n      label: '30kg'\n    }, {\n      value: 'extra',\n      label: 'Extra'\n    }];\n    // Calendar options\n    this.calendarDays = [{\n      value: '1',\n      label: '+/- 1 Days'\n    }, {\n      value: '3',\n      label: '+/- 3 Days'\n    }, {\n      value: '7',\n      label: '+/- 7 Days'\n    }];\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n  ngOnInit() {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n    // Set default dates\n    this.setDefaultDates();\n  }\n  /**\n   * Create reactive form\n   */\n  createForm() {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  setDefaultDates() {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n  /**\n   * Format date for input field\n   */\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type) {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n    this.flightForm.patchValue({\n      tripType: type\n    });\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n  }\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type, increment) {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n    this.flightForm.patchValue({\n      [type]: newValue\n    });\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers() {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass) {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({\n      class: flightClass\n    });\n  }\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar() {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({\n      calendar: this.showCalendar\n    });\n  }\n  /**\n   * Swap from and to locations\n   */\n  swapLocations() {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n      const formValue = this.flightForm.value;\n      const searchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n      // Perform search based on trip type\n      let searchObservable;\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n      searchObservable.subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n\n        error: error => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search) {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches() {\n    this.flightService.clearLatestSearches();\n  }\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName) {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function FlightComponent_Factory(t) {\n      return new (t || FlightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightComponent,\n      selectors: [[\"app-flight\"]],\n      decls: 140,\n      vars: 33,\n      consts: [[1, \"flight-container\"], [1, \"flight-content\"], [1, \"flight-search-panel\"], [1, \"search-header\"], [1, \"search-title\"], [1, \"fas\", \"fa-plane\"], [1, \"search-subtitle\"], [1, \"flight-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-selector\"], [\"type\", \"button\", 1, \"trip-type-btn\", 3, \"click\"], [1, \"location-date-section\"], [1, \"location-date-row\"], [1, \"form-group\", \"location-group\"], [\"for\", \"from\"], [1, \"location-input-wrapper\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"id\", \"from\", \"formControlName\", \"from\", \"placeholder\", \"Leaving from (City, Country or Specific Airport)\", 1, \"location-input\"], [\"type\", \"button\", 1, \"swap-btn\", 3, \"click\"], [1, \"fas\", \"fa-exchange-alt\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"to\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"id\", \"to\", \"formControlName\", \"to\", \"placeholder\", \"Going to (City, Country or Specific Airport)\", 1, \"location-input\"], [1, \"date-row\"], [1, \"form-group\", \"date-group\"], [1, \"date-input-wrapper\"], [\"type\", \"date\", \"id\", \"departureDate\", \"formControlName\", \"departureDate\", 1, \"date-input\"], [\"class\", \"date-placeholder\", 4, \"ngIf\"], [1, \"fas\", \"fa-calendar-alt\", \"date-icon\"], [\"class\", \"form-group date-group\", 4, \"ngIf\"], [1, \"passenger-class-section\"], [1, \"form-group\", \"passenger-group\"], [1, \"passenger-controls\"], [1, \"passenger-type\"], [1, \"passenger-icon\"], [1, \"fas\", \"fa-user\"], [1, \"passenger-count\"], [1, \"counter-controls\"], [\"type\", \"button\", 1, \"counter-btn\", 3, \"click\"], [1, \"fas\", \"fa-child\"], [1, \"fas\", \"fa-baby\"], [1, \"class-selection\"], [\"type\", \"button\", 1, \"class-btn\", 3, \"click\"], [1, \"fas\", \"fa-chair\"], [1, \"form-group\", \"airline-group\"], [\"for\", \"preferredAirline\"], [\"id\", \"preferredAirline\", \"formControlName\", \"preferredAirline\", 1, \"airline-select\"], [\"value\", \"\"], [\"value\", \"TK\"], [\"value\", \"AF\"], [\"value\", \"LH\"], [\"value\", \"EK\"], [\"value\", \"QR\"], [1, \"additional-options\"], [1, \"option-group\"], [1, \"option-label\"], [1, \"option-controls\"], [\"formControlName\", \"refundableFares\", 1, \"option-select\"], [\"value\", \"false\"], [\"value\", \"true\"], [\"formControlName\", \"baggage\", 1, \"option-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-toggle\"], [\"type\", \"checkbox\", \"id\", \"calendar\", \"formControlName\", \"calendar\", 3, \"change\"], [\"for\", \"calendar\", 1, \"calendar-label\"], [\"class\", \"calendar-days\", 4, \"ngIf\"], [1, \"search-button-section\"], [\"type\", \"submit\", 1, \"search-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"latest-searches-panel\"], [1, \"searches-header\"], [1, \"searches-list\"], [\"class\", \"search-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-searches\", 4, \"ngIf\"], [\"class\", \"searches-actions\", 4, \"ngIf\"], [1, \"error-message\"], [1, \"date-placeholder\"], [\"type\", \"date\", \"id\", \"returnDate\", \"formControlName\", \"returnDate\", 1, \"date-input\"], [3, \"value\"], [1, \"calendar-days\"], [\"formControlName\", \"calendarDays\", 1, \"calendar-select\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"search-item\", 3, \"click\"], [1, \"search-icon\"], [1, \"search-details\"], [1, \"search-route\"], [1, \"empty-searches\"], [1, \"fas\", \"fa-search\"], [1, \"searches-actions\"], [\"type\", \"button\", 1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"]],\n      template: function FlightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\");\n          i0.ɵɵtext(7, \"Search and Book Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function FlightComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_12_listener() {\n            return ctx.onTripTypeChange(\"oneWay\");\n          });\n          i0.ɵɵtext(13, \" One way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_14_listener() {\n            return ctx.onTripTypeChange(\"roundTrip\");\n          });\n          i0.ɵɵtext(15, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_16_listener() {\n            return ctx.onTripTypeChange(\"multiCity\");\n          });\n          i0.ɵɵtext(17, \" Multi-City/Stop-Overs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"label\", 13);\n          i0.ɵɵtext(22, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 14);\n          i0.ɵɵelement(24, \"i\", 15)(25, \"input\", 16);\n          i0.ɵɵelementStart(26, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_26_listener() {\n            return ctx.swapLocations();\n          });\n          i0.ɵɵelement(27, \"i\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(28, FlightComponent_div_28_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 12)(30, \"label\", 20);\n          i0.ɵɵtext(31, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 14);\n          i0.ɵɵelement(33, \"i\", 21)(34, \"input\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(35, FlightComponent_div_35_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 23)(37, \"div\", 24)(38, \"div\", 25);\n          i0.ɵɵelement(39, \"input\", 26);\n          i0.ɵɵtemplate(40, FlightComponent_span_40_Template, 2, 0, \"span\", 27);\n          i0.ɵɵelement(41, \"i\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(42, FlightComponent_div_42_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(43, FlightComponent_div_43_Template, 6, 4, \"div\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 30)(45, \"div\", 31)(46, \"label\");\n          i0.ɵɵtext(47, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 32)(49, \"div\", 33)(50, \"span\", 34);\n          i0.ɵɵelement(51, \"i\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"span\", 36);\n          i0.ɵɵtext(53);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 37)(55, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_55_listener() {\n            return ctx.updatePassengerCount(\"adults\", false);\n          });\n          i0.ɵɵtext(56, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_57_listener() {\n            return ctx.updatePassengerCount(\"adults\", true);\n          });\n          i0.ɵɵtext(58, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 33)(60, \"span\", 34);\n          i0.ɵɵelement(61, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"span\", 36);\n          i0.ɵɵtext(63);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"div\", 37)(65, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_65_listener() {\n            return ctx.updatePassengerCount(\"children\", false);\n          });\n          i0.ɵɵtext(66, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_67_listener() {\n            return ctx.updatePassengerCount(\"children\", true);\n          });\n          i0.ɵɵtext(68, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"div\", 33)(70, \"span\", 34);\n          i0.ɵɵelement(71, \"i\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\", 36);\n          i0.ɵɵtext(73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 37)(75, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_75_listener() {\n            return ctx.updatePassengerCount(\"infants\", false);\n          });\n          i0.ɵɵtext(76, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_77_listener() {\n            return ctx.updatePassengerCount(\"infants\", true);\n          });\n          i0.ɵɵtext(78, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"div\", 41)(80, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_80_listener() {\n            return ctx.onClassChange(\"economy\");\n          });\n          i0.ɵɵelement(81, \"i\", 43);\n          i0.ɵɵtext(82, \" Economy \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(83, \"div\", 44)(84, \"label\", 45);\n          i0.ɵɵtext(85, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"select\", 46)(87, \"option\", 47);\n          i0.ɵɵtext(88, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"option\", 48);\n          i0.ɵɵtext(90, \"Turkish Airlines\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"option\", 49);\n          i0.ɵɵtext(92, \"Air France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"option\", 50);\n          i0.ɵɵtext(94, \"Lufthansa\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"option\", 51);\n          i0.ɵɵtext(96, \"Emirates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"option\", 52);\n          i0.ɵɵtext(98, \"Qatar Airways\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(99, \"div\", 53)(100, \"div\", 54)(101, \"label\", 55);\n          i0.ɵɵtext(102, \"Refundable fares\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"div\", 56)(104, \"select\", 57)(105, \"option\", 58);\n          i0.ɵɵtext(106, \"--All--\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"option\", 59);\n          i0.ɵɵtext(108, \"Refundable Only\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(109, \"div\", 54)(110, \"label\", 55);\n          i0.ɵɵtext(111, \"Baggage\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 56)(113, \"select\", 60);\n          i0.ɵɵtemplate(114, FlightComponent_option_114_Template, 2, 2, \"option\", 61);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(115, \"div\", 54)(116, \"label\", 55);\n          i0.ɵɵtext(117, \"Calendar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"div\", 56)(119, \"div\", 62)(120, \"input\", 63);\n          i0.ɵɵlistener(\"change\", function FlightComponent_Template_input_change_120_listener() {\n            return ctx.toggleCalendar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"label\", 64);\n          i0.ɵɵtemplate(122, FlightComponent_span_122_Template, 3, 1, \"span\", 65);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(123, \"div\", 66)(124, \"button\", 67);\n          i0.ɵɵtemplate(125, FlightComponent_span_125_Template, 2, 0, \"span\", 68);\n          i0.ɵɵtemplate(126, FlightComponent_span_126_Template, 3, 0, \"span\", 68);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(127, \"div\", 69)(128, \"div\", 70)(129, \"h3\");\n          i0.ɵɵtext(130, \"Latest Searches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"p\");\n          i0.ɵɵtext(132, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(133, \"div\", 71);\n          i0.ɵɵtemplate(134, FlightComponent_div_134_Template, 13, 6, \"div\", 72);\n          i0.ɵɵpipe(135, \"async\");\n          i0.ɵɵtemplate(136, FlightComponent_div_136_Template, 6, 0, \"div\", 73);\n          i0.ɵɵpipe(137, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(138, FlightComponent_div_138_Template, 4, 0, \"div\", 74);\n          i0.ɵɵpipe(139, \"async\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_20_0;\n          let tmp_21_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.flightForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"oneWay\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"roundTrip\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"multiCity\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"from\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"to\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"has-value\", (tmp_6_0 = ctx.flightForm.get(\"departureDate\")) == null ? null : tmp_6_0.value);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_7_0 = ctx.flightForm.get(\"departureDate\")) == null ? null : tmp_7_0.value));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"departureDate\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showReturnDate);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_10_0 = ctx.flightForm.get(\"adults\")) == null ? null : tmp_10_0.value) || 1);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_11_0 = ctx.flightForm.get(\"children\")) == null ? null : tmp_11_0.value) || 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_12_0 = ctx.flightForm.get(\"infants\")) == null ? null : tmp_12_0.value) || 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.selectedClass === \"economy\");\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baggageOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCalendar);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.flightForm.valid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(135, 27, ctx.latestSearches$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = i0.ɵɵpipeBind1(137, 29, ctx.latestSearches$)) == null ? null : tmp_20_0.length) === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = i0.ɵɵpipeBind1(139, 31, ctx.latestSearches$)) == null ? null : tmp_21_0.length) > 0);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.AsyncPipe, i5.DatePipe],\n      styles: [\"\\n\\n.flight-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n.flight-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 30px;\\n  align-items: start;\\n}\\n\\n\\n\\n.flight-search-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 30px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.search-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 12px;\\n  border-radius: 50%;\\n  font-size: 20px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.search-subtitle[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n  margin-left: 55px;\\n}\\n\\n\\n\\n.trip-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  padding: 4px;\\n  margin-bottom: 25px;\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: none;\\n  background: transparent;\\n  color: #4a5568;\\n  font-size: 14px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.trip-type-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  box-shadow: 0 2px 8px rgba(59, 67, 113, 0.3);\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e2e8f0;\\n}\\n\\n\\n\\n.location-date-section[_ngcontent-%COMP%], .passenger-class-section[_ngcontent-%COMP%], .additional-options[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n\\n\\n.location-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.location-input-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  color: #a0aec0;\\n  z-index: 2;\\n}\\n\\n.location-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 15px 15px 45px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.location-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n.swap-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  padding: 8px;\\n  cursor: pointer;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.swap-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n\\n\\n.location-date-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n  margin-bottom: 20px;\\n}\\n\\n.date-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 20px;\\n}\\n\\n\\n\\n.date-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.date-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 45px 15px 15px;\\n  border: 2px solid #3b4371;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n  background: white;\\n  color: transparent;\\n}\\n\\n.date-input.has-value[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n}\\n\\n.date-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n  color: #2d3748;\\n}\\n\\n.date-placeholder[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  color: #a0aec0;\\n  font-size: 14px;\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.date-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  color: #3b4371;\\n  font-size: 16px;\\n  pointer-events: none;\\n  z-index: 2;\\n}\\n\\n\\n\\n.passenger-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: #f7fafc;\\n}\\n\\n.passenger-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n\\n.passenger-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.counter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 1px solid #cbd5e0;\\n  background: white;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-selection[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.class-btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: 2px solid #e2e8f0;\\n  background: white;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.class-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  border-color: #cbd5e0;\\n  background: #f7fafc;\\n}\\n\\n\\n\\n.airline-select[_ngcontent-%COMP%], .option-select[_ngcontent-%COMP%], .calendar-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  background: white;\\n  cursor: pointer;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.airline-select[_ngcontent-%COMP%]:focus, .option-select[_ngcontent-%COMP%]:focus, .calendar-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n\\n\\n.additional-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n}\\n\\n.option-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.option-label[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.calendar-select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.search-button-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n}\\n\\n.search-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  border: none;\\n  padding: 18px 60px;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n\\n\\n.latest-searches-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 25px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n\\n.searches-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n\\n\\n.searches-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.search-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 10px;\\n  border: 1px solid #f7fafc;\\n}\\n\\n.search-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  border-color: #e2e8f0;\\n  transform: translateX(5px);\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 10px;\\n  border-radius: 50%;\\n  font-size: 14px;\\n  flex-shrink: 0;\\n}\\n\\n.search-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.search-route[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.search-route[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.empty-searches[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a0aec0;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n  opacity: 0.5;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 5px 0;\\n  color: #718096;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a0aec0;\\n}\\n\\n\\n\\n.searches-actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  background: #fed7d7;\\n  color: #c53030;\\n  border: 1px solid #feb2b2;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  background: #fc8181;\\n  color: white;\\n  border-color: #fc8181;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .flight-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n\\n  .flight-search-panel[_ngcontent-%COMP%], .latest-searches-panel[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  .additional-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n\\n  .passenger-controls[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 15px;\\n  }\\n\\n  .search-title[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n\\n  .search-subtitle[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .flight-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n\\n  .trip-type-selector[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .trip-type-btn[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n\\n  .search-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 15px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ctx_r1", "ctx_r3", "ctx_r13", "ɵɵelement", "ɵɵtemplate", "FlightComponent_div_43_span_3_Template", "FlightComponent_div_43_div_5_Template", "ɵɵclassProp", "tmp_0_0", "ctx_r4", "flightForm", "get", "value", "ɵɵproperty", "tmp_1_0", "option_r14", "label", "option_r16", "FlightComponent_span_122_option_2_Template", "ctx_r6", "calendarDays", "ɵɵlistener", "FlightComponent_div_134_Template_div_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r19", "search_r17", "$implicit", "ctx_r18", "ɵɵnextContext", "ɵɵresetView", "loadLatestSearch", "ɵɵtextInterpolate", "from", "to", "ɵɵpipeBind2", "date", "FlightComponent_div_138_Template_button_click_1_listener", "_r21", "ctx_r20", "clearLatestSearches", "FlightComponent", "constructor", "fb", "flightService", "authService", "router", "isLoading", "currentUser", "tripType", "showReturnDate", "showCalendar", "adultCount", "childCount", "infantCount", "selectedClass", "baggageOptions", "createForm", "latestSearches$", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "setDefaultDates", "group", "required", "departureDate", "returnDate", "adults", "min", "children", "infants", "class", "preferredAirline", "directFlights", "refundableFares", "baggage", "calendar", "today", "Date", "tomorrow", "setDate", "getDate", "nextWeek", "patchValue", "formatDate", "toISOString", "split", "onTripTypeChange", "type", "setValidators", "clearValidators", "updateValueAndValidity", "updatePassengerCount", "increment", "currentValue", "newValue", "Math", "max", "getTotalPassengers", "onClassChange", "flightClass", "toggleCalendar", "swapLocations", "onSubmit", "valid", "formValue", "searchForm", "passengers", "saveLatestSearch", "searchObservable", "searchOneWayFlights", "searchRoundTripFlights", "searchMultiCityFlights", "next", "response", "header", "success", "console", "log", "error", "messages", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "search", "controlName", "control", "errors", "touched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightService", "i3", "AuthService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "FlightComponent_Template", "rf", "ctx", "FlightComponent_Template_form_ngSubmit_10_listener", "FlightComponent_Template_button_click_12_listener", "FlightComponent_Template_button_click_14_listener", "FlightComponent_Template_button_click_16_listener", "FlightComponent_Template_button_click_26_listener", "FlightComponent_div_28_Template", "FlightComponent_div_35_Template", "FlightComponent_span_40_Template", "FlightComponent_div_42_Template", "FlightComponent_div_43_Template", "FlightComponent_Template_button_click_55_listener", "FlightComponent_Template_button_click_57_listener", "FlightComponent_Template_button_click_65_listener", "FlightComponent_Template_button_click_67_listener", "FlightComponent_Template_button_click_75_listener", "FlightComponent_Template_button_click_77_listener", "FlightComponent_Template_button_click_80_listener", "FlightComponent_option_114_Template", "FlightComponent_Template_input_change_120_listener", "FlightComponent_span_122_Template", "FlightComponent_span_125_Template", "FlightComponent_span_126_Template", "FlightComponent_div_134_Template", "FlightComponent_div_136_Template", "FlightComponent_div_138_Template", "tmp_6_0", "tmp_7_0", "tmp_10_0", "tmp_11_0", "tmp_12_0", "ɵɵpipeBind1", "tmp_20_0", "length", "tmp_21_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { FlightService } from '../../services/flight.service';\nimport { AuthService } from '../../services/auth.service';\nimport { FlightSearchForm, LatestSearch, Location } from '../../models/flight.models';\nimport { Observable } from 'rxjs';\n\n@Component({\n  selector: 'app-flight',\n  templateUrl: './flight.component.html',\n  styleUrls: ['./flight.component.css']\n})\nexport class FlightComponent implements OnInit {\n  flightForm: FormGroup;\n  isLoading = false;\n  currentUser: any = null;\n  latestSearches$: Observable<LatestSearch[]>;\n  \n  // Form state\n  tripType = 'oneWay';\n  showReturnDate = false;\n  showCalendar = false;\n  \n  // Passenger counts\n  adultCount = 1;\n  childCount = 0;\n  infantCount = 0;\n  \n  // Class selection\n  selectedClass = 'economy';\n  \n  // Baggage options\n  baggageOptions = [\n    { value: 'all', label: '--All--' },\n    { value: '20kg', label: '20kg' },\n    { value: '30kg', label: '30kg' },\n    { value: 'extra', label: 'Extra' }\n  ];\n  \n  // Calendar options\n  calendarDays = [\n    { value: '1', label: '+/- 1 Days' },\n    { value: '3', label: '+/- 3 Days' },\n    { value: '7', label: '+/- 7 Days' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private flightService: FlightService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n\n  ngOnInit(): void {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n\n    // Set default dates\n    this.setDefaultDates();\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  private setDefaultDates(): void {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    \n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n\n  /**\n   * Format date for input field\n   */\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type: string): void {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n    \n    this.flightForm.patchValue({ tripType: type });\n    \n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n  }\n\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n    \n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n    \n    this.flightForm.patchValue({ [type]: newValue });\n    \n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers(): number {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass: string): void {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({ class: flightClass });\n  }\n\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar(): void {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({ calendar: this.showCalendar });\n  }\n\n  /**\n   * Swap from and to locations\n   */\n  swapLocations(): void {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n    \n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n      \n      const formValue = this.flightForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n\n      // Perform search based on trip type\n      let searchObservable;\n      \n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n\n      searchObservable.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search: LatestSearch): void {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches(): void {\n    this.flightService.clearLatestSearches();\n  }\n\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"flight-container\">\n  <!-- Main Content -->\n  <div class=\"flight-content\">\n    <!-- Left Panel - Flight Search -->\n    <div class=\"flight-search-panel\">\n      <div class=\"search-header\">\n        <div class=\"search-title\">\n          <i class=\"fas fa-plane\"></i>\n          <h2>Search and Book Flights</h2>\n        </div>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n\n      <form [formGroup]=\"flightForm\" (ngSubmit)=\"onSubmit()\" class=\"flight-form\">\n        <!-- Trip Type Selection -->\n        <div class=\"trip-type-selector\">\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'oneWay'\"\n            (click)=\"onTripTypeChange('oneWay')\">\n            One way\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'roundTrip'\"\n            (click)=\"onTripTypeChange('roundTrip')\">\n            Round Trip\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'multiCity'\"\n            (click)=\"onTripTypeChange('multiCity')\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location and Date Selection -->\n        <div class=\"location-date-section\">\n          <div class=\"location-date-row\">\n            <!-- From Location -->\n            <div class=\"form-group location-group\">\n              <label for=\"from\">From</label>\n              <div class=\"location-input-wrapper\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <input\n                  type=\"text\"\n                  id=\"from\"\n                  formControlName=\"from\"\n                  placeholder=\"Leaving from (City, Country or Specific Airport)\"\n                  class=\"location-input\">\n                <button type=\"button\" class=\"swap-btn\" (click)=\"swapLocations()\">\n                  <i class=\"fas fa-exchange-alt\"></i>\n                </button>\n              </div>\n              <div class=\"error-message\" *ngIf=\"getErrorMessage('from')\">\n                {{ getErrorMessage('from') }}\n              </div>\n            </div>\n\n            <!-- To Location -->\n            <div class=\"form-group location-group\">\n              <label for=\"to\">To</label>\n              <div class=\"location-input-wrapper\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <input\n                  type=\"text\"\n                  id=\"to\"\n                  formControlName=\"to\"\n                  placeholder=\"Going to (City, Country or Specific Airport)\"\n                  class=\"location-input\">\n              </div>\n              <div class=\"error-message\" *ngIf=\"getErrorMessage('to')\">\n                {{ getErrorMessage('to') }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"date-row\">\n            <!-- Departure Date -->\n            <div class=\"form-group date-group\">\n              <div class=\"date-input-wrapper\">\n                <input\n                  type=\"date\"\n                  id=\"departureDate\"\n                  formControlName=\"departureDate\"\n                  class=\"date-input\"\n                  [class.has-value]=\"flightForm.get('departureDate')?.value\">\n                <span class=\"date-placeholder\" *ngIf=\"!flightForm.get('departureDate')?.value\">Choose A Date</span>\n                <i class=\"fas fa-calendar-alt date-icon\"></i>\n              </div>\n              <div class=\"error-message\" *ngIf=\"getErrorMessage('departureDate')\">\n                {{ getErrorMessage('departureDate') }}\n              </div>\n            </div>\n\n            <!-- Return Date (for Round Trip) -->\n            <div class=\"form-group date-group\" *ngIf=\"showReturnDate\">\n              <div class=\"date-input-wrapper\">\n                <input\n                  type=\"date\"\n                  id=\"returnDate\"\n                  formControlName=\"returnDate\"\n                  class=\"date-input\"\n                  [class.has-value]=\"flightForm.get('returnDate')?.value\">\n                <span class=\"date-placeholder\" *ngIf=\"!flightForm.get('returnDate')?.value\">Choose A Date</span>\n                <i class=\"fas fa-calendar-alt date-icon\"></i>\n              </div>\n              <div class=\"error-message\" *ngIf=\"getErrorMessage('returnDate')\">\n                {{ getErrorMessage('returnDate') }}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger and Class Selection -->\n        <div class=\"passenger-class-section\">\n          <!-- Passenger Count -->\n          <div class=\"form-group passenger-group\">\n            <label>Passenger & Class of travel</label>\n            <div class=\"passenger-controls\">\n              <!-- Adults -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-user\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('adults')?.value || 1 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Children -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-child\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('children')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Infants -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-baby\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('infants')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Class Selection -->\n              <div class=\"class-selection\">\n                <button\n                  type=\"button\"\n                  class=\"class-btn\"\n                  [class.active]=\"selectedClass === 'economy'\"\n                  (click)=\"onClassChange('economy')\">\n                  <i class=\"fas fa-chair\"></i> Economy\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Preferred Airline -->\n          <div class=\"form-group airline-group\">\n            <label for=\"preferredAirline\">Preferred Airline</label>\n            <select id=\"preferredAirline\" formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option value=\"TK\">Turkish Airlines</option>\n              <option value=\"AF\">Air France</option>\n              <option value=\"LH\">Lufthansa</option>\n              <option value=\"EK\">Emirates</option>\n              <option value=\"QR\">Qatar Airways</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Additional Options -->\n        <div class=\"additional-options\">\n          <!-- Refundable Fares -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"refundableFares\" class=\"option-select\">\n                <option value=\"false\">--All--</option>\n                <option value=\"true\">Refundable Only</option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Baggage -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"baggage\" class=\"option-select\">\n                <option *ngFor=\"let option of baggageOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Calendar -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"option-controls\">\n              <div class=\"calendar-toggle\">\n                <input\n                  type=\"checkbox\"\n                  id=\"calendar\"\n                  formControlName=\"calendar\"\n                  (change)=\"toggleCalendar()\">\n                <label for=\"calendar\" class=\"calendar-label\">\n                  <span class=\"calendar-days\" *ngIf=\"showCalendar\">\n                    <select formControlName=\"calendarDays\" class=\"calendar-select\">\n                      <option *ngFor=\"let option of calendarDays\" [value]=\"option.value\">\n                        {{ option.label }}\n                      </option>\n                    </select>\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <div class=\"search-button-section\">\n          <button\n            type=\"submit\"\n            class=\"search-btn\"\n            [disabled]=\"isLoading || !flightForm.valid\">\n            <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n            <span *ngIf=\"isLoading\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Searching...\n            </span>\n          </button>\n        </div>\n      </form>\n    </div>\n\n    <!-- Right Panel - Latest Searches -->\n    <div class=\"latest-searches-panel\">\n      <div class=\"searches-header\">\n        <h3>Latest Searches</h3>\n        <p>We're bringing you a new level of comfort</p>\n      </div>\n\n      <div class=\"searches-list\">\n        <div\n          *ngFor=\"let search of latestSearches$ | async\"\n          class=\"search-item\"\n          (click)=\"loadLatestSearch(search)\">\n          <div class=\"search-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <div class=\"search-details\">\n            <div class=\"search-route\">\n              Coming from <strong>{{ search.from }}</strong> - <strong>{{ search.to }}</strong> on {{ search.date | date:'MMM d, yyyy' }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div *ngIf=\"(latestSearches$ | async)?.length === 0\" class=\"empty-searches\">\n          <i class=\"fas fa-search\"></i>\n          <p>No recent searches</p>\n          <small>Your recent flight searches will appear here</small>\n        </div>\n      </div>\n\n      <!-- Clear searches button -->\n      <div class=\"searches-actions\" *ngIf=\"(latestSearches$ | async)?.length! > 0\">\n        <button type=\"button\" class=\"clear-btn\" (click)=\"clearLatestSearches()\">\n          <i class=\"fas fa-trash\"></i> Clear All\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICwDrDC,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,cACF;;;;;IAeAP,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAG,MAAA,CAAAD,eAAA,YACF;;;;;IAcEP,EAAA,CAAAC,cAAA,eAA+E;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGrGH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,eAAA,uBACF;;;;;IAYEP,EAAA,CAAAC,cAAA,eAA4E;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGlGH,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAK,OAAA,CAAAH,eAAA,oBACF;;;;;IAbFP,EAAA,CAAAC,cAAA,cAA0D;IAEtDD,EAAA,CAAAW,SAAA,gBAK0D;IAC1DX,EAAA,CAAAY,UAAA,IAAAC,sCAAA,mBAAgG;IAChGb,EAAA,CAAAW,SAAA,YAA6C;IAC/CX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,UAAA,IAAAE,qCAAA,kBAEM;IACRd,EAAA,CAAAG,YAAA,EAAM;;;;;;IAPAH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,iCAAAH,OAAA,CAAAI,KAAA,CAAuD;IACzBpB,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAqB,UAAA,YAAAC,OAAA,GAAAL,MAAA,CAAAC,UAAA,CAAAC,GAAA,iCAAAG,OAAA,CAAAF,KAAA,EAA0C;IAGhDpB,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAqB,UAAA,SAAAJ,MAAA,CAAAV,eAAA,eAAmC;;;;;IAwF7DP,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFqCH,EAAA,CAAAqB,UAAA,UAAAE,UAAA,CAAAH,KAAA,CAAsB;IAClEpB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkB,UAAA,CAAAC,KAAA,MACF;;;;;IAkBMxB,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFmCH,EAAA,CAAAqB,UAAA,UAAAI,UAAA,CAAAL,KAAA,CAAsB;IAChEpB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAoB,UAAA,CAAAD,KAAA,MACF;;;;;IAJJxB,EAAA,CAAAC,cAAA,eAAiD;IAE7CD,EAAA,CAAAY,UAAA,IAAAc,0CAAA,qBAES;IACX1B,EAAA,CAAAG,YAAA,EAAS;;;;IAHoBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAqB,UAAA,YAAAM,MAAA,CAAAC,YAAA,CAAe;;;;;IAiBpD5B,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1CH,EAAA,CAAAC,cAAA,WAAwB;IACtBD,EAAA,CAAAW,SAAA,YAAsC;IAACX,EAAA,CAAAE,MAAA,qBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAcXH,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAA6B,UAAA,mBAAAC,sDAAA;MAAA,MAAAC,WAAA,GAAA/B,EAAA,CAAAgC,aAAA,CAAAC,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAAqC,aAAA;MAAA,OAASrC,EAAA,CAAAsC,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAAL,UAAA,CAAwB;IAAA,EAAC;IAClClC,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAW,SAAA,WAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACpF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADgBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAwC,iBAAA,CAAAN,UAAA,CAAAO,IAAA,CAAiB;IAAoBzC,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAwC,iBAAA,CAAAN,UAAA,CAAAQ,EAAA,CAAe;IAAU1C,EAAA,CAAAI,SAAA,GACpF;IADoFJ,EAAA,CAAAK,kBAAA,SAAAL,EAAA,CAAA2C,WAAA,QAAAT,UAAA,CAAAU,IAAA,sBACpF;;;;;IAKJ5C,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAW,SAAA,YAA6B;IAC7BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAK/DH,EAAA,CAAAC,cAAA,cAA6E;IACnCD,EAAA,CAAA6B,UAAA,mBAAAgB,yDAAA;MAAA7C,EAAA,CAAAgC,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAA/C,EAAA,CAAAqC,aAAA;MAAA,OAASrC,EAAA,CAAAsC,WAAA,CAAAS,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IACrEhD,EAAA,CAAAW,SAAA,YAA4B;IAACX,EAAA,CAAAE,MAAA,kBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADzQjB,OAAM,MAAO8C,eAAe;EAkC1BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IApChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAQ,IAAI;IAGvB;IACA,KAAAC,QAAQ,GAAG,QAAQ;IACnB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IAEpB;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,aAAa,GAAG,SAAS;IAEzB;IACA,KAAAC,cAAc,GAAG,CACf;MAAE5C,KAAK,EAAE,KAAK;MAAEI,KAAK,EAAE;IAAS,CAAE,EAClC;MAAEJ,KAAK,EAAE,MAAM;MAAEI,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEJ,KAAK,EAAE,MAAM;MAAEI,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEJ,KAAK,EAAE,OAAO;MAAEI,KAAK,EAAE;IAAO,CAAE,CACnC;IAED;IACA,KAAAI,YAAY,GAAG,CACb;MAAER,KAAK,EAAE,GAAG;MAAEI,KAAK,EAAE;IAAY,CAAE,EACnC;MAAEJ,KAAK,EAAE,GAAG;MAAEI,KAAK,EAAE;IAAY,CAAE,EACnC;MAAEJ,KAAK,EAAE,GAAG;MAAEI,KAAK,EAAE;IAAY,CAAE,CACpC;IAQC,IAAI,CAACN,UAAU,GAAG,IAAI,CAAC+C,UAAU,EAAE;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACd,aAAa,CAACc,eAAe;EAC3D;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACd,WAAW,CAACe,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACd,WAAW,GAAGc,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACjB,WAAW,CAACkB,eAAe,EAAE,EAAE;MACvC,IAAI,CAACjB,MAAM,CAACkB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACjC;;IAGF;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQR,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACd,EAAE,CAACuB,KAAK,CAAC;MACnBjB,QAAQ,EAAE,CAAC,QAAQ,EAAE1D,UAAU,CAAC4E,QAAQ,CAAC;MACzClC,IAAI,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAAC4E,QAAQ,CAAC;MAC/BjC,EAAE,EAAE,CAAC,EAAE,EAAE3C,UAAU,CAAC4E,QAAQ,CAAC;MAC7BC,aAAa,EAAE,CAAC,EAAE,EAAE7E,UAAU,CAAC4E,QAAQ,CAAC;MACxCE,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC/E,UAAU,CAAC4E,QAAQ,EAAE5E,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAACjF,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAClF,UAAU,CAACgF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCG,KAAK,EAAE,CAAC,SAAS,EAAEnF,UAAU,CAAC4E,QAAQ,CAAC;MACvCQ,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,eAAe,EAAE,CAAC,KAAK,CAAC;MACxBC,OAAO,EAAE,CAAC,KAAK,CAAC;MAChBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjB3D,YAAY,EAAE,CAAC,GAAG;KACnB,CAAC;EACJ;EAEA;;;EAGQ6C,eAAeA,CAAA;IACrB,MAAMe,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAChCE,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAAC;IAChCK,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,IAAI,CAAC1E,UAAU,CAAC4E,UAAU,CAAC;MACzBlB,aAAa,EAAE,IAAI,CAACmB,UAAU,CAACL,QAAQ,CAAC;MACxCb,UAAU,EAAE,IAAI,CAACkB,UAAU,CAACF,QAAQ;KACrC,CAAC;EACJ;EAEA;;;EAGQE,UAAUA,CAACnD,IAAU;IAC3B,OAAOA,IAAI,CAACoD,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEA;;;EAGAC,gBAAgBA,CAACC,IAAY;IAC3B,IAAI,CAAC1C,QAAQ,GAAG0C,IAAI;IACpB,IAAI,CAACzC,cAAc,GAAGyC,IAAI,KAAK,WAAW;IAE1C,IAAI,CAACjF,UAAU,CAAC4E,UAAU,CAAC;MAAErC,QAAQ,EAAE0C;IAAI,CAAE,CAAC;IAE9C,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAACjF,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEiF,aAAa,CAAC,CAACrG,UAAU,CAAC4E,QAAQ,CAAC,CAAC;KACxE,MAAM;MACL,IAAI,CAACzD,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEkF,eAAe,EAAE;;IAEtD,IAAI,CAACnF,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEmF,sBAAsB,EAAE;EAC7D;EAEA;;;EAGAC,oBAAoBA,CAACJ,IAAuC,EAAEK,SAAkB;IAC9E,MAAMC,YAAY,GAAG,IAAI,CAACvF,UAAU,CAACC,GAAG,CAACgF,IAAI,CAAC,EAAE/E,KAAK,IAAI,CAAC;IAC1D,IAAIsF,QAAQ,GAAGF,SAAS,GAAGC,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,YAAY,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIN,IAAI,KAAK,QAAQ,IAAIO,QAAQ,GAAG,CAAC,EAAE;MACrCA,QAAQ,GAAG,CAAC;;IAGd,IAAI,CAACxF,UAAU,CAAC4E,UAAU,CAAC;MAAE,CAACK,IAAI,GAAGO;IAAQ,CAAE,CAAC;IAEhD;IACA,IAAIP,IAAI,KAAK,QAAQ,EAAE,IAAI,CAACvC,UAAU,GAAG8C,QAAQ;IACjD,IAAIP,IAAI,KAAK,UAAU,EAAE,IAAI,CAACtC,UAAU,GAAG6C,QAAQ;IACnD,IAAIP,IAAI,KAAK,SAAS,EAAE,IAAI,CAACrC,WAAW,GAAG4C,QAAQ;EACrD;EAEA;;;EAGAG,kBAAkBA,CAAA;IAChB,MAAM/B,MAAM,GAAG,IAAI,CAAC5D,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,IAAI,CAAC;IACxD,MAAM4D,QAAQ,GAAG,IAAI,CAAC9D,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC5D,MAAM6D,OAAO,GAAG,IAAI,CAAC/D,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC1D,OAAO0D,MAAM,GAAGE,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGA6B,aAAaA,CAACC,WAAmB;IAC/B,IAAI,CAAChD,aAAa,GAAGgD,WAAW;IAChC,IAAI,CAAC7F,UAAU,CAAC4E,UAAU,CAAC;MAAEZ,KAAK,EAAE6B;IAAW,CAAE,CAAC;EACpD;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACrD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACzC,UAAU,CAAC4E,UAAU,CAAC;MAAEP,QAAQ,EAAE,IAAI,CAAC5B;IAAY,CAAE,CAAC;EAC7D;EAEA;;;EAGAsD,aAAaA,CAAA;IACX,MAAMxE,IAAI,GAAG,IAAI,CAACvB,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK;IAC/C,MAAMsB,EAAE,GAAG,IAAI,CAACxB,UAAU,CAACC,GAAG,CAAC,IAAI,CAAC,EAAEC,KAAK;IAE3C,IAAI,CAACF,UAAU,CAAC4E,UAAU,CAAC;MACzBrD,IAAI,EAAEC,EAAE;MACRA,EAAE,EAAED;KACL,CAAC;EACJ;EAEA;;;EAGAyE,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChG,UAAU,CAACiG,KAAK,EAAE;MACzB,IAAI,CAAC5D,SAAS,GAAG,IAAI;MAErB,MAAM6D,SAAS,GAAG,IAAI,CAAClG,UAAU,CAACE,KAAK;MACvC,MAAMiG,UAAU,GAAqB;QACnC5D,QAAQ,EAAE2D,SAAS,CAAC3D,QAAQ;QAC5BhB,IAAI,EAAE2E,SAAS,CAAC3E,IAAI;QACpBC,EAAE,EAAE0E,SAAS,CAAC1E,EAAE;QAChBkC,aAAa,EAAEwC,SAAS,CAACxC,aAAa;QACtCC,UAAU,EAAEuC,SAAS,CAACvC,UAAU;QAChCyC,UAAU,EAAE;UACVxC,MAAM,EAAEsC,SAAS,CAACtC,MAAM;UACxBE,QAAQ,EAAEoC,SAAS,CAACpC,QAAQ;UAC5BC,OAAO,EAAEmC,SAAS,CAACnC;SACpB;QACDC,KAAK,EAAEkC,SAAS,CAAClC,KAAK;QACtBC,gBAAgB,EAAEiC,SAAS,CAACjC,gBAAgB;QAC5CC,aAAa,EAAEgC,SAAS,CAAChC,aAAa;QACtCC,eAAe,EAAE+B,SAAS,CAAC/B,eAAe;QAC1CC,OAAO,EAAE8B,SAAS,CAAC9B,OAAO;QAC1BC,QAAQ,EAAE6B,SAAS,CAAC7B;OACrB;MAED;MACA,IAAI,CAACnC,aAAa,CAACmE,gBAAgB,CAACF,UAAU,CAAC;MAE/C;MACA,IAAIG,gBAAgB;MAEpB,QAAQH,UAAU,CAAC5D,QAAQ;QACzB,KAAK,QAAQ;UACX+D,gBAAgB,GAAG,IAAI,CAACpE,aAAa,CAACqE,mBAAmB,CAACJ,UAAU,CAAC;UACrE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACpE,aAAa,CAACsE,sBAAsB,CAACL,UAAU,CAAC;UACxE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACpE,aAAa,CAACuE,sBAAsB,CAACN,UAAU,CAAC;UACxE;QACF;UACEG,gBAAgB,GAAG,IAAI,CAACpE,aAAa,CAACqE,mBAAmB,CAACJ,UAAU,CAAC;;MAGzEG,gBAAgB,CAACnD,SAAS,CAAC;QACzBuD,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACtE,SAAS,GAAG,KAAK;UACtB,IAAIsE,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3BC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,QAAQ,CAAC;YAC/C;YACA;WACD,MAAM;YACLG,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEL,QAAQ,CAACC,MAAM,CAACK,QAAQ,CAAC;YACzD;;QAEJ,CAAC;;QACDD,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC3E,SAAS,GAAG,KAAK;UACtByE,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC;QACF;OACD,CAAC;KACH,MAAM;MACL;MACAE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnH,UAAU,CAACoH,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QAClD,IAAI,CAACtH,UAAU,CAACC,GAAG,CAACqH,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC3C,CAAC,CAAC;;EAEN;EAEA;;;EAGAlG,gBAAgBA,CAACmG,MAAoB;IACnC,IAAI,CAACxH,UAAU,CAAC4E,UAAU,CAAC;MACzBrD,IAAI,EAAEiG,MAAM,CAACjG,IAAI;MACjBC,EAAE,EAAEgG,MAAM,CAAChG,EAAE;MACbkC,aAAa,EAAE8D,MAAM,CAAC9F,IAAI;MAC1BkC,MAAM,EAAE4D,MAAM,CAACpB,UAAU;MACzBtC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;;;EAGAjC,mBAAmBA,CAAA;IACjB,IAAI,CAACI,aAAa,CAACJ,mBAAmB,EAAE;EAC1C;EAEA;;;EAGAzC,eAAeA,CAACoI,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAAC1H,UAAU,CAACC,GAAG,CAACwH,WAAW,CAAC;IAChD,IAAIC,OAAO,EAAEC,MAAM,IAAID,OAAO,CAACE,OAAO,EAAE;MACtC,IAAIF,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGF,WAAW,cAAc;;MAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oBAAoBD,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC9D,GAAG,EAAE;;;IAG1D,OAAO,EAAE;EACX;;;uBAlSW9B,eAAe,EAAAjD,EAAA,CAAA+I,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjJ,EAAA,CAAA+I,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAnJ,EAAA,CAAA+I,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArJ,EAAA,CAAA+I,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAftG,eAAe;MAAAuG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb5B9J,EAAA,CAAAC,cAAA,aAA8B;UAOpBD,EAAA,CAAAW,SAAA,WAA4B;UAC5BX,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElCH,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAE,MAAA,gDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG1EH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAA6B,UAAA,sBAAAmI,mDAAA;YAAA,OAAYD,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAEpDlH,EAAA,CAAAC,cAAA,cAAgC;UAK5BD,EAAA,CAAA6B,UAAA,mBAAAoI,kDAAA;YAAA,OAASF,GAAA,CAAA7D,gBAAA,CAAiB,QAAQ,CAAC;UAAA,EAAC;UACpClG,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAA6B,UAAA,mBAAAqI,kDAAA;YAAA,OAASH,GAAA,CAAA7D,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvClG,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAA6B,UAAA,mBAAAsI,kDAAA;YAAA,OAASJ,GAAA,CAAA7D,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvClG,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAmC;UAIXD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,eAAoC;UAClCD,EAAA,CAAAW,SAAA,aAAsC;UAOtCX,EAAA,CAAAC,cAAA,kBAAiE;UAA1BD,EAAA,CAAA6B,UAAA,mBAAAuI,kDAAA;YAAA,OAASL,GAAA,CAAA9C,aAAA,EAAe;UAAA,EAAC;UAC9DjH,EAAA,CAAAW,SAAA,aAAmC;UACrCX,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAY,UAAA,KAAAyJ,+BAAA,kBAEM;UACRrK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAuC;UACrBD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1BH,EAAA,CAAAC,cAAA,eAAoC;UAClCD,EAAA,CAAAW,SAAA,aAAoC;UAOtCX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAA0J,+BAAA,kBAEM;UACRtK,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAsB;UAIhBD,EAAA,CAAAW,SAAA,iBAK6D;UAC7DX,EAAA,CAAAY,UAAA,KAAA2J,gCAAA,mBAAmG;UACnGvK,EAAA,CAAAW,SAAA,aAA6C;UAC/CX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAA4J,+BAAA,kBAEM;UACRxK,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAY,UAAA,KAAA6J,+BAAA,kBAcM;UACRzK,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAqC;UAG1BD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAAgC;UAGCD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/EH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAA6B,UAAA,mBAAA6I,kDAAA;YAAA,OAASX,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,KAAK,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpGH,EAAA,CAAAC,cAAA,kBAAyF;UAA/CD,EAAA,CAAA6B,UAAA,mBAAA8I,kDAAA;YAAA,OAASZ,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,IAAI,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKvGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA4B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAA6B,UAAA,mBAAA+I,kDAAA;YAAA,OAASb,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,KAAK,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtGH,EAAA,CAAAC,cAAA,kBAA2F;UAAjDD,EAAA,CAAA6B,UAAA,mBAAAgJ,kDAAA;YAAA,OAASd,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,IAAI,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKzGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAA6B,UAAA,mBAAAiJ,kDAAA;YAAA,OAASf,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,KAAK,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrGH,EAAA,CAAAC,cAAA,kBAA0F;UAAhDD,EAAA,CAAA6B,UAAA,mBAAAkJ,kDAAA;YAAA,OAAShB,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,IAAI,CAAC;UAAA,EAAC;UAACvG,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKxGH,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAA6B,UAAA,mBAAAmJ,kDAAA;YAAA,OAASjB,GAAA,CAAAjD,aAAA,CAAc,SAAS,CAAC;UAAA,EAAC;UAClC9G,EAAA,CAAAW,SAAA,aAA4B;UAACX,EAAA,CAAAE,MAAA,iBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAsC;UACND,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAwF;UACrED,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM/CH,EAAA,CAAAC,cAAA,eAAgC;UAGAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,gBAA6B;UAEHD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,mBAAqB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMnDH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,gBAA6B;UAEzBD,EAAA,CAAAY,UAAA,MAAAqK,mCAAA,qBAES;UACXjL,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,gBAA6B;UAMvBD,EAAA,CAAA6B,UAAA,oBAAAqJ,mDAAA;YAAA,OAAUnB,GAAA,CAAA/C,cAAA,EAAgB;UAAA,EAAC;UAJ7BhH,EAAA,CAAAG,YAAA,EAI8B;UAC9BH,EAAA,CAAAC,cAAA,kBAA6C;UAC3CD,EAAA,CAAAY,UAAA,MAAAuK,iCAAA,mBAMO;UACTnL,EAAA,CAAAG,YAAA,EAAQ;UAOhBH,EAAA,CAAAC,cAAA,gBAAmC;UAK/BD,EAAA,CAAAY,UAAA,MAAAwK,iCAAA,mBAA0C;UAC1CpL,EAAA,CAAAY,UAAA,MAAAyK,iCAAA,mBAEO;UACTrL,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,gBAAmC;UAE3BD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGlDH,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAY,UAAA,MAAA0K,gCAAA,mBAYM;;UAGNtL,EAAA,CAAAY,UAAA,MAAA2K,gCAAA,kBAIM;;UACRvL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAY,UAAA,MAAA4K,gCAAA,kBAIM;;UACRxL,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;UA3QEH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAqB,UAAA,cAAA0I,GAAA,CAAA7I,UAAA,CAAwB;UAMxBlB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAe,WAAA,WAAAgJ,GAAA,CAAAtG,QAAA,cAAsC;UAOtCzD,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAe,WAAA,WAAAgJ,GAAA,CAAAtG,QAAA,iBAAyC;UAOzCzD,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAe,WAAA,WAAAgJ,GAAA,CAAAtG,QAAA,iBAAyC;UAwBXzD,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAAxJ,eAAA,SAA6B;UAiB7BP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAAxJ,eAAA,OAA2B;UAenDP,EAAA,CAAAI,SAAA,GAA0D;UAA1DJ,EAAA,CAAAe,WAAA,eAAA0K,OAAA,GAAA1B,GAAA,CAAA7I,UAAA,CAAAC,GAAA,oCAAAsK,OAAA,CAAArK,KAAA,CAA0D;UAC5BpB,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAqB,UAAA,YAAAqK,OAAA,GAAA3B,GAAA,CAAA7I,UAAA,CAAAC,GAAA,oCAAAuK,OAAA,CAAAtK,KAAA,EAA6C;UAGnDpB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAAxJ,eAAA,kBAAsC;UAMhCP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAArG,cAAA,CAAoB;UA2BtB1D,EAAA,CAAAI,SAAA,IAA0C;UAA1CJ,EAAA,CAAAwC,iBAAA,GAAAmJ,QAAA,GAAA5B,GAAA,CAAA7I,UAAA,CAAAC,GAAA,6BAAAwK,QAAA,CAAAvK,KAAA,OAA0C;UAU1CpB,EAAA,CAAAI,SAAA,IAA4C;UAA5CJ,EAAA,CAAAwC,iBAAA,GAAAoJ,QAAA,GAAA7B,GAAA,CAAA7I,UAAA,CAAAC,GAAA,+BAAAyK,QAAA,CAAAxK,KAAA,OAA4C;UAU5CpB,EAAA,CAAAI,SAAA,IAA2C;UAA3CJ,EAAA,CAAAwC,iBAAA,GAAAqJ,QAAA,GAAA9B,GAAA,CAAA7I,UAAA,CAAAC,GAAA,8BAAA0K,QAAA,CAAAzK,KAAA,OAA2C;UAYvEpB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAe,WAAA,WAAAgJ,GAAA,CAAAhG,aAAA,eAA4C;UAwCnB/D,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAqB,UAAA,YAAA0I,GAAA,CAAA/F,cAAA,CAAiB;UAkBbhE,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAApG,YAAA,CAAkB;UAkBrD3D,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAqB,UAAA,aAAA0I,GAAA,CAAAxG,SAAA,KAAAwG,GAAA,CAAA7I,UAAA,CAAAiG,KAAA,CAA2C;UACpCnH,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAqB,UAAA,UAAA0I,GAAA,CAAAxG,SAAA,CAAgB;UAChBvD,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAqB,UAAA,SAAA0I,GAAA,CAAAxG,SAAA,CAAe;UAiBLvD,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAA8L,WAAA,UAAA/B,GAAA,CAAA7F,eAAA,EAA0B;UAczClE,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAqB,UAAA,WAAA0K,QAAA,GAAA/L,EAAA,CAAA8L,WAAA,UAAA/B,GAAA,CAAA7F,eAAA,oBAAA6H,QAAA,CAAAC,MAAA,QAA6C;UAQtBhM,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAqB,UAAA,WAAA4K,QAAA,GAAAjM,EAAA,CAAA8L,WAAA,UAAA/B,GAAA,CAAA7F,eAAA,oBAAA+H,QAAA,CAAAD,MAAA,MAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}