{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction FlightComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"from\"), \" \");\n  }\n}\nfunction FlightComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"to\"), \" \");\n  }\n}\nfunction FlightComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"departureDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_37_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getErrorMessage(\"returnDate\"), \" \");\n  }\n}\nfunction FlightComponent_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14)(3, \"div\", 15);\n    i0.ɵɵelement(4, \"i\", 16)(5, \"input\", 74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"div\", 15);\n    i0.ɵɵelement(8, \"i\", 19)(9, \"input\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"div\", 22);\n    i0.ɵɵelement(12, \"input\", 76)(13, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FlightComponent_div_37_div_14_Template, 2, 1, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", (tmp_0_0 = ctx_r3.flightForm.get(\"to\")) == null ? null : tmp_0_0.value);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", (tmp_1_0 = ctx_r3.flightForm.get(\"from\")) == null ? null : tmp_1_0.value);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getErrorMessage(\"returnDate\"));\n  }\n}\nfunction FlightComponent_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13)(2, \"div\", 14)(3, \"div\", 15);\n    i0.ɵɵelement(4, \"i\", 16);\n    i0.ɵɵelementStart(5, \"input\", 77);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_38_Template_input_ngModelChange_5_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.from = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 14)(7, \"div\", 15);\n    i0.ɵɵelement(8, \"i\", 19);\n    i0.ɵɵelementStart(9, \"input\", 78);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_38_Template_input_ngModelChange_9_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.to = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 21)(11, \"div\", 22)(12, \"input\", 79);\n    i0.ɵɵlistener(\"ngModelChange\", function FlightComponent_div_38_Template_input_ngModelChange_12_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const segment_r14 = restoredCtx.$implicit;\n      return i0.ɵɵresetView(segment_r14.date = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"i\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_38_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const i_r15 = restoredCtx.index;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.removeSegment(i_r15));\n    });\n    i0.ɵɵelement(15, \"i\", 81);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const segment_r14 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.from);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.to);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", segment_r14.date);\n  }\n}\nfunction FlightComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_39_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.addSegment());\n    });\n    i0.ɵɵelement(2, \"i\", 84);\n    i0.ɵɵtext(3, \" Add Sector \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightComponent_option_110_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r23.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r23.label, \" \");\n  }\n}\nfunction FlightComponent_span_118_option_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 85);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r25.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r25.label, \" \");\n  }\n}\nfunction FlightComponent_span_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 86)(1, \"select\", 87);\n    i0.ɵɵtemplate(2, FlightComponent_span_118_option_2_Template, 2, 2, \"option\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.calendarDays);\n  }\n}\nfunction FlightComponent_span_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"SEARCH NOW\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_span_122_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 88);\n    i0.ɵɵtext(2, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightComponent_div_130_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_130_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r28);\n      const search_r26 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.loadLatestSearch(search_r26));\n    });\n    i0.ɵɵelementStart(1, \"div\", 90);\n    i0.ɵɵelement(2, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 91)(4, \"div\", 92);\n    i0.ɵɵtext(5, \" Coming from \");\n    i0.ɵɵelementStart(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" - \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const search_r26 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(search_r26.from);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(search_r26.to);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" on \", i0.ɵɵpipeBind2(12, 3, search_r26.date, \"MMM d, yyyy\"), \" \");\n  }\n}\nfunction FlightComponent_div_132_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93);\n    i0.ɵɵelement(1, \"i\", 94);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"No recent searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"small\");\n    i0.ɵɵtext(5, \"Your recent flight searches will appear here\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightComponent_div_134_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function FlightComponent_div_134_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.clearLatestSearches());\n    });\n    i0.ɵɵelement(2, \"i\", 97);\n    i0.ɵɵtext(3, \" Clear All \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class FlightComponent {\n  constructor(fb, flightService, authService, router) {\n    this.fb = fb;\n    this.flightService = flightService;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.currentUser = null;\n    // Form state\n    this.tripType = 'oneWay';\n    this.showReturnDate = false;\n    this.showCalendar = false;\n    this.additionalSegments = [];\n    // Passenger counts\n    this.adultCount = 1;\n    this.childCount = 0;\n    this.infantCount = 0;\n    // Class selection\n    this.selectedClass = 'economy';\n    // Baggage options\n    this.baggageOptions = [{\n      value: 'all',\n      label: '--All--'\n    }, {\n      value: '20kg',\n      label: '20kg'\n    }, {\n      value: '30kg',\n      label: '30kg'\n    }, {\n      value: 'extra',\n      label: 'Extra'\n    }];\n    // Calendar options\n    this.calendarDays = [{\n      value: '1',\n      label: '+/- 1 Days'\n    }, {\n      value: '3',\n      label: '+/- 3 Days'\n    }, {\n      value: '7',\n      label: '+/- 7 Days'\n    }];\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n  ngOnInit() {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n    // Set default dates\n    this.setDefaultDates();\n  }\n  /**\n   * Create reactive form\n   */\n  createForm() {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  setDefaultDates() {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n  /**\n   * Format date for input field\n   */\n  formatDate(date) {\n    return date.toISOString().split('T')[0];\n  }\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type) {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n    this.flightForm.patchValue({\n      tripType: type\n    });\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n  }\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type, increment) {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n    this.flightForm.patchValue({\n      [type]: newValue\n    });\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers() {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass) {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({\n      class: flightClass\n    });\n  }\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar() {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({\n      calendar: this.showCalendar\n    });\n  }\n  /**\n   * Swap from and to locations\n   */\n  swapLocations() {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n      const formValue = this.flightForm.value;\n      const searchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n      // Perform search based on trip type\n      let searchObservable;\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n      searchObservable.subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n\n        error: error => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search) {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches() {\n    this.flightService.clearLatestSearches();\n  }\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName) {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function FlightComponent_Factory(t) {\n      return new (t || FlightComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightComponent,\n      selectors: [[\"app-flight\"]],\n      decls: 136,\n      vars: 32,\n      consts: [[1, \"flight-container\"], [1, \"flight-content\"], [1, \"flight-search-panel\"], [1, \"search-header\"], [1, \"search-title\"], [1, \"fas\", \"fa-plane\"], [1, \"search-subtitle\"], [1, \"flight-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-selector\"], [\"type\", \"button\", 1, \"trip-type-btn\", 3, \"click\"], [1, \"location-date-section\"], [1, \"flight-segments\"], [1, \"flight-segment\"], [1, \"segment-row\"], [1, \"form-group\", \"location-group\"], [1, \"location-input-wrapper\"], [1, \"fas\", \"fa-plane-departure\"], [\"type\", \"text\", \"formControlName\", \"from\", \"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", 1, \"location-input\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"fas\", \"fa-plane-arrival\"], [\"type\", \"text\", \"formControlName\", \"to\", \"placeholder\", \"Going to (City, Country Or Specific Airport)\", 1, \"location-input\"], [1, \"form-group\", \"date-group\"], [1, \"date-input-wrapper\"], [\"type\", \"date\", \"formControlName\", \"departureDate\", \"placeholder\", \"Choose A Date\", 1, \"date-input\"], [1, \"fas\", \"fa-calendar-alt\", \"date-icon\"], [\"class\", \"flight-segment\", 4, \"ngIf\"], [\"class\", \"flight-segment\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"add-sector-section\", 4, \"ngIf\"], [1, \"passenger-class-section\"], [1, \"form-group\", \"passenger-group\"], [1, \"passenger-controls\"], [1, \"passenger-type\"], [1, \"passenger-icon\"], [1, \"fas\", \"fa-user\"], [1, \"passenger-count\"], [1, \"counter-controls\"], [\"type\", \"button\", 1, \"counter-btn\", 3, \"click\"], [1, \"fas\", \"fa-child\"], [1, \"fas\", \"fa-baby\"], [1, \"class-selection\"], [\"type\", \"button\", 1, \"class-btn\", 3, \"click\"], [1, \"fas\", \"fa-chair\"], [1, \"form-group\", \"airline-group\"], [\"for\", \"preferredAirline\"], [\"id\", \"preferredAirline\", \"formControlName\", \"preferredAirline\", 1, \"airline-select\"], [\"value\", \"\"], [\"value\", \"TK\"], [\"value\", \"AF\"], [\"value\", \"LH\"], [\"value\", \"EK\"], [\"value\", \"QR\"], [1, \"additional-options\"], [1, \"option-group\"], [1, \"option-label\"], [1, \"option-controls\"], [\"formControlName\", \"refundableFares\", 1, \"option-select\"], [\"value\", \"false\"], [\"value\", \"true\"], [\"formControlName\", \"baggage\", 1, \"option-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"calendar-toggle\"], [\"type\", \"checkbox\", \"id\", \"calendar\", \"formControlName\", \"calendar\", 3, \"change\"], [\"for\", \"calendar\", 1, \"calendar-label\"], [\"class\", \"calendar-days\", 4, \"ngIf\"], [1, \"search-button-section\"], [\"type\", \"submit\", 1, \"search-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"latest-searches-panel\"], [1, \"searches-header\"], [1, \"searches-list\"], [\"class\", \"search-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"empty-searches\", 4, \"ngIf\"], [\"class\", \"searches-actions\", 4, \"ngIf\"], [1, \"error-message\"], [\"type\", \"text\", \"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", \"readonly\", \"\", 1, \"location-input\", 3, \"value\"], [\"type\", \"text\", \"placeholder\", \"Going to (City, Country Or Specific Airport)\", \"readonly\", \"\", 1, \"location-input\", 3, \"value\"], [\"type\", \"date\", \"formControlName\", \"returnDate\", \"placeholder\", \"Choose A Date\", 1, \"date-input\"], [\"type\", \"text\", \"placeholder\", \"Leaving from (City, Country Or Specific Airport)\", 1, \"location-input\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"text\", \"placeholder\", \"Going to (City, Country Or Specific Airport)\", 1, \"location-input\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"date\", \"placeholder\", \"Choose A Date\", 1, \"date-input\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"button\", 1, \"remove-segment-btn\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"add-sector-section\"], [\"type\", \"button\", 1, \"add-sector-btn\", 3, \"click\"], [1, \"fas\", \"fa-plus\"], [3, \"value\"], [1, \"calendar-days\"], [\"formControlName\", \"calendarDays\", 1, \"calendar-select\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\"], [1, \"search-item\", 3, \"click\"], [1, \"search-icon\"], [1, \"search-details\"], [1, \"search-route\"], [1, \"empty-searches\"], [1, \"fas\", \"fa-search\"], [1, \"searches-actions\"], [\"type\", \"button\", 1, \"clear-btn\", 3, \"click\"], [1, \"fas\", \"fa-trash\"]],\n      template: function FlightComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵelementStart(6, \"h2\");\n          i0.ɵɵtext(7, \"Search and Book Flights\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"form\", 7);\n          i0.ɵɵlistener(\"ngSubmit\", function FlightComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_12_listener() {\n            return ctx.onTripTypeChange(\"oneWay\");\n          });\n          i0.ɵɵtext(13, \" One way \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_14_listener() {\n            return ctx.onTripTypeChange(\"roundTrip\");\n          });\n          i0.ɵɵtext(15, \" Round Trip \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_16_listener() {\n            return ctx.onTripTypeChange(\"multiCity\");\n          });\n          i0.ɵɵtext(17, \" Multi-City/Stop-Overs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 10)(19, \"div\", 11)(20, \"div\", 12)(21, \"div\", 13)(22, \"div\", 14)(23, \"div\", 15);\n          i0.ɵɵelement(24, \"i\", 16)(25, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(26, FlightComponent_div_26_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"div\", 15);\n          i0.ɵɵelement(29, \"i\", 19)(30, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, FlightComponent_div_31_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22);\n          i0.ɵɵelement(34, \"input\", 23)(35, \"i\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(36, FlightComponent_div_36_Template, 2, 1, \"div\", 18);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(37, FlightComponent_div_37_Template, 15, 3, \"div\", 25);\n          i0.ɵɵtemplate(38, FlightComponent_div_38_Template, 16, 3, \"div\", 26);\n          i0.ɵɵtemplate(39, FlightComponent_div_39_Template, 4, 0, \"div\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 28)(41, \"div\", 29)(42, \"label\");\n          i0.ɵɵtext(43, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 30)(45, \"div\", 31)(46, \"span\", 32);\n          i0.ɵɵelement(47, \"i\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"span\", 34);\n          i0.ɵɵtext(49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 35)(51, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_51_listener() {\n            return ctx.updatePassengerCount(\"adults\", false);\n          });\n          i0.ɵɵtext(52, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_53_listener() {\n            return ctx.updatePassengerCount(\"adults\", true);\n          });\n          i0.ɵɵtext(54, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 31)(56, \"span\", 32);\n          i0.ɵɵelement(57, \"i\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"span\", 34);\n          i0.ɵɵtext(59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 35)(61, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_61_listener() {\n            return ctx.updatePassengerCount(\"children\", false);\n          });\n          i0.ɵɵtext(62, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_63_listener() {\n            return ctx.updatePassengerCount(\"children\", true);\n          });\n          i0.ɵɵtext(64, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 31)(66, \"span\", 32);\n          i0.ɵɵelement(67, \"i\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"span\", 34);\n          i0.ɵɵtext(69);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 35)(71, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_71_listener() {\n            return ctx.updatePassengerCount(\"infants\", false);\n          });\n          i0.ɵɵtext(72, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_73_listener() {\n            return ctx.updatePassengerCount(\"infants\", true);\n          });\n          i0.ɵɵtext(74, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function FlightComponent_Template_button_click_76_listener() {\n            return ctx.onClassChange(\"economy\");\n          });\n          i0.ɵɵelement(77, \"i\", 41);\n          i0.ɵɵtext(78, \" Economy \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(79, \"div\", 42)(80, \"label\", 43);\n          i0.ɵɵtext(81, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"select\", 44)(83, \"option\", 45);\n          i0.ɵɵtext(84, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"option\", 46);\n          i0.ɵɵtext(86, \"Turkish Airlines\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"option\", 47);\n          i0.ɵɵtext(88, \"Air France\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"option\", 48);\n          i0.ɵɵtext(90, \"Lufthansa\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"option\", 49);\n          i0.ɵɵtext(92, \"Emirates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"option\", 50);\n          i0.ɵɵtext(94, \"Qatar Airways\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(95, \"div\", 51)(96, \"div\", 52)(97, \"label\", 53);\n          i0.ɵɵtext(98, \"Refundable fares\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 54)(100, \"select\", 55)(101, \"option\", 56);\n          i0.ɵɵtext(102, \"--All--\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"option\", 57);\n          i0.ɵɵtext(104, \"Refundable Only\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(105, \"div\", 52)(106, \"label\", 53);\n          i0.ɵɵtext(107, \"Baggage\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 54)(109, \"select\", 58);\n          i0.ɵɵtemplate(110, FlightComponent_option_110_Template, 2, 2, \"option\", 59);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(111, \"div\", 52)(112, \"label\", 53);\n          i0.ɵɵtext(113, \"Calendar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"div\", 54)(115, \"div\", 60)(116, \"input\", 61);\n          i0.ɵɵlistener(\"change\", function FlightComponent_Template_input_change_116_listener() {\n            return ctx.toggleCalendar();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"label\", 62);\n          i0.ɵɵtemplate(118, FlightComponent_span_118_Template, 3, 1, \"span\", 63);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(119, \"div\", 64)(120, \"button\", 65);\n          i0.ɵɵtemplate(121, FlightComponent_span_121_Template, 2, 0, \"span\", 66);\n          i0.ɵɵtemplate(122, FlightComponent_span_122_Template, 3, 0, \"span\", 66);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(123, \"div\", 67)(124, \"div\", 68)(125, \"h3\");\n          i0.ɵɵtext(126, \"Latest Searches\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"p\");\n          i0.ɵɵtext(128, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(129, \"div\", 69);\n          i0.ɵɵtemplate(130, FlightComponent_div_130_Template, 13, 6, \"div\", 70);\n          i0.ɵɵpipe(131, \"async\");\n          i0.ɵɵtemplate(132, FlightComponent_div_132_Template, 6, 0, \"div\", 71);\n          i0.ɵɵpipe(133, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(134, FlightComponent_div_134_Template, 4, 0, \"div\", 72);\n          i0.ɵɵpipe(135, \"async\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_20_0;\n          let tmp_21_0;\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.flightForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"oneWay\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"roundTrip\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.tripType === \"multiCity\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"from\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"to\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.getErrorMessage(\"departureDate\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showReturnDate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.additionalSegments);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.tripType === \"multiCity\");\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_10_0 = ctx.flightForm.get(\"adults\")) == null ? null : tmp_10_0.value) || 1);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_11_0 = ctx.flightForm.get(\"children\")) == null ? null : tmp_11_0.value) || 0);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate(((tmp_12_0 = ctx.flightForm.get(\"infants\")) == null ? null : tmp_12_0.value) || 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.selectedClass === \"economy\");\n          i0.ɵɵadvance(34);\n          i0.ɵɵproperty(\"ngForOf\", ctx.baggageOptions);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.showCalendar);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || !ctx.flightForm.valid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(131, 26, ctx.latestSearches$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_20_0 = i0.ɵɵpipeBind1(133, 28, ctx.latestSearches$)) == null ? null : tmp_20_0.length) === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = i0.ɵɵpipeBind1(135, 30, ctx.latestSearches$)) == null ? null : tmp_21_0.length) > 0);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i1.NgModel, i5.AsyncPipe, i5.DatePipe],\n      styles: [\"\\n\\n.flight-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 20px;\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n.flight-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 30px;\\n  align-items: start;\\n}\\n\\n\\n\\n.flight-search-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 30px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n\\n.search-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  margin-bottom: 10px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 12px;\\n  border-radius: 50%;\\n  font-size: 20px;\\n}\\n\\n.search-title[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.search-subtitle[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n  margin-left: 55px;\\n}\\n\\n\\n\\n.trip-type-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  padding: 4px;\\n  margin-bottom: 25px;\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 16px;\\n  border: none;\\n  background: transparent;\\n  color: #4a5568;\\n  font-size: 14px;\\n  font-weight: 500;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.trip-type-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  box-shadow: 0 2px 8px rgba(59, 67, 113, 0.3);\\n}\\n\\n.trip-type-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #e2e8f0;\\n}\\n\\n\\n\\n.location-date-section[_ngcontent-%COMP%], .passenger-class-section[_ngcontent-%COMP%], .additional-options[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n\\n\\n.location-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.location-input-wrapper[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  color: #a0aec0;\\n  z-index: 2;\\n}\\n\\n.location-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px 15px 15px 45px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.location-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n.swap-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  padding: 8px;\\n  cursor: pointer;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.swap-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n\\n\\n.date-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.date-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n\\n\\n.passenger-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: #f7fafc;\\n}\\n\\n.passenger-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 16px;\\n  width: 20px;\\n  text-align: center;\\n}\\n\\n.passenger-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.counter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 1px solid #cbd5e0;\\n  background: white;\\n  border-radius: 4px;\\n  cursor: pointer;\\n  font-size: 12px;\\n  font-weight: 600;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n}\\n\\n.counter-btn[_ngcontent-%COMP%]:hover {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-selection[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n\\n.class-btn[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: 2px solid #e2e8f0;\\n  background: white;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  font-size: 14px;\\n  color: #4a5568;\\n  transition: all 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.class-btn.active[_ngcontent-%COMP%] {\\n  background: #3b4371;\\n  color: white;\\n  border-color: #3b4371;\\n}\\n\\n.class-btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  border-color: #cbd5e0;\\n  background: #f7fafc;\\n}\\n\\n\\n\\n.airline-select[_ngcontent-%COMP%], .option-select[_ngcontent-%COMP%], .calendar-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  background: white;\\n  cursor: pointer;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.airline-select[_ngcontent-%COMP%]:focus, .option-select[_ngcontent-%COMP%]:focus, .calendar-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #3b4371;\\n  box-shadow: 0 0 0 3px rgba(59, 67, 113, 0.1);\\n}\\n\\n\\n\\n.additional-options[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n}\\n\\n.option-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.option-label[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-bottom: 8px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n}\\n\\n.calendar-toggle[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  cursor: pointer;\\n}\\n\\n.calendar-days[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.calendar-select[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  font-size: 12px;\\n}\\n\\n\\n\\n.search-button-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 30px;\\n}\\n\\n.search-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  border: none;\\n  padding: 18px 60px;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(72, 187, 120, 0.4);\\n}\\n\\n.search-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n\\n\\n.latest-searches-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 15px;\\n  padding: 25px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  height: -moz-fit-content;\\n  height: fit-content;\\n}\\n\\n.searches-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n\\n.searches-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n\\n\\n.searches-list[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.search-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 15px;\\n  padding: 15px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  margin-bottom: 10px;\\n  border: 1px solid #f7fafc;\\n}\\n\\n.search-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  border-color: #e2e8f0;\\n  transform: translateX(5px);\\n}\\n\\n.search-icon[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3b4371, #5a67d8);\\n  color: white;\\n  padding: 10px;\\n  border-radius: 50%;\\n  font-size: 14px;\\n  flex-shrink: 0;\\n}\\n\\n.search-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.search-route[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n\\n.search-route[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.empty-searches[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 40px 20px;\\n  color: #a0aec0;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 15px;\\n  opacity: 0.5;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0 0 5px 0;\\n  color: #718096;\\n}\\n\\n.empty-searches[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #a0aec0;\\n}\\n\\n\\n\\n.searches-actions[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  text-align: center;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%] {\\n  background: #fed7d7;\\n  color: #c53030;\\n  border: 1px solid #feb2b2;\\n  padding: 8px 16px;\\n  border-radius: 6px;\\n  font-size: 12px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 6px;\\n}\\n\\n.clear-btn[_ngcontent-%COMP%]:hover {\\n  background: #fc8181;\\n  color: white;\\n  border-color: #fc8181;\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n  font-size: 12px;\\n  margin-top: 5px;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .flight-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n  \\n  .flight-search-panel[_ngcontent-%COMP%], .latest-searches-panel[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  \\n  .additional-options[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n  \\n  .passenger-controls[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 15px;\\n  }\\n  \\n  .search-title[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 10px;\\n  }\\n  \\n  .search-subtitle[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .flight-container[_ngcontent-%COMP%] {\\n    padding: 10px;\\n  }\\n  \\n  .trip-type-selector[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  \\n  .trip-type-btn[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  \\n  .search-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 15px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ctx_r1", "ctx_r2", "ctx_r13", "ɵɵelement", "ɵɵtemplate", "FlightComponent_div_37_div_14_Template", "ɵɵproperty", "tmp_0_0", "ctx_r3", "flightForm", "get", "value", "tmp_1_0", "ɵɵlistener", "FlightComponent_div_38_Template_input_ngModelChange_5_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r17", "segment_r14", "$implicit", "ɵɵresetView", "from", "FlightComponent_div_38_Template_input_ngModelChange_9_listener", "to", "FlightComponent_div_38_Template_input_ngModelChange_12_listener", "date", "FlightComponent_div_38_Template_button_click_14_listener", "i_r15", "index", "ctx_r20", "ɵɵnextContext", "removeSegment", "FlightComponent_div_39_Template_button_click_1_listener", "_r22", "ctx_r21", "addSegment", "option_r23", "label", "option_r25", "FlightComponent_span_118_option_2_Template", "ctx_r7", "calendarDays", "FlightComponent_div_130_Template_div_click_0_listener", "_r28", "search_r26", "ctx_r27", "loadLatestSearch", "ɵɵtextInterpolate", "ɵɵpipeBind2", "FlightComponent_div_134_Template_button_click_1_listener", "_r30", "ctx_r29", "clearLatestSearches", "FlightComponent", "constructor", "fb", "flightService", "authService", "router", "isLoading", "currentUser", "tripType", "showReturnDate", "showCalendar", "additionalSegments", "adultCount", "childCount", "infantCount", "selectedClass", "baggageOptions", "createForm", "latestSearches$", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "setDefaultDates", "group", "required", "departureDate", "returnDate", "adults", "min", "children", "infants", "class", "preferredAirline", "directFlights", "refundableFares", "baggage", "calendar", "today", "Date", "tomorrow", "setDate", "getDate", "nextWeek", "patchValue", "formatDate", "toISOString", "split", "onTripTypeChange", "type", "setValidators", "clearValidators", "updateValueAndValidity", "updatePassengerCount", "increment", "currentValue", "newValue", "Math", "max", "getTotalPassengers", "onClassChange", "flightClass", "toggleCalendar", "swapLocations", "onSubmit", "valid", "formValue", "searchForm", "passengers", "saveLatestSearch", "searchObservable", "searchOneWayFlights", "searchRoundTripFlights", "searchMultiCityFlights", "next", "response", "header", "success", "console", "log", "error", "messages", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "search", "controlName", "control", "errors", "touched", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightService", "i3", "AuthService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "FlightComponent_Template", "rf", "ctx", "FlightComponent_Template_form_ngSubmit_10_listener", "FlightComponent_Template_button_click_12_listener", "FlightComponent_Template_button_click_14_listener", "FlightComponent_Template_button_click_16_listener", "FlightComponent_div_26_Template", "FlightComponent_div_31_Template", "FlightComponent_div_36_Template", "FlightComponent_div_37_Template", "FlightComponent_div_38_Template", "FlightComponent_div_39_Template", "FlightComponent_Template_button_click_51_listener", "FlightComponent_Template_button_click_53_listener", "FlightComponent_Template_button_click_61_listener", "FlightComponent_Template_button_click_63_listener", "FlightComponent_Template_button_click_71_listener", "FlightComponent_Template_button_click_73_listener", "FlightComponent_Template_button_click_76_listener", "FlightComponent_option_110_Template", "FlightComponent_Template_input_change_116_listener", "FlightComponent_span_118_Template", "FlightComponent_span_121_Template", "FlightComponent_span_122_Template", "FlightComponent_div_130_Template", "FlightComponent_div_132_Template", "FlightComponent_div_134_Template", "ɵɵclassProp", "tmp_10_0", "tmp_11_0", "tmp_12_0", "ɵɵpipeBind1", "tmp_20_0", "length", "tmp_21_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight\\flight.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { FlightService } from '../../services/flight.service';\nimport { AuthService } from '../../services/auth.service';\nimport { FlightSearchForm, LatestSearch, Location } from '../../models/flight.models';\nimport { Observable } from 'rxjs';\n\ninterface FlightSegment {\n  from: string;\n  to: string;\n  date: string;\n}\n\n@Component({\n  selector: 'app-flight',\n  templateUrl: './flight.component.html',\n  styleUrls: ['./flight.component.css']\n})\nexport class FlightComponent implements OnInit {\n  flightForm: FormGroup;\n  isLoading = false;\n  currentUser: any = null;\n  latestSearches$: Observable<LatestSearch[]>;\n\n  // Form state\n  tripType = 'oneWay';\n  showReturnDate = false;\n  showCalendar = false;\n  additionalSegments: FlightSegment[] = [];\n\n  // Passenger counts\n  adultCount = 1;\n  childCount = 0;\n  infantCount = 0;\n\n  // Class selection\n  selectedClass = 'economy';\n\n  // Baggage options\n  baggageOptions = [\n    { value: 'all', label: '--All--' },\n    { value: '20kg', label: '20kg' },\n    { value: '30kg', label: '30kg' },\n    { value: 'extra', label: 'Extra' }\n  ];\n\n  // Calendar options\n  calendarDays = [\n    { value: '1', label: '+/- 1 Days' },\n    { value: '3', label: '+/- 3 Days' },\n    { value: '7', label: '+/- 7 Days' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private flightService: FlightService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n\n  ngOnInit(): void {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n\n    // Set default dates\n    this.setDefaultDates();\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  private setDefaultDates(): void {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n\n  /**\n   * Format date for input field\n   */\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type: string): void {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n\n    this.flightForm.patchValue({ tripType: type });\n\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n  }\n\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n\n    this.flightForm.patchValue({ [type]: newValue });\n\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers(): number {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass: string): void {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({ class: flightClass });\n  }\n\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar(): void {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({ calendar: this.showCalendar });\n  }\n\n  /**\n   * Swap from and to locations\n   */\n  swapLocations(): void {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.flightForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n\n      // Perform search based on trip type\n      let searchObservable;\n\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n\n      searchObservable.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // TODO: Navigate to results page or display results\n            // this.router.navigate(['/flight-results'], { state: { results: response } });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search: LatestSearch): void {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches(): void {\n    this.flightService.clearLatestSearches();\n  }\n\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"flight-container\">\n  <!-- Main Content -->\n  <div class=\"flight-content\">\n    <!-- Left Panel - Flight Search -->\n    <div class=\"flight-search-panel\">\n      <div class=\"search-header\">\n        <div class=\"search-title\">\n          <i class=\"fas fa-plane\"></i>\n          <h2>Search and Book Flights</h2>\n        </div>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n\n      <form [formGroup]=\"flightForm\" (ngSubmit)=\"onSubmit()\" class=\"flight-form\">\n        <!-- Trip Type Selection -->\n        <div class=\"trip-type-selector\">\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'oneWay'\"\n            (click)=\"onTripTypeChange('oneWay')\">\n            One way\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'roundTrip'\"\n            (click)=\"onTripTypeChange('roundTrip')\">\n            Round Trip\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'multiCity'\"\n            (click)=\"onTripTypeChange('multiCity')\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location and Date Selection -->\n        <div class=\"location-date-section\">\n          <!-- Flight Segments -->\n          <div class=\"flight-segments\">\n            <!-- First Segment (always visible) -->\n            <div class=\"flight-segment\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-departure\"></i>\n                    <input\n                      type=\"text\"\n                      formControlName=\"from\"\n                      placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                      class=\"location-input\">\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('from')\">\n                    {{ getErrorMessage('from') }}\n                  </div>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-arrival\"></i>\n                    <input\n                      type=\"text\"\n                      formControlName=\"to\"\n                      placeholder=\"Going to (City, Country Or Specific Airport)\"\n                      class=\"location-input\">\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('to')\">\n                    {{ getErrorMessage('to') }}\n                  </div>\n                </div>\n\n                <!-- Departure Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"departureDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('departureDate')\">\n                    {{ getErrorMessage('departureDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Second Segment (for Round Trip) -->\n            <div class=\"flight-segment\" *ngIf=\"showReturnDate\">\n              <div class=\"segment-row\">\n                <!-- Return From Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-departure\"></i>\n                    <input\n                      type=\"text\"\n                      [value]=\"flightForm.get('to')?.value\"\n                      placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                      class=\"location-input\"\n                      readonly>\n                  </div>\n                </div>\n\n                <!-- Return To Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-arrival\"></i>\n                    <input\n                      type=\"text\"\n                      [value]=\"flightForm.get('from')?.value\"\n                      placeholder=\"Going to (City, Country Or Specific Airport)\"\n                      class=\"location-input\"\n                      readonly>\n                  </div>\n                </div>\n\n                <!-- Return Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"returnDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('returnDate')\">\n                    {{ getErrorMessage('returnDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Additional Segments for Multi-City -->\n            <div class=\"flight-segment\" *ngFor=\"let segment of additionalSegments; let i = index\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-departure\"></i>\n                    <input\n                      type=\"text\"\n                      [(ngModel)]=\"segment.from\"\n                      placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                      class=\"location-input\">\n                  </div>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <div class=\"location-input-wrapper\">\n                    <i class=\"fas fa-plane-arrival\"></i>\n                    <input\n                      type=\"text\"\n                      [(ngModel)]=\"segment.to\"\n                      placeholder=\"Going to (City, Country Or Specific Airport)\"\n                      class=\"location-input\">\n                  </div>\n                </div>\n\n                <!-- Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      [(ngModel)]=\"segment.date\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                </div>\n\n                <!-- Remove Segment Button -->\n                <button type=\"button\" class=\"remove-segment-btn\" (click)=\"removeSegment(i)\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Add Sector Button -->\n            <div class=\"add-sector-section\" *ngIf=\"tripType === 'multiCity'\">\n              <button type=\"button\" class=\"add-sector-btn\" (click)=\"addSegment()\">\n                <i class=\"fas fa-plus\"></i>\n                Add Sector\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger and Class Selection -->\n        <div class=\"passenger-class-section\">\n          <!-- Passenger Count -->\n          <div class=\"form-group passenger-group\">\n            <label>Passenger & Class of travel</label>\n            <div class=\"passenger-controls\">\n              <!-- Adults -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-user\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('adults')?.value || 1 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Children -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-child\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('children')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Infants -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-baby\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('infants')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Class Selection -->\n              <div class=\"class-selection\">\n                <button\n                  type=\"button\"\n                  class=\"class-btn\"\n                  [class.active]=\"selectedClass === 'economy'\"\n                  (click)=\"onClassChange('economy')\">\n                  <i class=\"fas fa-chair\"></i> Economy\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Preferred Airline -->\n          <div class=\"form-group airline-group\">\n            <label for=\"preferredAirline\">Preferred Airline</label>\n            <select id=\"preferredAirline\" formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option value=\"TK\">Turkish Airlines</option>\n              <option value=\"AF\">Air France</option>\n              <option value=\"LH\">Lufthansa</option>\n              <option value=\"EK\">Emirates</option>\n              <option value=\"QR\">Qatar Airways</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Additional Options -->\n        <div class=\"additional-options\">\n          <!-- Refundable Fares -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"refundableFares\" class=\"option-select\">\n                <option value=\"false\">--All--</option>\n                <option value=\"true\">Refundable Only</option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Baggage -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"baggage\" class=\"option-select\">\n                <option *ngFor=\"let option of baggageOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Calendar -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"option-controls\">\n              <div class=\"calendar-toggle\">\n                <input\n                  type=\"checkbox\"\n                  id=\"calendar\"\n                  formControlName=\"calendar\"\n                  (change)=\"toggleCalendar()\">\n                <label for=\"calendar\" class=\"calendar-label\">\n                  <span class=\"calendar-days\" *ngIf=\"showCalendar\">\n                    <select formControlName=\"calendarDays\" class=\"calendar-select\">\n                      <option *ngFor=\"let option of calendarDays\" [value]=\"option.value\">\n                        {{ option.label }}\n                      </option>\n                    </select>\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <div class=\"search-button-section\">\n          <button\n            type=\"submit\"\n            class=\"search-btn\"\n            [disabled]=\"isLoading || !flightForm.valid\">\n            <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n            <span *ngIf=\"isLoading\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Searching...\n            </span>\n          </button>\n        </div>\n      </form>\n    </div>\n\n    <!-- Right Panel - Latest Searches -->\n    <div class=\"latest-searches-panel\">\n      <div class=\"searches-header\">\n        <h3>Latest Searches</h3>\n        <p>We're bringing you a new level of comfort</p>\n      </div>\n\n      <div class=\"searches-list\">\n        <div\n          *ngFor=\"let search of latestSearches$ | async\"\n          class=\"search-item\"\n          (click)=\"loadLatestSearch(search)\">\n          <div class=\"search-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <div class=\"search-details\">\n            <div class=\"search-route\">\n              Coming from <strong>{{ search.from }}</strong> - <strong>{{ search.to }}</strong> on {{ search.date | date:'MMM d, yyyy' }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div *ngIf=\"(latestSearches$ | async)?.length === 0\" class=\"empty-searches\">\n          <i class=\"fas fa-search\"></i>\n          <p>No recent searches</p>\n          <small>Your recent flight searches will appear here</small>\n        </div>\n      </div>\n\n      <!-- Clear searches button -->\n      <div class=\"searches-actions\" *ngIf=\"(latestSearches$ | async)?.length! > 0\">\n        <button type=\"button\" class=\"clear-btn\" (click)=\"clearLatestSearches()\">\n          <i class=\"fas fa-trash\"></i> Clear All\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICuDjDC,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,cACF;;;;;IAaAP,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAG,MAAA,CAAAD,eAAA,YACF;;;;;IAaAP,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAI,MAAA,CAAAF,eAAA,uBACF;;;;;IA4CAP,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAK,OAAA,CAAAH,eAAA,oBACF;;;;;IAxCNP,EAAA,CAAAC,cAAA,cAAmD;IAK3CD,EAAA,CAAAW,SAAA,YAAsC;IAOxCX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAW,SAAA,YAAoC;IAOtCX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,eAAmC;IAE/BD,EAAA,CAAAW,SAAA,iBAIqB;IAEvBX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAY,UAAA,KAAAC,sCAAA,kBAEM;IACRb,EAAA,CAAAG,YAAA,EAAM;;;;;;IAjCAH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAc,UAAA,WAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,yBAAAH,OAAA,CAAAI,KAAA,CAAqC;IAarCnB,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAc,UAAA,WAAAM,OAAA,GAAAJ,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAE,OAAA,CAAAD,KAAA,CAAuC;IAiBfnB,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAc,UAAA,SAAAE,MAAA,CAAAT,eAAA,eAAmC;;;;;;IAQrEP,EAAA,CAAAC,cAAA,cAAsF;IAK9ED,EAAA,CAAAW,SAAA,YAAsC;IACtCX,EAAA,CAAAC,cAAA,gBAIyB;IAFvBD,EAAA,CAAAqB,UAAA,2BAAAC,+DAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAG,IAAA,GAAAP,MAAA,CAC7B;IAAA,EAD0C;IAF5BvB,EAAA,CAAAG,YAAA,EAIyB;IAK7BH,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAW,SAAA,YAAoC;IACpCX,EAAA,CAAAC,cAAA,gBAIyB;IAFvBD,EAAA,CAAAqB,UAAA,2BAAAU,+DAAAR,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAK,EAAA,GAAAT,MAAA,CAC7B;IAAA,EADwC;IAF1BvB,EAAA,CAAAG,YAAA,EAIyB;IAK7BH,EAAA,CAAAC,cAAA,eAAmC;IAI7BD,EAAA,CAAAqB,UAAA,2BAAAY,gEAAAV,MAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAC,WAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,OAAa5B,EAAA,CAAA6B,WAAA,CAAAF,WAAA,CAAAO,IAAA,GAAAX,MAAA,CAC7B;IAAA,EAD0C;IAF5BvB,EAAA,CAAAG,YAAA,EAIqB;IACrBH,EAAA,CAAAW,SAAA,aAA6C;IAC/CX,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAC,cAAA,kBAA4E;IAA3BD,EAAA,CAAAqB,UAAA,mBAAAc,yDAAA;MAAA,MAAAX,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAAC,IAAA;MAAA,MAAAU,KAAA,GAAAZ,WAAA,CAAAa,KAAA;MAAA,MAAAC,OAAA,GAAAtC,EAAA,CAAAuC,aAAA;MAAA,OAASvC,EAAA,CAAA6B,WAAA,CAAAS,OAAA,CAAAE,aAAA,CAAAJ,KAAA,CAAgB;IAAA,EAAC;IACzEpC,EAAA,CAAAW,SAAA,aAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAS;;;;IAjCHH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAG,IAAA,CAA0B;IAY1B9B,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAK,EAAA,CAAwB;IAWxBhC,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAc,UAAA,YAAAa,WAAA,CAAAO,IAAA,CAA0B;;;;;;IAepClC,EAAA,CAAAC,cAAA,cAAiE;IAClBD,EAAA,CAAAqB,UAAA,mBAAAoB,wDAAA;MAAAzC,EAAA,CAAAyB,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA3C,EAAA,CAAAuC,aAAA;MAAA,OAASvC,EAAA,CAAA6B,WAAA,CAAAc,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACjE5C,EAAA,CAAAW,SAAA,YAA2B;IAC3BX,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAsFPH,EAAA,CAAAC,cAAA,iBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFqCH,EAAA,CAAAc,UAAA,UAAA+B,UAAA,CAAA1B,KAAA,CAAsB;IAClEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwC,UAAA,CAAAC,KAAA,MACF;;;;;IAkBM9C,EAAA,CAAAC,cAAA,iBAAmE;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFmCH,EAAA,CAAAc,UAAA,UAAAiC,UAAA,CAAA5B,KAAA,CAAsB;IAChEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,UAAA,CAAAD,KAAA,MACF;;;;;IAJJ9C,EAAA,CAAAC,cAAA,eAAiD;IAE7CD,EAAA,CAAAY,UAAA,IAAAoC,0CAAA,qBAES;IACXhD,EAAA,CAAAG,YAAA,EAAS;;;;IAHoBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAc,UAAA,YAAAmC,MAAA,CAAAC,YAAA,CAAe;;;;;IAiBpDlD,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC1CH,EAAA,CAAAC,cAAA,WAAwB;IACtBD,EAAA,CAAAW,SAAA,YAAsC;IAACX,EAAA,CAAAE,MAAA,qBACzC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAcXH,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAqB,UAAA,mBAAA8B,sDAAA;MAAA,MAAA3B,WAAA,GAAAxB,EAAA,CAAAyB,aAAA,CAAA2B,IAAA;MAAA,MAAAC,UAAA,GAAA7B,WAAA,CAAAI,SAAA;MAAA,MAAA0B,OAAA,GAAAtD,EAAA,CAAAuC,aAAA;MAAA,OAASvC,EAAA,CAAA6B,WAAA,CAAAyB,OAAA,CAAAC,gBAAA,CAAAF,UAAA,CAAwB;IAAA,EAAC;IAClCrD,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAW,SAAA,WAA4B;IAC9BX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA4B;IAExBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,UAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACpF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADgBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAwD,iBAAA,CAAAH,UAAA,CAAAvB,IAAA,CAAiB;IAAoB9B,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAwD,iBAAA,CAAAH,UAAA,CAAArB,EAAA,CAAe;IAAUhC,EAAA,CAAAI,SAAA,GACpF;IADoFJ,EAAA,CAAAK,kBAAA,SAAAL,EAAA,CAAAyD,WAAA,QAAAJ,UAAA,CAAAnB,IAAA,sBACpF;;;;;IAKJlC,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAW,SAAA,YAA6B;IAC7BX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;;IAK/DH,EAAA,CAAAC,cAAA,cAA6E;IACnCD,EAAA,CAAAqB,UAAA,mBAAAqC,yDAAA;MAAA1D,EAAA,CAAAyB,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA5D,EAAA,CAAAuC,aAAA;MAAA,OAASvC,EAAA,CAAA6B,WAAA,CAAA+B,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IACrE7D,EAAA,CAAAW,SAAA,YAA4B;IAACX,EAAA,CAAAE,MAAA,kBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADjVjB,OAAM,MAAO2D,eAAe;EAmC1BC,YACUC,EAAe,EACfC,aAA4B,EAC5BC,WAAwB,EACxBC,MAAc;IAHd,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IArChB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAQ,IAAI;IAGvB;IACA,KAAAC,QAAQ,GAAG,QAAQ;IACnB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,kBAAkB,GAAoB,EAAE;IAExC;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,aAAa,GAAG,SAAS;IAEzB;IACA,KAAAC,cAAc,GAAG,CACf;MAAE3D,KAAK,EAAE,KAAK;MAAE2B,KAAK,EAAE;IAAS,CAAE,EAClC;MAAE3B,KAAK,EAAE,MAAM;MAAE2B,KAAK,EAAE;IAAM,CAAE,EAChC;MAAE3B,KAAK,EAAE,MAAM;MAAE2B,KAAK,EAAE;IAAM,CAAE,EAChC;MAAE3B,KAAK,EAAE,OAAO;MAAE2B,KAAK,EAAE;IAAO,CAAE,CACnC;IAED;IACA,KAAAI,YAAY,GAAG,CACb;MAAE/B,KAAK,EAAE,GAAG;MAAE2B,KAAK,EAAE;IAAY,CAAE,EACnC;MAAE3B,KAAK,EAAE,GAAG;MAAE2B,KAAK,EAAE;IAAY,CAAE,EACnC;MAAE3B,KAAK,EAAE,GAAG;MAAE2B,KAAK,EAAE;IAAY,CAAE,CACpC;IAQC,IAAI,CAAC7B,UAAU,GAAG,IAAI,CAAC8D,UAAU,EAAE;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACf,aAAa,CAACe,eAAe;EAC3D;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACf,WAAW,CAACgB,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACf,WAAW,GAAGe,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAAClB,WAAW,CAACmB,eAAe,EAAE,EAAE;MACvC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACjC;;IAGF;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEA;;;EAGQR,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACf,EAAE,CAACwB,KAAK,CAAC;MACnBlB,QAAQ,EAAE,CAAC,QAAQ,EAAEvE,UAAU,CAAC0F,QAAQ,CAAC;MACzC3D,IAAI,EAAE,CAAC,EAAE,EAAE/B,UAAU,CAAC0F,QAAQ,CAAC;MAC/BzD,EAAE,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAAC0F,QAAQ,CAAC;MAC7BC,aAAa,EAAE,CAAC,EAAE,EAAE3F,UAAU,CAAC0F,QAAQ,CAAC;MACxCE,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC7F,UAAU,CAAC0F,QAAQ,EAAE1F,UAAU,CAAC8F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC/F,UAAU,CAAC8F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAChG,UAAU,CAAC8F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCG,KAAK,EAAE,CAAC,SAAS,EAAEjG,UAAU,CAAC0F,QAAQ,CAAC;MACvCQ,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtBC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,eAAe,EAAE,CAAC,KAAK,CAAC;MACxBC,OAAO,EAAE,CAAC,KAAK,CAAC;MAChBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBnD,YAAY,EAAE,CAAC,GAAG;KACnB,CAAC;EACJ;EAEA;;;EAGQqC,eAAeA,CAAA;IACrB,MAAMe,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,MAAMC,QAAQ,GAAG,IAAID,IAAI,CAACD,KAAK,CAAC;IAChCE,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,MAAMC,QAAQ,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAAC;IAChCK,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,IAAI,CAACzF,UAAU,CAAC2F,UAAU,CAAC;MACzBlB,aAAa,EAAE,IAAI,CAACmB,UAAU,CAACL,QAAQ,CAAC;MACxCb,UAAU,EAAE,IAAI,CAACkB,UAAU,CAACF,QAAQ;KACrC,CAAC;EACJ;EAEA;;;EAGQE,UAAUA,CAAC3E,IAAU;IAC3B,OAAOA,IAAI,CAAC4E,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEA;;;EAGAC,gBAAgBA,CAACC,IAAY;IAC3B,IAAI,CAAC3C,QAAQ,GAAG2C,IAAI;IACpB,IAAI,CAAC1C,cAAc,GAAG0C,IAAI,KAAK,WAAW;IAE1C,IAAI,CAAChG,UAAU,CAAC2F,UAAU,CAAC;MAAEtC,QAAQ,EAAE2C;IAAI,CAAE,CAAC;IAE9C,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAAChG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEgG,aAAa,CAAC,CAACnH,UAAU,CAAC0F,QAAQ,CAAC,CAAC;KACxE,MAAM;MACL,IAAI,CAACxE,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEiG,eAAe,EAAE;;IAEtD,IAAI,CAAClG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAEkG,sBAAsB,EAAE;EAC7D;EAEA;;;EAGAC,oBAAoBA,CAACJ,IAAuC,EAAEK,SAAkB;IAC9E,MAAMC,YAAY,GAAG,IAAI,CAACtG,UAAU,CAACC,GAAG,CAAC+F,IAAI,CAAC,EAAE9F,KAAK,IAAI,CAAC;IAC1D,IAAIqG,QAAQ,GAAGF,SAAS,GAAGC,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEH,YAAY,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIN,IAAI,KAAK,QAAQ,IAAIO,QAAQ,GAAG,CAAC,EAAE;MACrCA,QAAQ,GAAG,CAAC;;IAGd,IAAI,CAACvG,UAAU,CAAC2F,UAAU,CAAC;MAAE,CAACK,IAAI,GAAGO;IAAQ,CAAE,CAAC;IAEhD;IACA,IAAIP,IAAI,KAAK,QAAQ,EAAE,IAAI,CAACvC,UAAU,GAAG8C,QAAQ;IACjD,IAAIP,IAAI,KAAK,UAAU,EAAE,IAAI,CAACtC,UAAU,GAAG6C,QAAQ;IACnD,IAAIP,IAAI,KAAK,SAAS,EAAE,IAAI,CAACrC,WAAW,GAAG4C,QAAQ;EACrD;EAEA;;;EAGAG,kBAAkBA,CAAA;IAChB,MAAM/B,MAAM,GAAG,IAAI,CAAC3E,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,IAAI,CAAC;IACxD,MAAM2E,QAAQ,GAAG,IAAI,CAAC7E,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC5D,MAAM4E,OAAO,GAAG,IAAI,CAAC9E,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC1D,OAAOyE,MAAM,GAAGE,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGA6B,aAAaA,CAACC,WAAmB;IAC/B,IAAI,CAAChD,aAAa,GAAGgD,WAAW;IAChC,IAAI,CAAC5G,UAAU,CAAC2F,UAAU,CAAC;MAAEZ,KAAK,EAAE6B;IAAW,CAAE,CAAC;EACpD;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACtD,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACvD,UAAU,CAAC2F,UAAU,CAAC;MAAEP,QAAQ,EAAE,IAAI,CAAC7B;IAAY,CAAE,CAAC;EAC7D;EAEA;;;EAGAuD,aAAaA,CAAA;IACX,MAAMjG,IAAI,GAAG,IAAI,CAACb,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAEC,KAAK;IAC/C,MAAMa,EAAE,GAAG,IAAI,CAACf,UAAU,CAACC,GAAG,CAAC,IAAI,CAAC,EAAEC,KAAK;IAE3C,IAAI,CAACF,UAAU,CAAC2F,UAAU,CAAC;MACzB9E,IAAI,EAAEE,EAAE;MACRA,EAAE,EAAEF;KACL,CAAC;EACJ;EAEA;;;EAGAkG,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC/G,UAAU,CAACgH,KAAK,EAAE;MACzB,IAAI,CAAC7D,SAAS,GAAG,IAAI;MAErB,MAAM8D,SAAS,GAAG,IAAI,CAACjH,UAAU,CAACE,KAAK;MACvC,MAAMgH,UAAU,GAAqB;QACnC7D,QAAQ,EAAE4D,SAAS,CAAC5D,QAAQ;QAC5BxC,IAAI,EAAEoG,SAAS,CAACpG,IAAI;QACpBE,EAAE,EAAEkG,SAAS,CAAClG,EAAE;QAChB0D,aAAa,EAAEwC,SAAS,CAACxC,aAAa;QACtCC,UAAU,EAAEuC,SAAS,CAACvC,UAAU;QAChCyC,UAAU,EAAE;UACVxC,MAAM,EAAEsC,SAAS,CAACtC,MAAM;UACxBE,QAAQ,EAAEoC,SAAS,CAACpC,QAAQ;UAC5BC,OAAO,EAAEmC,SAAS,CAACnC;SACpB;QACDC,KAAK,EAAEkC,SAAS,CAAClC,KAAK;QACtBC,gBAAgB,EAAEiC,SAAS,CAACjC,gBAAgB;QAC5CC,aAAa,EAAEgC,SAAS,CAAChC,aAAa;QACtCC,eAAe,EAAE+B,SAAS,CAAC/B,eAAe;QAC1CC,OAAO,EAAE8B,SAAS,CAAC9B,OAAO;QAC1BC,QAAQ,EAAE6B,SAAS,CAAC7B;OACrB;MAED;MACA,IAAI,CAACpC,aAAa,CAACoE,gBAAgB,CAACF,UAAU,CAAC;MAE/C;MACA,IAAIG,gBAAgB;MAEpB,QAAQH,UAAU,CAAC7D,QAAQ;QACzB,KAAK,QAAQ;UACXgE,gBAAgB,GAAG,IAAI,CAACrE,aAAa,CAACsE,mBAAmB,CAACJ,UAAU,CAAC;UACrE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACrE,aAAa,CAACuE,sBAAsB,CAACL,UAAU,CAAC;UACxE;QACF,KAAK,WAAW;UACdG,gBAAgB,GAAG,IAAI,CAACrE,aAAa,CAACwE,sBAAsB,CAACN,UAAU,CAAC;UACxE;QACF;UACEG,gBAAgB,GAAG,IAAI,CAACrE,aAAa,CAACsE,mBAAmB,CAACJ,UAAU,CAAC;;MAGzEG,gBAAgB,CAACnD,SAAS,CAAC;QACzBuD,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACvE,SAAS,GAAG,KAAK;UACtB,IAAIuE,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3BC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,QAAQ,CAAC;YAC/C;YACA;WACD,MAAM;YACLG,OAAO,CAACE,KAAK,CAAC,gBAAgB,EAAEL,QAAQ,CAACC,MAAM,CAACK,QAAQ,CAAC;YACzD;;QAEJ,CAAC;;QACDD,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAC5E,SAAS,GAAG,KAAK;UACtB0E,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC;QACF;OACD,CAAC;KACH,MAAM;MACL;MACAE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClI,UAAU,CAACmI,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;QAClD,IAAI,CAACrI,UAAU,CAACC,GAAG,CAACoI,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC3C,CAAC,CAAC;;EAEN;EAEA;;;EAGAhG,gBAAgBA,CAACiG,MAAoB;IACnC,IAAI,CAACvI,UAAU,CAAC2F,UAAU,CAAC;MACzB9E,IAAI,EAAE0H,MAAM,CAAC1H,IAAI;MACjBE,EAAE,EAAEwH,MAAM,CAACxH,EAAE;MACb0D,aAAa,EAAE8D,MAAM,CAACtH,IAAI;MAC1B0D,MAAM,EAAE4D,MAAM,CAACpB,UAAU;MACzBtC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;;;EAGAlC,mBAAmBA,CAAA;IACjB,IAAI,CAACI,aAAa,CAACJ,mBAAmB,EAAE;EAC1C;EAEA;;;EAGAtD,eAAeA,CAACkJ,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACzI,UAAU,CAACC,GAAG,CAACuI,WAAW,CAAC;IAChD,IAAIC,OAAO,EAAEC,MAAM,IAAID,OAAO,CAACE,OAAO,EAAE;MACtC,IAAIF,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGF,WAAW,cAAc;;MAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oBAAoBD,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,CAAC9D,GAAG,EAAE;;;IAG1D,OAAO,EAAE;EACX;;;uBAnSW/B,eAAe,EAAA9D,EAAA,CAAA6J,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/J,EAAA,CAAA6J,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAjK,EAAA,CAAA6J,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAnK,EAAA,CAAA6J,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfvG,eAAe;MAAAwG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB5B5K,EAAA,CAAAC,cAAA,aAA8B;UAOpBD,EAAA,CAAAW,SAAA,WAA4B;UAC5BX,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAElCH,EAAA,CAAAC,cAAA,WAA2B;UAAAD,EAAA,CAAAE,MAAA,gDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG1EH,EAAA,CAAAC,cAAA,eAA2E;UAA5CD,EAAA,CAAAqB,UAAA,sBAAAyJ,mDAAA;YAAA,OAAYD,GAAA,CAAA7C,QAAA,EAAU;UAAA,EAAC;UAEpDhI,EAAA,CAAAC,cAAA,cAAgC;UAK5BD,EAAA,CAAAqB,UAAA,mBAAA0J,kDAAA;YAAA,OAASF,GAAA,CAAA7D,gBAAA,CAAiB,QAAQ,CAAC;UAAA,EAAC;UACpChH,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAAqB,UAAA,mBAAA2J,kDAAA;YAAA,OAASH,GAAA,CAAA7D,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvChH,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAI0C;UAAxCD,EAAA,CAAAqB,UAAA,mBAAA4J,kDAAA;YAAA,OAASJ,GAAA,CAAA7D,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvChH,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,eAAmC;UASvBD,EAAA,CAAAW,SAAA,aAAsC;UAMxCX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAAsK,+BAAA,kBAEM;UACRlL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAuC;UAEnCD,EAAA,CAAAW,SAAA,aAAoC;UAMtCX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAAuK,+BAAA,kBAEM;UACRnL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAmC;UAE/BD,EAAA,CAAAW,SAAA,iBAIqB;UAEvBX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAY,UAAA,KAAAwK,+BAAA,kBAEM;UACRpL,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAY,UAAA,KAAAyK,+BAAA,mBA2CM;UAGNrL,EAAA,CAAAY,UAAA,KAAA0K,+BAAA,mBA2CM;UAGNtL,EAAA,CAAAY,UAAA,KAAA2K,+BAAA,kBAKM;UACRvL,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAqC;UAG1BD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAAgC;UAGCD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/EH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAAmK,kDAAA;YAAA,OAASX,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpGH,EAAA,CAAAC,cAAA,kBAAyF;UAA/CD,EAAA,CAAAqB,UAAA,mBAAAoK,kDAAA;YAAA,OAASZ,GAAA,CAAAxD,oBAAA,CAAqB,QAAQ,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKvGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA4B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAChEH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAAqK,kDAAA;YAAA,OAASb,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtGH,EAAA,CAAAC,cAAA,kBAA2F;UAAjDD,EAAA,CAAAqB,UAAA,mBAAAsK,kDAAA;YAAA,OAASd,GAAA,CAAAxD,oBAAA,CAAqB,UAAU,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKzGH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAW,SAAA,aAA2B;UAAAX,EAAA,CAAAG,YAAA,EAAO;UAC/DH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChFH,EAAA,CAAAC,cAAA,eAA8B;UACcD,EAAA,CAAAqB,UAAA,mBAAAuK,kDAAA;YAAA,OAASf,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrGH,EAAA,CAAAC,cAAA,kBAA0F;UAAhDD,EAAA,CAAAqB,UAAA,mBAAAwK,kDAAA;YAAA,OAAShB,GAAA,CAAAxD,oBAAA,CAAqB,SAAS,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrH,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKxGH,EAAA,CAAAC,cAAA,eAA6B;UAKzBD,EAAA,CAAAqB,UAAA,mBAAAyK,kDAAA;YAAA,OAASjB,GAAA,CAAAjD,aAAA,CAAc,SAAS,CAAC;UAAA,EAAC;UAClC5H,EAAA,CAAAW,SAAA,aAA4B;UAACX,EAAA,CAAAE,MAAA,iBAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,eAAsC;UACND,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAwF;UACrED,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,kBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM/CH,EAAA,CAAAC,cAAA,eAAgC;UAGAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,eAA6B;UAEHD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,mBAAqB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMnDH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3CH,EAAA,CAAAC,cAAA,gBAA6B;UAEzBD,EAAA,CAAAY,UAAA,MAAAmL,mCAAA,qBAES;UACX/L,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,gBAA0B;UACID,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5CH,EAAA,CAAAC,cAAA,gBAA6B;UAMvBD,EAAA,CAAAqB,UAAA,oBAAA2K,mDAAA;YAAA,OAAUnB,GAAA,CAAA/C,cAAA,EAAgB;UAAA,EAAC;UAJ7B9H,EAAA,CAAAG,YAAA,EAI8B;UAC9BH,EAAA,CAAAC,cAAA,kBAA6C;UAC3CD,EAAA,CAAAY,UAAA,MAAAqL,iCAAA,mBAMO;UACTjM,EAAA,CAAAG,YAAA,EAAQ;UAOhBH,EAAA,CAAAC,cAAA,gBAAmC;UAK/BD,EAAA,CAAAY,UAAA,MAAAsL,iCAAA,mBAA0C;UAC1ClM,EAAA,CAAAY,UAAA,MAAAuL,iCAAA,mBAEO;UACTnM,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAC,cAAA,gBAAmC;UAE3BD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGlDH,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAY,UAAA,MAAAwL,gCAAA,mBAYM;;UAGNpM,EAAA,CAAAY,UAAA,MAAAyL,gCAAA,kBAIM;;UACRrM,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAY,UAAA,MAAA0L,gCAAA,kBAIM;;UACRtM,EAAA,CAAAG,YAAA,EAAM;;;;;;;;UAzVEH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAc,UAAA,cAAA+J,GAAA,CAAA5J,UAAA,CAAwB;UAMxBjB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAuM,WAAA,WAAA1B,GAAA,CAAAvG,QAAA,cAAsC;UAOtCtE,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAuM,WAAA,WAAA1B,GAAA,CAAAvG,QAAA,iBAAyC;UAOzCtE,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAuM,WAAA,WAAA1B,GAAA,CAAAvG,QAAA,iBAAyC;UAuBPtE,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAtK,eAAA,SAA6B;UAe7BP,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAtK,eAAA,OAA2B;UAe3BP,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAtK,eAAA,kBAAsC;UAQ3CP,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAtG,cAAA,CAAoB;UA8CDvE,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAc,UAAA,YAAA+J,GAAA,CAAApG,kBAAA,CAAuB;UA8CtCzE,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAvG,QAAA,iBAA8B;UAkB7BtE,EAAA,CAAAI,SAAA,IAA0C;UAA1CJ,EAAA,CAAAwD,iBAAA,GAAAgJ,QAAA,GAAA3B,GAAA,CAAA5J,UAAA,CAAAC,GAAA,6BAAAsL,QAAA,CAAArL,KAAA,OAA0C;UAU1CnB,EAAA,CAAAI,SAAA,IAA4C;UAA5CJ,EAAA,CAAAwD,iBAAA,GAAAiJ,QAAA,GAAA5B,GAAA,CAAA5J,UAAA,CAAAC,GAAA,+BAAAuL,QAAA,CAAAtL,KAAA,OAA4C;UAU5CnB,EAAA,CAAAI,SAAA,IAA2C;UAA3CJ,EAAA,CAAAwD,iBAAA,GAAAkJ,QAAA,GAAA7B,GAAA,CAAA5J,UAAA,CAAAC,GAAA,8BAAAwL,QAAA,CAAAvL,KAAA,OAA2C;UAYvEnB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAuM,WAAA,WAAA1B,GAAA,CAAAhG,aAAA,eAA4C;UAwCnB7E,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAc,UAAA,YAAA+J,GAAA,CAAA/F,cAAA,CAAiB;UAkBb9E,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAArG,YAAA,CAAkB;UAkBrDxE,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAc,UAAA,aAAA+J,GAAA,CAAAzG,SAAA,KAAAyG,GAAA,CAAA5J,UAAA,CAAAgH,KAAA,CAA2C;UACpCjI,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,UAAA+J,GAAA,CAAAzG,SAAA,CAAgB;UAChBpE,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAc,UAAA,SAAA+J,GAAA,CAAAzG,SAAA,CAAe;UAiBLpE,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAA2M,WAAA,UAAA9B,GAAA,CAAA7F,eAAA,EAA0B;UAczChF,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAc,UAAA,WAAA8L,QAAA,GAAA5M,EAAA,CAAA2M,WAAA,UAAA9B,GAAA,CAAA7F,eAAA,oBAAA4H,QAAA,CAAAC,MAAA,QAA6C;UAQtB7M,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAc,UAAA,WAAAgM,QAAA,GAAA9M,EAAA,CAAA2M,WAAA,UAAA9B,GAAA,CAAA7F,eAAA,oBAAA8H,QAAA,CAAAD,MAAA,MAA4C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}