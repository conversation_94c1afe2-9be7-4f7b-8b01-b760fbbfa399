{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, FormsModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, SigninComponent, DashboardComponent, FlightComponent, FlightResultsComponent, AutocompleteComponent],\n    imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, FormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "AppRoutingModule", "AppComponent", "SigninComponent", "DashboardComponent", "FlightComponent", "FlightResultsComponent", "AutocompleteComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    SigninComponent,\n    DashboardComponent,\n    FlightComponent,\n    FlightResultsComponent,\n    AutocompleteComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    FormsModule,\n    HttpClientModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,QAA2B,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,qBAAqB,QAAQ,yDAAyD;;AAsB/F,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRP,YAAY;IAAA;EAAA;;;gBAPtBL,aAAa,EACbI,gBAAgB,EAChBH,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB;IAAA;EAAA;;;2EAKPQ,SAAS;IAAAE,YAAA,GAjBlBR,YAAY,EACZC,eAAe,EACfC,kBAAkB,EAClBC,eAAe,EACfC,sBAAsB,EACtBC,qBAAqB;IAAAI,OAAA,GAGrBd,aAAa,EACbI,gBAAgB,EAChBH,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}