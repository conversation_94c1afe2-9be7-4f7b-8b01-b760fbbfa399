{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/flight.service\";\nimport * as i4 from \"@angular/common\";\nfunction FlightResultsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"span\", 11);\n    i0.ɵɵtext(3, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Searching for flights...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightResultsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"h4\");\n    i0.ɵɵtext(2, \"Error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.retrySearch());\n    });\n    i0.ɵɵtext(6, \"Try Again\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.error);\n  }\n}\nfunction FlightResultsComponent_div_12_div_2_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtext(1, \" RESTRICTED \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = function (a0, a1) {\n  return {\n    \"direct\": a0,\n    \"stops\": a1\n  };\n};\nconst _c1 = function (a0, a1) {\n  return {\n    \"refundable\": a0,\n    \"non-refundable\": a1\n  };\n};\nfunction FlightResultsComponent_div_12_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19);\n    i0.ɵɵelement(3, \"img\", 20);\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"div\", 22);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 24);\n    i0.ɵɵelement(10, \"i\", 25);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 26);\n    i0.ɵɵelement(13, \"i\", 27);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 28)(16, \"div\", 29)(17, \"div\", 30);\n    i0.ɵɵelement(18, \"i\", 31);\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20, \"Departure\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 32);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 33);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 34);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 35)(28, \"div\", 30);\n    i0.ɵɵelement(29, \"i\", 36);\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31, \"Total Time\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 37);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 38);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 39);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 40)(39, \"div\", 30);\n    i0.ɵɵelement(40, \"i\", 41);\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42, \"Arrival\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 32);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 33);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"div\", 34);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(49, \"div\", 42)(50, \"div\", 43);\n    i0.ɵɵtext(51, \"TOTAL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 44)(53, \"span\", 45);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"span\", 46);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"i\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 48);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(60, \"div\", 49)(61, \"div\", 50);\n    i0.ɵɵelement(62, \"img\", 51);\n    i0.ɵɵelementStart(63, \"div\", 52);\n    i0.ɵɵelement(64, \"input\", 53);\n    i0.ɵɵelementStart(65, \"label\", 54);\n    i0.ɵɵtext(66, \"Send Quote.. ?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"div\", 55);\n    i0.ɵɵtext(68, \"Note that price may change before confirming\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(69, \"div\", 56)(70, \"button\", 57);\n    i0.ɵɵtext(71, \"AVAILABILITY\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"button\", 57);\n    i0.ɵɵtext(73, \"PRICE BREAKDOWN\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"button\", 57);\n    i0.ɵɵtext(75, \"FARE RULES\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 57);\n    i0.ɵɵtext(77, \"MORE DETAILS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"button\", 58);\n    i0.ɵɵtext(79, \"SELECT NOW\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(80, FlightResultsComponent_div_12_div_2_div_80_Template, 2, 0, \"div\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const flight_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", flight_r7.airlineLogo, i0.ɵɵsanitizeUrl)(\"alt\", flight_r7.airline);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r7.flightNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.aircraftType || \"Economy Standard\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", flight_r7.availableSeats || \"32B\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", flight_r7.baggageAllowance || \"2PC\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.formatDate(flight_r7.departureDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.departureTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.departureAirport);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(flight_r7.duration);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(24, _c0, flight_r7.stops === 0, flight_r7.stops > 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r7.stops === 0 ? \"DIRECT\" : flight_r7.stops + \" STOP\", \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c1, flight_r7.refundable, !flight_r7.refundable));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r7.refundable ? \"REFUNDABLE\" : \"NON REFUNDABLE\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r6.formatDate(flight_r7.arrivalDate));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.arrivalTime);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.arrivalAirport);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(flight_r7.currency);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r7.price);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", flight_r7.price, \" \", flight_r7.currency, \" / ADULT\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"quote-\", i_r8, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"quote-\", i_r8, \"\");\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", flight_r7.restricted);\n  }\n}\nfunction FlightResultsComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15);\n    i0.ɵɵtemplate(2, FlightResultsComponent_div_12_div_2_Template, 81, 30, \"div\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.flights);\n  }\n}\nfunction FlightResultsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"i\", 63);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No flights found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or dates.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_13_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.modifySearch());\n    });\n    i0.ɵɵtext(8, \"Modify Search\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class FlightResultsComponent {\n  constructor(router, route, fb, flightService) {\n    this.router = router;\n    this.route = route;\n    this.fb = fb;\n    this.flightService = flightService;\n    this.searchResults = null;\n    this.isLoading = false;\n    this.error = null;\n    this.flights = [];\n    this.searchSummary = '';\n    this.originalSearchParams = null;\n    // Filter options\n    this.airlineFilters = [{\n      code: 'TK',\n      name: 'Turkish Airlines',\n      logo: 'assets/airlines/turkish-airlines.png',\n      active: false\n    }, {\n      code: 'LH',\n      name: 'Lufthansa',\n      logo: 'assets/airlines/lufthansa.png',\n      active: false\n    }, {\n      code: 'EY',\n      name: 'Etihad',\n      logo: 'assets/airlines/etihad.png',\n      active: false\n    }, {\n      code: 'MS',\n      name: 'EgyptAir',\n      logo: 'assets/airlines/egyptair.png',\n      active: false\n    }, {\n      code: 'AH',\n      name: 'Air Algerie',\n      logo: 'assets/airlines/air-algerie.png',\n      active: false\n    }, {\n      code: 'TU',\n      name: 'Tunisair',\n      logo: 'assets/airlines/tunisair.png',\n      active: false\n    }];\n    // Organized flight results by stops\n    this.flightsByStops = {\n      nonStop: [],\n      oneStop: [],\n      twoOrMoreStops: []\n    };\n    this.searchForm = this.createSearchForm();\n  }\n  ngOnInit() {\n    // Get search results from navigation state or route params\n    const navigation = this.router.getCurrentNavigation();\n    if (navigation?.extras.state?.['results']) {\n      this.searchResults = navigation.extras.state['results'];\n      this.originalSearchParams = navigation.extras.state['searchParams'];\n      this.processSearchResults();\n    } else {\n      // If no results in state, redirect back to search\n      this.router.navigate(['/flights']);\n    }\n    // Initialize form with original search parameters\n    if (this.originalSearchParams) {\n      this.initializeSearchForm();\n    }\n  }\n  createSearchForm() {\n    return this.fb.group({\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]]\n    });\n  }\n  initializeSearchForm() {\n    if (this.originalSearchParams) {\n      this.searchForm.patchValue({\n        from: this.originalSearchParams.from,\n        to: this.originalSearchParams.to,\n        departureDate: this.originalSearchParams.departureDate,\n        adults: this.originalSearchParams.passengers?.adults || 1,\n        children: this.originalSearchParams.passengers?.children || 0,\n        infants: this.originalSearchParams.passengers?.infants || 0\n      });\n    }\n  }\n  processSearchResults() {\n    if (!this.searchResults?.body?.flights) {\n      return;\n    }\n    // Reset flight arrays\n    this.flightsByStops.nonStop = [];\n    this.flightsByStops.oneStop = [];\n    this.flightsByStops.twoOrMoreStops = [];\n    this.flights = [];\n    // Process each flight and categorize by stops\n    this.searchResults.body.flights.forEach(flight => {\n      const flightResult = this.convertToFlightResult(flight);\n      this.flights.push(flightResult);\n      // Get stop count from the first flight item\n      const stopCount = flight.items[0]?.stopCount || 0;\n      if (stopCount === 0) {\n        this.flightsByStops.nonStop.push(flightResult);\n      } else if (stopCount === 1) {\n        this.flightsByStops.oneStop.push(flightResult);\n      } else {\n        this.flightsByStops.twoOrMoreStops.push(flightResult);\n      }\n    });\n    // Sort by price within each category\n    this.flightsByStops.nonStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.oneStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.twoOrMoreStops.sort((a, b) => a.price - b.price);\n    this.flights.sort((a, b) => a.price - b.price);\n    // Update search summary\n    this.searchSummary = this.getSearchSummary();\n  }\n  updateFlightsList() {\n    // This method is called after retrySearch to update the flights list\n    this.processSearchResults();\n  }\n  convertToFlightResult(flight) {\n    const firstItem = flight.items[0];\n    const firstOffer = flight.offers[0];\n    return {\n      airline: firstItem?.airline?.name || 'Unknown',\n      airlineLogo: this.getAirlineLogo(firstItem?.airline?.id || ''),\n      price: firstOffer?.price?.amount || 0,\n      currency: firstOffer?.price?.currency || 'TND',\n      stops: firstItem?.stopCount || 0,\n      duration: this.formatDuration(firstItem?.duration || 0),\n      departureTime: this.extractTimeFromDate(firstItem?.departure?.date || ''),\n      arrivalTime: this.extractTimeFromDate(firstItem?.arrival?.date || ''),\n      departureDate: this.extractDateFromDate(firstItem?.departure?.date || ''),\n      arrivalDate: this.extractDateFromDate(firstItem?.arrival?.date || ''),\n      departureAirport: firstItem?.departure?.airport?.code || firstItem?.departure?.city?.name || '',\n      arrivalAirport: firstItem?.arrival?.airport?.code || firstItem?.arrival?.city?.name || '',\n      flightNumber: firstItem?.flightNo || '',\n      aircraftType: firstItem?.flightClass?.name || 'Economy Standard',\n      availableSeats: firstOffer?.seatInfo?.availableSeatCount?.toString() + 'B' || '32B',\n      baggageAllowance: this.getBaggageAllowance(firstOffer?.baggageInformations || []),\n      refundable: this.isRefundable(firstOffer?.services || []),\n      restricted: false\n    };\n  }\n  getAirlineLogo(airlineCode) {\n    const airline = this.airlineFilters.find(a => a.code === airlineCode);\n    return airline?.logo || 'assets/airlines/default.png';\n  }\n  formatDuration(durationMinutes) {\n    const hours = Math.floor(durationMinutes / 60);\n    const minutes = durationMinutes % 60;\n    return `${hours}h ${minutes}m`;\n  }\n  formatTime(timeString) {\n    // Simple time formatting - in real app, this would parse actual time\n    return timeString || '--:--';\n  }\n  extractTimeFromDate(dateString) {\n    if (!dateString) return '--:--';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch {\n      return '--:--';\n    }\n  }\n  extractDateFromDate(dateString) {\n    if (!dateString) return '';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch {\n      return '';\n    }\n  }\n  getBaggageAllowance(baggageInfo) {\n    if (!baggageInfo || baggageInfo.length === 0) return '2PC';\n    const firstBaggage = baggageInfo[0];\n    if (firstBaggage.piece) {\n      return `${firstBaggage.piece}PC`;\n    }\n    if (firstBaggage.weight) {\n      return `${firstBaggage.weight}KG`;\n    }\n    return '2PC';\n  }\n  isRefundable(services) {\n    if (!services) return false;\n    return services.some(service => service.name?.toLowerCase().includes('refund') || service.id?.toLowerCase().includes('refund'));\n  }\n  /**\n   * Get search summary for display\n   */\n  getSearchSummary() {\n    if (!this.originalSearchParams) return '';\n    const from = this.originalSearchParams.from;\n    const to = this.originalSearchParams.to;\n    const date = new Date(this.originalSearchParams.departureDate).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit'\n    });\n    const adults = this.originalSearchParams.passengers?.adults || 1;\n    return `${from} - ${to}, ${date}, ${adults}Adult(s)`;\n  }\n  /**\n   * Format date for display\n   */\n  formatDate(dateString) {\n    if (!dateString) return '';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch {\n      return dateString;\n    }\n  }\n  /**\n   * Modify search - navigate back to search page\n   */\n  modifySearch() {\n    this.router.navigate(['/flights']);\n  }\n  /**\n   * Retry search with same parameters\n   */\n  retrySearch() {\n    if (this.originalSearchParams) {\n      this.isLoading = true;\n      this.error = null;\n      this.flightService.searchOneWayFlights(this.originalSearchParams).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response;\n            this.processSearchResults();\n            this.updateFlightsList();\n          } else {\n            this.error = 'Search failed. Please try again.';\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.error = 'An error occurred while searching. Please try again.';\n          console.error('Search error:', error);\n        }\n      });\n    }\n  }\n  /**\n   * Toggle airline filter\n   */\n  toggleAirlineFilter(airline) {\n    airline.active = !airline.active;\n    // In a real app, this would filter the results\n  }\n  /**\n   * Perform new search\n   */\n  onNewSearch() {\n    this.router.navigate(['/flights']);\n  }\n  /**\n   * Search again with current form values\n   */\n  onSearchAgain() {\n    if (this.searchForm.valid) {\n      this.isLoading = true;\n      const formValue = this.searchForm.value;\n      const searchForm = {\n        tripType: 'oneWay',\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: 'economy',\n        directFlights: false,\n        refundableFares: false,\n        baggage: 'all',\n        calendar: false\n      };\n      this.flightService.searchOneWayFlights(searchForm).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response;\n            this.originalSearchParams = searchForm;\n            this.processSearchResults();\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n        }\n      });\n    }\n  }\n  /**\n   * Get flights for a specific stop category\n   */\n  getFlightsForStops(stopType) {\n    return this.flightsByStops[stopType];\n  }\n  /**\n   * Get minimum price for a stop category\n   */\n  getMinPriceForStops(stopType) {\n    const flights = this.flightsByStops[stopType];\n    if (flights.length === 0) return null;\n    return Math.min(...flights.map(f => f.price));\n  }\n  /**\n   * Format price display\n   */\n  formatPrice(price, currency = 'TND') {\n    return `${price} ${currency}`;\n  }\n  /**\n   * Get stop label\n   */\n  getStopLabel(stopType) {\n    switch (stopType) {\n      case 'nonStop':\n        return 'Non Stop';\n      case 'oneStop':\n        return '1 Stop';\n      case 'twoOrMoreStops':\n        return '2+ Stops';\n      default:\n        return '';\n    }\n  }\n  /**\n   * Get flight for specific airline and stop type\n   */\n  getFlightForAirlineAndStops(airlineCode, stopType) {\n    const flights = this.flightsByStops[stopType];\n    return flights.find(flight => flight.airline.includes(airlineCode)) || null;\n  }\n  static {\n    this.ɵfac = function FlightResultsComponent_Factory(t) {\n      return new (t || FlightResultsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.FlightService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightResultsComponent,\n      selectors: [[\"app-flight-results\"]],\n      decls: 14,\n      vars: 5,\n      consts: [[1, \"flight-results-container\"], [1, \"search-summary\"], [1, \"search-info\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"results-container\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"loading-container\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"sr-only\"], [1, \"alert\", \"alert-danger\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"results-container\"], [1, \"flights-list\"], [\"class\", \"flight-card paximum-style\", 4, \"ngFor\", \"ngForOf\"], [1, \"flight-card\", \"paximum-style\"], [1, \"flight-header\"], [1, \"airline-section\"], [1, \"airline-logo\", 3, \"src\", \"alt\"], [1, \"flight-info\"], [1, \"flight-number\"], [1, \"aircraft-type\"], [1, \"seat-info\"], [1, \"fas\", \"fa-chair\"], [1, \"baggage-info\"], [1, \"fas\", \"fa-suitcase\"], [1, \"route-section\"], [1, \"departure-info\"], [1, \"section-header\"], [1, \"fas\", \"fa-plane-departure\"], [1, \"date-time\"], [1, \"time\"], [1, \"airport\"], [1, \"flight-duration\"], [1, \"fas\", \"fa-clock\"], [1, \"duration\"], [1, \"stops-badge\", 3, \"ngClass\"], [1, \"refund-status\", 3, \"ngClass\"], [1, \"arrival-info\"], [1, \"fas\", \"fa-plane-arrival\"], [1, \"price-section\"], [1, \"total-label\"], [1, \"price-display\"], [1, \"currency\"], [1, \"amount\"], [1, \"fas\", \"fa-money-bill-wave\", \"price-icon\"], [1, \"price-per-adult\"], [1, \"action-buttons\"], [1, \"left-actions\"], [\"src\", \"assets/images/airline-logo-small.png\", \"alt\", \"Airline\", 1, \"action-airline-logo\"], [1, \"quote-section\"], [\"type\", \"checkbox\", 1, \"quote-checkbox\", 3, \"id\"], [3, \"for\"], [1, \"quote-note\"], [1, \"right-actions\"], [1, \"btn\", \"btn-outline-secondary\"], [1, \"btn\", \"btn-success\", \"select-btn\"], [\"class\", \"restriction-badge\", 4, \"ngIf\"], [1, \"restriction-badge\"], [1, \"no-results\"], [1, \"text-center\"], [1, \"fas\", \"fa-plane-slash\", \"fa-3x\", \"text-muted\", \"mb-3\"]],\n      template: function FlightResultsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Flight Search Results\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_7_listener() {\n            return ctx.modifySearch();\n          });\n          i0.ɵɵelement(8, \"i\", 4);\n          i0.ɵɵtext(9, \" Modify Search \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(10, FlightResultsComponent_div_10_Template, 6, 0, \"div\", 5);\n          i0.ɵɵtemplate(11, FlightResultsComponent_div_11_Template, 7, 1, \"div\", 6);\n          i0.ɵɵtemplate(12, FlightResultsComponent_div_12_Template, 3, 1, \"div\", 7);\n          i0.ɵɵtemplate(13, FlightResultsComponent_div_13_Template, 9, 0, \"div\", 8);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.searchSummary);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.flights.length > 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.error && ctx.flights.length === 0);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf],\n      styles: [\"\\n\\n.flight-results-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  background-color: #f8f9fa;\\n}\\n\\n\\n\\n.search-summary-header[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 20px 30px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  color: #333;\\n  margin: 0;\\n}\\n\\n\\n\\n.search-modification-panel[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 25px 30px;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.search-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  align-items: end;\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #666;\\n  margin-bottom: 8px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px;\\n  border: 2px solid #e0e0e0;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  transition: border-color 0.3s ease;\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #4a90e2;\\n}\\n\\n.date-input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.date-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 60px;\\n}\\n\\n.date-controls[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 10px;\\n  display: flex;\\n  gap: 5px;\\n}\\n\\n.date-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 1px solid #ddd;\\n  background: white;\\n  border-radius: 4px;\\n  font-size: 14px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.date-btn[_ngcontent-%COMP%]:hover {\\n  background: #f0f0f0;\\n}\\n\\n\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  align-items: center;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 12px 24px;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-transform: uppercase;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #5a6268;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #4a90e2;\\n  color: white;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #357abd;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n\\n.email-btn[_ngcontent-%COMP%] {\\n  background: #28a745;\\n}\\n\\n.email-btn[_ngcontent-%COMP%]:hover {\\n  background: #218838;\\n}\\n\\n\\n\\n.results-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.airline-filters[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 20px 30px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.filter-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  align-items: center;\\n}\\n\\n.airline-tab[_ngcontent-%COMP%] {\\n  padding: 10px 15px;\\n  border: 2px solid #e0e0e0;\\n  background: white;\\n  border-radius: 6px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.airline-tab[_ngcontent-%COMP%]:hover {\\n  border-color: #4a90e2;\\n}\\n\\n.airline-tab.active[_ngcontent-%COMP%] {\\n  border-color: #4a90e2;\\n  background: #e3f2fd;\\n}\\n\\n.airline-logo[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n\\n.airline-logo-placeholder[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 30px;\\n  background: #f0f0f0;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 1px solid #ddd;\\n}\\n\\n.airline-code[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  font-weight: 600;\\n  color: #666;\\n}\\n\\n\\n\\n.flight-results-grid[_ngcontent-%COMP%], .sample-price-grid[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 150px repeat(6, 1fr);\\n  gap: 15px;\\n  margin-bottom: 20px;\\n  padding-bottom: 15px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n\\n.stop-category-header[_ngcontent-%COMP%] {\\n  \\n\\n}\\n\\n.airline-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 10px;\\n  background: #f8f9fa;\\n  border-radius: 6px;\\n}\\n\\n.airline-logo-small[_ngcontent-%COMP%] {\\n  height: 25px;\\n  width: auto;\\n  object-fit: contain;\\n}\\n\\n.airline-logo-placeholder-small[_ngcontent-%COMP%] {\\n  width: 35px;\\n  height: 25px;\\n  background: #f8f9fa;\\n  border-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.airline-code-small[_ngcontent-%COMP%] {\\n  font-size: 9px;\\n  font-weight: 600;\\n  color: #666;\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 150px repeat(6, 1fr);\\n  gap: 15px;\\n  margin-bottom: 15px;\\n  align-items: center;\\n}\\n\\n.stop-category[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px 0;\\n}\\n\\n.stop-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.price-cell[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 15px 10px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 6px;\\n  min-height: 60px;\\n  background: white;\\n  transition: all 0.3s ease;\\n}\\n\\n.price-cell[_ngcontent-%COMP%]:hover {\\n  border-color: #4a90e2;\\n  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);\\n  cursor: pointer;\\n}\\n\\n.price-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.price[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 18px;\\n  font-weight: 700;\\n  color: #333;\\n  line-height: 1.2;\\n}\\n\\n.currency[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 12px;\\n  color: #666;\\n  margin-top: 2px;\\n}\\n\\n.no-flight[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 60px 20px;\\n  color: #666;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #ddd;\\n  margin-bottom: 20px;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-bottom: 10px;\\n  color: #333;\\n}\\n\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 120px repeat(6, 1fr);\\n    gap: 10px;\\n  }\\n\\n  .airline-logo-small[_ngcontent-%COMP%] {\\n    height: 20px;\\n  }\\n\\n  .price[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n}\\n\\n@media (max-width: 992px) {\\n  .search-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n\\n  .action-buttons[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 10px;\\n  }\\n\\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 100px repeat(3, 1fr);\\n  }\\n\\n  \\n\\n  .grid-header[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+5), .grid-row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+5) {\\n    display: none;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .flight-results-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n\\n  .search-modification-panel[_ngcontent-%COMP%], .results-section[_ngcontent-%COMP%] {\\n    padding: 20px 15px;\\n  }\\n\\n  .search-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n\\n  .filter-tabs[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    gap: 10px;\\n  }\\n\\n  .airline-logo[_ngcontent-%COMP%] {\\n    height: 25px;\\n  }\\n\\n  .grid-header[_ngcontent-%COMP%], .grid-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 80px repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n\\n  \\n\\n  .grid-header[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+4), .grid-row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(n+4) {\\n    display: none;\\n  }\\n\\n  .price-cell[_ngcontent-%COMP%] {\\n    padding: 10px 5px;\\n    min-height: 50px;\\n  }\\n\\n  .price[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n\\n  .currency[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n}\\n\\n\\n\\n.btn[_ngcontent-%COMP%]   .fa-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n\\n\\n.price-cell[_ngcontent-%COMP%]:hover   .price[_ngcontent-%COMP%] {\\n  color: #4a90e2;\\n}\\n\\n.price-cell[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);\\n}\\n\\n.btn[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "FlightResultsComponent_div_11_Template_button_click_5_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "retrySearch", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "error", "ɵɵelement", "ɵɵtemplate", "FlightResultsComponent_div_12_div_2_div_80_Template", "ɵɵproperty", "flight_r7", "airlineLogo", "ɵɵsanitizeUrl", "airline", "flightNumber", "aircraftType", "ɵɵtextInterpolate1", "availableSeats", "baggageAllowance", "ctx_r6", "formatDate", "departureDate", "departureTime", "departureAirport", "duration", "ɵɵpureFunction2", "_c0", "stops", "_c1", "refundable", "arrivalDate", "arrivalTime", "arrivalAirport", "currency", "price", "ɵɵtextInterpolate2", "ɵɵpropertyInterpolate1", "i_r8", "restricted", "FlightResultsComponent_div_12_div_2_Template", "ctx_r2", "flights", "FlightResultsComponent_div_13_Template_button_click_7_listener", "_r11", "ctx_r10", "modifySearch", "FlightResultsComponent", "constructor", "router", "route", "fb", "flightService", "searchResults", "isLoading", "searchSummary", "originalSearchParams", "airlineFilters", "code", "name", "logo", "active", "flightsByStops", "nonStop", "oneStop", "twoOrMoreStops", "searchForm", "createSearchForm", "ngOnInit", "navigation", "getCurrentNavigation", "extras", "state", "processSearchResults", "navigate", "initializeSearchForm", "group", "from", "required", "to", "adults", "min", "children", "infants", "patchValue", "passengers", "body", "for<PERSON>ach", "flight", "flightResult", "convertToFlightResult", "push", "stopCount", "items", "sort", "a", "b", "getSearchSummary", "updateFlightsList", "firstItem", "firstOffer", "offers", "getAirlineLogo", "id", "amount", "formatDuration", "extractTimeFromDate", "departure", "date", "arrival", "extractDateFromDate", "airport", "city", "flightNo", "flightClass", "seatInfo", "availableSeatCount", "toString", "getBaggageAllowance", "baggageInformations", "isRefundable", "services", "airlineCode", "find", "durationMinutes", "hours", "Math", "floor", "minutes", "formatTime", "timeString", "dateString", "Date", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "day", "month", "year", "baggageInfo", "length", "firstBaggage", "piece", "weight", "some", "service", "toLowerCase", "includes", "searchOneWayFlights", "subscribe", "next", "response", "header", "success", "console", "toggleAirlineFilter", "onNewSearch", "onSearchAgain", "valid", "formValue", "value", "tripType", "class", "directFlights", "refundableFares", "baggage", "calendar", "getFlightsForStops", "stopType", "getMinPriceForStops", "map", "f", "formatPrice", "getStopLabel", "getFlightForAirlineAndStops", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "FormBuilder", "i3", "FlightService", "selectors", "decls", "vars", "consts", "template", "FlightResultsComponent_Template", "rf", "ctx", "FlightResultsComponent_Template_button_click_7_listener", "FlightResultsComponent_div_10_Template", "FlightResultsComponent_div_11_Template", "FlightResultsComponent_div_12_Template", "FlightResultsComponent_div_13_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight-results\\flight-results.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\flight-results\\flight-results.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { FlightService } from '../../services/flight.service';\nimport { FlightSearchResponse, Flight, FlightSearchForm } from '../../models/flight.models';\nimport { Observable } from 'rxjs';\n\ninterface AirlineFilter {\n  code: string;\n  name: string;\n  logo: string;\n  active: boolean;\n}\n\ninterface FlightResult {\n  airline: string;\n  airlineLogo: string;\n  price: number;\n  currency: string;\n  stops: number;\n  duration: string;\n  departureTime: string;\n  arrivalTime: string;\n  departureDate: string;\n  arrivalDate: string;\n  departureAirport: string;\n  arrivalAirport: string;\n  flightNumber: string;\n  aircraftType?: string;\n  availableSeats?: string;\n  baggageAllowance?: string;\n  refundable: boolean;\n  restricted?: boolean;\n}\n\n@Component({\n  selector: 'app-flight-results',\n  templateUrl: './flight-results.component.html',\n  styleUrls: ['./flight-results.component.css']\n})\nexport class FlightResultsComponent implements OnInit {\n  searchResults: FlightSearchResponse | null = null;\n  searchForm: FormGroup;\n  isLoading = false;\n  error: string | null = null;\n  flights: FlightResult[] = [];\n  searchSummary = '';\n  originalSearchParams: any = null;\n\n  // Filter options\n  airlineFilters: AirlineFilter[] = [\n    { code: 'TK', name: 'Turkish Airlines', logo: 'assets/airlines/turkish-airlines.png', active: false },\n    { code: 'LH', name: 'Lufthansa', logo: 'assets/airlines/lufthansa.png', active: false },\n    { code: 'EY', name: 'Etihad', logo: 'assets/airlines/etihad.png', active: false },\n    { code: 'MS', name: 'EgyptAir', logo: 'assets/airlines/egyptair.png', active: false },\n    { code: 'AH', name: 'Air Algerie', logo: 'assets/airlines/air-algerie.png', active: false },\n    { code: 'TU', name: 'Tunisair', logo: 'assets/airlines/tunisair.png', active: false }\n  ];\n\n  // Organized flight results by stops\n  flightsByStops = {\n    nonStop: [] as FlightResult[],\n    oneStop: [] as FlightResult[],\n    twoOrMoreStops: [] as FlightResult[]\n  };\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private flightService: FlightService\n  ) {\n    this.searchForm = this.createSearchForm();\n  }\n\n  ngOnInit(): void {\n    // Get search results from navigation state or route params\n    const navigation = this.router.getCurrentNavigation();\n    if (navigation?.extras.state?.['results']) {\n      this.searchResults = navigation.extras.state['results'];\n      this.originalSearchParams = navigation.extras.state['searchParams'];\n      this.processSearchResults();\n    } else {\n      // If no results in state, redirect back to search\n      this.router.navigate(['/flights']);\n    }\n\n    // Initialize form with original search parameters\n    if (this.originalSearchParams) {\n      this.initializeSearchForm();\n    }\n  }\n\n  private createSearchForm(): FormGroup {\n    return this.fb.group({\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]]\n    });\n  }\n\n  private initializeSearchForm(): void {\n    if (this.originalSearchParams) {\n      this.searchForm.patchValue({\n        from: this.originalSearchParams.from,\n        to: this.originalSearchParams.to,\n        departureDate: this.originalSearchParams.departureDate,\n        adults: this.originalSearchParams.passengers?.adults || 1,\n        children: this.originalSearchParams.passengers?.children || 0,\n        infants: this.originalSearchParams.passengers?.infants || 0\n      });\n    }\n  }\n\n  private processSearchResults(): void {\n    if (!this.searchResults?.body?.flights) {\n      return;\n    }\n\n    // Reset flight arrays\n    this.flightsByStops.nonStop = [];\n    this.flightsByStops.oneStop = [];\n    this.flightsByStops.twoOrMoreStops = [];\n    this.flights = [];\n\n    // Process each flight and categorize by stops\n    this.searchResults.body.flights.forEach(flight => {\n      const flightResult = this.convertToFlightResult(flight);\n      this.flights.push(flightResult);\n\n      // Get stop count from the first flight item\n      const stopCount = flight.items[0]?.stopCount || 0;\n\n      if (stopCount === 0) {\n        this.flightsByStops.nonStop.push(flightResult);\n      } else if (stopCount === 1) {\n        this.flightsByStops.oneStop.push(flightResult);\n      } else {\n        this.flightsByStops.twoOrMoreStops.push(flightResult);\n      }\n    });\n\n    // Sort by price within each category\n    this.flightsByStops.nonStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.oneStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.twoOrMoreStops.sort((a, b) => a.price - b.price);\n    this.flights.sort((a, b) => a.price - b.price);\n\n    // Update search summary\n    this.searchSummary = this.getSearchSummary();\n  }\n\n  private updateFlightsList(): void {\n    // This method is called after retrySearch to update the flights list\n    this.processSearchResults();\n  }\n\n  private convertToFlightResult(flight: Flight): FlightResult {\n    const firstItem = flight.items[0];\n    const firstOffer = flight.offers[0];\n\n    return {\n      airline: firstItem?.airline?.name || 'Unknown',\n      airlineLogo: this.getAirlineLogo(firstItem?.airline?.id || ''),\n      price: firstOffer?.price?.amount || 0,\n      currency: firstOffer?.price?.currency || 'TND',\n      stops: firstItem?.stopCount || 0,\n      duration: this.formatDuration(firstItem?.duration || 0),\n      departureTime: this.extractTimeFromDate(firstItem?.departure?.date || ''),\n      arrivalTime: this.extractTimeFromDate(firstItem?.arrival?.date || ''),\n      departureDate: this.extractDateFromDate(firstItem?.departure?.date || ''),\n      arrivalDate: this.extractDateFromDate(firstItem?.arrival?.date || ''),\n      departureAirport: firstItem?.departure?.airport?.code || firstItem?.departure?.city?.name || '',\n      arrivalAirport: firstItem?.arrival?.airport?.code || firstItem?.arrival?.city?.name || '',\n      flightNumber: firstItem?.flightNo || '',\n      aircraftType: firstItem?.flightClass?.name || 'Economy Standard',\n      availableSeats: firstOffer?.seatInfo?.availableSeatCount?.toString() + 'B' || '32B',\n      baggageAllowance: this.getBaggageAllowance(firstOffer?.baggageInformations || []),\n      refundable: this.isRefundable(firstOffer?.services || []),\n      restricted: false\n    };\n  }\n\n  private getAirlineLogo(airlineCode: string): string {\n    const airline = this.airlineFilters.find(a => a.code === airlineCode);\n    return airline?.logo || 'assets/airlines/default.png';\n  }\n\n  private formatDuration(durationMinutes: number): string {\n    const hours = Math.floor(durationMinutes / 60);\n    const minutes = durationMinutes % 60;\n    return `${hours}h ${minutes}m`;\n  }\n\n  private formatTime(timeString: string): string {\n    // Simple time formatting - in real app, this would parse actual time\n    return timeString || '--:--';\n  }\n\n  private extractTimeFromDate(dateString: string): string {\n    if (!dateString) return '--:--';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch {\n      return '--:--';\n    }\n  }\n\n  private extractDateFromDate(dateString: string): string {\n    if (!dateString) return '';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch {\n      return '';\n    }\n  }\n\n  private getBaggageAllowance(baggageInfo: any[]): string {\n    if (!baggageInfo || baggageInfo.length === 0) return '2PC';\n    const firstBaggage = baggageInfo[0];\n    if (firstBaggage.piece) {\n      return `${firstBaggage.piece}PC`;\n    }\n    if (firstBaggage.weight) {\n      return `${firstBaggage.weight}KG`;\n    }\n    return '2PC';\n  }\n\n  private isRefundable(services: any[]): boolean {\n    if (!services) return false;\n    return services.some(service =>\n      service.name?.toLowerCase().includes('refund') ||\n      service.id?.toLowerCase().includes('refund')\n    );\n  }\n\n  /**\n   * Get search summary for display\n   */\n  getSearchSummary(): string {\n    if (!this.originalSearchParams) return '';\n\n    const from = this.originalSearchParams.from;\n    const to = this.originalSearchParams.to;\n    const date = new Date(this.originalSearchParams.departureDate).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit'\n    });\n    const adults = this.originalSearchParams.passengers?.adults || 1;\n\n    return `${from} - ${to}, ${date}, ${adults}Adult(s)`;\n  }\n\n  /**\n   * Format date for display\n   */\n  formatDate(dateString: string): string {\n    if (!dateString) return '';\n    try {\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric'\n      });\n    } catch {\n      return dateString;\n    }\n  }\n\n  /**\n   * Modify search - navigate back to search page\n   */\n  modifySearch(): void {\n    this.router.navigate(['/flights']);\n  }\n\n  /**\n   * Retry search with same parameters\n   */\n  retrySearch(): void {\n    if (this.originalSearchParams) {\n      this.isLoading = true;\n      this.error = null;\n\n      this.flightService.searchOneWayFlights(this.originalSearchParams).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response;\n            this.processSearchResults();\n            this.updateFlightsList();\n          } else {\n            this.error = 'Search failed. Please try again.';\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.error = 'An error occurred while searching. Please try again.';\n          console.error('Search error:', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Toggle airline filter\n   */\n  toggleAirlineFilter(airline: AirlineFilter): void {\n    airline.active = !airline.active;\n    // In a real app, this would filter the results\n  }\n\n  /**\n   * Perform new search\n   */\n  onNewSearch(): void {\n    this.router.navigate(['/flights']);\n  }\n\n  /**\n   * Search again with current form values\n   */\n  onSearchAgain(): void {\n    if (this.searchForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.searchForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: 'oneWay',\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: 'economy',\n        directFlights: false,\n        refundableFares: false,\n        baggage: 'all',\n        calendar: false\n      };\n\n      this.flightService.searchOneWayFlights(searchForm).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response;\n            this.originalSearchParams = searchForm;\n            this.processSearchResults();\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get flights for a specific stop category\n   */\n  getFlightsForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult[] {\n    return this.flightsByStops[stopType];\n  }\n\n  /**\n   * Get minimum price for a stop category\n   */\n  getMinPriceForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): number | null {\n    const flights = this.flightsByStops[stopType];\n    if (flights.length === 0) return null;\n    return Math.min(...flights.map(f => f.price));\n  }\n\n  /**\n   * Format price display\n   */\n  formatPrice(price: number, currency: string = 'TND'): string {\n    return `${price} ${currency}`;\n  }\n\n  /**\n   * Get stop label\n   */\n  getStopLabel(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): string {\n    switch (stopType) {\n      case 'nonStop': return 'Non Stop';\n      case 'oneStop': return '1 Stop';\n      case 'twoOrMoreStops': return '2+ Stops';\n      default: return '';\n    }\n  }\n\n  /**\n   * Get flight for specific airline and stop type\n   */\n  getFlightForAirlineAndStops(airlineCode: string, stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult | null {\n    const flights = this.flightsByStops[stopType];\n    return flights.find(flight => flight.airline.includes(airlineCode)) || null;\n  }\n}\n", "<!-- Flight Results Component with Paximum-style design -->\n<div class=\"flight-results-container\">\n  <!-- Search Summary -->\n  <div class=\"search-summary\">\n    <div class=\"search-info\">\n      <h2>Flight Search Results</h2>\n      <p>{{ searchSummary }}</p>\n    </div>\n\n    <!-- Modify Search Button -->\n    <button class=\"btn btn-outline-primary\" (click)=\"modifySearch()\">\n      <i class=\"fas fa-edit\"></i> Modify Search\n    </button>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"isLoading\" class=\"loading-container\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"sr-only\">Loading...</span>\n    </div>\n    <p>Searching for flights...</p>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error\" class=\"alert alert-danger\">\n    <h4>Error</h4>\n    <p>{{ error }}</p>\n    <button class=\"btn btn-primary\" (click)=\"retrySearch()\">Try Again</button>\n  </div>\n\n  <!-- Results -->\n  <div *ngIf=\"!isLoading && !error && flights.length > 0\" class=\"results-container\">\n    <!-- Flight Results with Paximum-style design -->\n    <div class=\"flights-list\">\n      <div *ngFor=\"let flight of flights; let i = index\" class=\"flight-card paximum-style\">\n        <!-- Flight Header with airline info -->\n        <div class=\"flight-header\">\n          <div class=\"airline-section\">\n            <img [src]=\"flight.airlineLogo\" [alt]=\"flight.airline\" class=\"airline-logo\">\n            <div class=\"flight-info\">\n              <div class=\"flight-number\">{{ flight.flightNumber }}</div>\n              <div class=\"aircraft-type\">{{ flight.aircraftType || 'Economy Standard' }}</div>\n              <div class=\"seat-info\">\n                <i class=\"fas fa-chair\"></i> {{ flight.availableSeats || '32B' }}\n              </div>\n              <div class=\"baggage-info\">\n                <i class=\"fas fa-suitcase\"></i> {{ flight.baggageAllowance || '2PC' }}\n              </div>\n            </div>\n          </div>\n\n          <!-- Flight Route Information -->\n          <div class=\"route-section\">\n            <div class=\"departure-info\">\n              <div class=\"section-header\">\n                <i class=\"fas fa-plane-departure\"></i>\n                <span>Departure</span>\n              </div>\n              <div class=\"date-time\">{{ formatDate(flight.departureDate) }}</div>\n              <div class=\"time\">{{ flight.departureTime }}</div>\n              <div class=\"airport\">{{ flight.departureAirport }}</div>\n            </div>\n\n            <div class=\"flight-duration\">\n              <div class=\"section-header\">\n                <i class=\"fas fa-clock\"></i>\n                <span>Total Time</span>\n              </div>\n              <div class=\"duration\">{{ flight.duration }}</div>\n              <div class=\"stops-badge\" [ngClass]=\"{'direct': flight.stops === 0, 'stops': flight.stops > 0}\">\n                {{ flight.stops === 0 ? 'DIRECT' : flight.stops + ' STOP' }}\n              </div>\n              <div class=\"refund-status\" [ngClass]=\"{'refundable': flight.refundable, 'non-refundable': !flight.refundable}\">\n                {{ flight.refundable ? 'REFUNDABLE' : 'NON REFUNDABLE' }}\n              </div>\n            </div>\n\n            <div class=\"arrival-info\">\n              <div class=\"section-header\">\n                <i class=\"fas fa-plane-arrival\"></i>\n                <span>Arrival</span>\n              </div>\n              <div class=\"date-time\">{{ formatDate(flight.arrivalDate) }}</div>\n              <div class=\"time\">{{ flight.arrivalTime }}</div>\n              <div class=\"airport\">{{ flight.arrivalAirport }}</div>\n            </div>\n          </div>\n\n          <!-- Price Section -->\n          <div class=\"price-section\">\n            <div class=\"total-label\">TOTAL</div>\n            <div class=\"price-display\">\n              <span class=\"currency\">{{ flight.currency }}</span>\n              <span class=\"amount\">{{ flight.price }}</span>\n              <i class=\"fas fa-money-bill-wave price-icon\"></i>\n            </div>\n            <div class=\"price-per-adult\">{{ flight.price }} {{ flight.currency }} / ADULT</div>\n          </div>\n        </div>\n\n        <!-- Action Buttons -->\n        <div class=\"action-buttons\">\n          <div class=\"left-actions\">\n            <img src=\"assets/images/airline-logo-small.png\" alt=\"Airline\" class=\"action-airline-logo\">\n            <div class=\"quote-section\">\n              <input type=\"checkbox\" id=\"quote-{{ i }}\" class=\"quote-checkbox\">\n              <label for=\"quote-{{ i }}\">Send Quote.. ?</label>\n              <div class=\"quote-note\">Note that price may change before confirming</div>\n            </div>\n          </div>\n\n          <div class=\"right-actions\">\n            <button class=\"btn btn-outline-secondary\">AVAILABILITY</button>\n            <button class=\"btn btn-outline-secondary\">PRICE BREAKDOWN</button>\n            <button class=\"btn btn-outline-secondary\">FARE RULES</button>\n            <button class=\"btn btn-outline-secondary\">MORE DETAILS</button>\n            <button class=\"btn btn-success select-btn\">SELECT NOW</button>\n          </div>\n\n          <div class=\"restriction-badge\" *ngIf=\"flight.restricted\">\n            RESTRICTED\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- No Results -->\n  <div *ngIf=\"!isLoading && !error && flights.length === 0\" class=\"no-results\">\n    <div class=\"text-center\">\n      <i class=\"fas fa-plane-slash fa-3x text-muted mb-3\"></i>\n      <h4>No flights found</h4>\n      <p>Try adjusting your search criteria or dates.</p>\n      <button class=\"btn btn-primary\" (click)=\"modifySearch()\">Modify Search</button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;ICcjEC,EAAA,CAAAC,cAAA,aAAiD;IAEvBD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAIjCH,EAAA,CAAAC,cAAA,cAA8C;IACxCD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAwD;IAAxBD,EAAA,CAAAI,UAAA,mBAAAC,+DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAACX,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADvEH,EAAA,CAAAY,SAAA,GAAW;IAAXZ,EAAA,CAAAa,iBAAA,CAAAC,MAAA,CAAAC,KAAA,CAAW;;;;;IA6FRf,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;;;;IAvFVH,EAAA,CAAAC,cAAA,cAAqF;IAI/ED,EAAA,CAAAgB,SAAA,cAA4E;IAC5EhB,EAAA,CAAAC,cAAA,cAAyB;IACID,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChFH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAgB,SAAA,aAA4B;IAAChB,EAAA,CAAAE,MAAA,IAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAgB,SAAA,aAA+B;IAAChB,EAAA,CAAAE,MAAA,IAClC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAA2B;IAGrBD,EAAA,CAAAgB,SAAA,aAAsC;IACtChB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAExBH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG1DH,EAAA,CAAAC,cAAA,eAA6B;IAEzBD,EAAA,CAAAgB,SAAA,aAA4B;IAC5BhB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzBH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,eAA+F;IAC7FD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+G;IAC7GD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAA0B;IAEtBD,EAAA,CAAAgB,SAAA,aAAoC;IACpChB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEtBH,EAAA,CAAAC,cAAA,eAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAK1DH,EAAA,CAAAC,cAAA,eAA2B;IACAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpCH,EAAA,CAAAC,cAAA,eAA2B;IACFD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAgB,SAAA,aAAiD;IACnDhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAKvFH,EAAA,CAAAC,cAAA,eAA4B;IAExBD,EAAA,CAAAgB,SAAA,eAA0F;IAC1FhB,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAgB,SAAA,iBAAiE;IACjEhB,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,oDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI9EH,EAAA,CAAAC,cAAA,eAA2B;IACiBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/DH,EAAA,CAAAC,cAAA,kBAA0C;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClEH,EAAA,CAAAC,cAAA,kBAA0C;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7DH,EAAA,CAAAC,cAAA,kBAA0C;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/DH,EAAA,CAAAC,cAAA,kBAA2C;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAGhEH,EAAA,CAAAiB,UAAA,KAAAC,mDAAA,kBAEM;IACRlB,EAAA,CAAAG,YAAA,EAAM;;;;;;IApFGH,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAmB,UAAA,QAAAC,SAAA,CAAAC,WAAA,EAAArB,EAAA,CAAAsB,aAAA,CAA0B,QAAAF,SAAA,CAAAG,OAAA;IAEFvB,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAI,YAAA,CAAyB;IACzBxB,EAAA,CAAAY,SAAA,GAA+C;IAA/CZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAK,YAAA,uBAA+C;IAE3CzB,EAAA,CAAAY,SAAA,GAC/B;IAD+BZ,EAAA,CAAA0B,kBAAA,MAAAN,SAAA,CAAAO,cAAA,eAC/B;IAEkC3B,EAAA,CAAAY,SAAA,GAClC;IADkCZ,EAAA,CAAA0B,kBAAA,MAAAN,SAAA,CAAAQ,gBAAA,eAClC;IAWuB5B,EAAA,CAAAY,SAAA,GAAsC;IAAtCZ,EAAA,CAAAa,iBAAA,CAAAgB,MAAA,CAAAC,UAAA,CAAAV,SAAA,CAAAW,aAAA,EAAsC;IAC3C/B,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAY,aAAA,CAA0B;IACvBhC,EAAA,CAAAY,SAAA,GAA6B;IAA7BZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAa,gBAAA,CAA6B;IAQ5BjC,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAc,QAAA,CAAqB;IAClBlC,EAAA,CAAAY,SAAA,GAAqE;IAArEZ,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAAAhB,SAAA,CAAAiB,KAAA,QAAAjB,SAAA,CAAAiB,KAAA,MAAqE;IAC5FrC,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA0B,kBAAA,MAAAN,SAAA,CAAAiB,KAAA,oBAAAjB,SAAA,CAAAiB,KAAA,gBACF;IAC2BrC,EAAA,CAAAY,SAAA,GAAmF;IAAnFZ,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAmC,eAAA,KAAAG,GAAA,EAAAlB,SAAA,CAAAmB,UAAA,GAAAnB,SAAA,CAAAmB,UAAA,EAAmF;IAC5GvC,EAAA,CAAAY,SAAA,GACF;IADEZ,EAAA,CAAA0B,kBAAA,MAAAN,SAAA,CAAAmB,UAAA,wCACF;IAQuBvC,EAAA,CAAAY,SAAA,GAAoC;IAApCZ,EAAA,CAAAa,iBAAA,CAAAgB,MAAA,CAAAC,UAAA,CAAAV,SAAA,CAAAoB,WAAA,EAAoC;IACzCxC,EAAA,CAAAY,SAAA,GAAwB;IAAxBZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAqB,WAAA,CAAwB;IACrBzC,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAsB,cAAA,CAA2B;IAQzB1C,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAuB,QAAA,CAAqB;IACvB3C,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,iBAAA,CAAAO,SAAA,CAAAwB,KAAA,CAAkB;IAGZ5C,EAAA,CAAAY,SAAA,GAAgD;IAAhDZ,EAAA,CAAA6C,kBAAA,KAAAzB,SAAA,CAAAwB,KAAA,OAAAxB,SAAA,CAAAuB,QAAA,aAAgD;IASpD3C,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAA8C,sBAAA,iBAAAC,IAAA,KAAkB;IAClC/C,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAA8C,sBAAA,kBAAAC,IAAA,KAAmB;IAaE/C,EAAA,CAAAY,SAAA,IAAuB;IAAvBZ,EAAA,CAAAmB,UAAA,SAAAC,SAAA,CAAA4B,UAAA,CAAuB;;;;;IAxF/DhD,EAAA,CAAAC,cAAA,cAAkF;IAG9ED,EAAA,CAAAiB,UAAA,IAAAgC,4CAAA,oBAyFM;IACRjD,EAAA,CAAAG,YAAA,EAAM;;;;IA1FoBH,EAAA,CAAAY,SAAA,GAAY;IAAZZ,EAAA,CAAAmB,UAAA,YAAA+B,MAAA,CAAAC,OAAA,CAAY;;;;;;IA8FxCnD,EAAA,CAAAC,cAAA,cAA6E;IAEzED,EAAA,CAAAgB,SAAA,YAAwD;IACxDhB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mDAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnDH,EAAA,CAAAC,cAAA,iBAAyD;IAAzBD,EAAA,CAAAI,UAAA,mBAAAgD,+DAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA+C,IAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAA4C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAACvD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;AD7FrF,OAAM,MAAOqD,sBAAsB;EA0BjCC,YACUC,MAAc,EACdC,KAAqB,EACrBC,EAAe,EACfC,aAA4B;IAH5B,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IA7BvB,KAAAC,aAAa,GAAgC,IAAI;IAEjD,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAhD,KAAK,GAAkB,IAAI;IAC3B,KAAAoC,OAAO,GAAmB,EAAE;IAC5B,KAAAa,aAAa,GAAG,EAAE;IAClB,KAAAC,oBAAoB,GAAQ,IAAI;IAEhC;IACA,KAAAC,cAAc,GAAoB,CAChC;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,kBAAkB;MAAEC,IAAI,EAAE,sCAAsC;MAAEC,MAAM,EAAE;IAAK,CAAE,EACrG;MAAEH,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,+BAA+B;MAAEC,MAAM,EAAE;IAAK,CAAE,EACvF;MAAEH,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,4BAA4B;MAAEC,MAAM,EAAE;IAAK,CAAE,EACjF;MAAEH,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,8BAA8B;MAAEC,MAAM,EAAE;IAAK,CAAE,EACrF;MAAEH,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE,iCAAiC;MAAEC,MAAM,EAAE;IAAK,CAAE,EAC3F;MAAEH,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,8BAA8B;MAAEC,MAAM,EAAE;IAAK,CAAE,CACtF;IAED;IACA,KAAAC,cAAc,GAAG;MACfC,OAAO,EAAE,EAAoB;MAC7BC,OAAO,EAAE,EAAoB;MAC7BC,cAAc,EAAE;KACjB;IAQC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,gBAAgB,EAAE;EAC3C;EAEAC,QAAQA,CAAA;IACN;IACA,MAAMC,UAAU,GAAG,IAAI,CAACpB,MAAM,CAACqB,oBAAoB,EAAE;IACrD,IAAID,UAAU,EAAEE,MAAM,CAACC,KAAK,GAAG,SAAS,CAAC,EAAE;MACzC,IAAI,CAACnB,aAAa,GAAGgB,UAAU,CAACE,MAAM,CAACC,KAAK,CAAC,SAAS,CAAC;MACvD,IAAI,CAAChB,oBAAoB,GAAGa,UAAU,CAACE,MAAM,CAACC,KAAK,CAAC,cAAc,CAAC;MACnE,IAAI,CAACC,oBAAoB,EAAE;KAC5B,MAAM;MACL;MACA,IAAI,CAACxB,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;IAGpC;IACA,IAAI,IAAI,CAAClB,oBAAoB,EAAE;MAC7B,IAAI,CAACmB,oBAAoB,EAAE;;EAE/B;EAEQR,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAAChB,EAAE,CAACyB,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC,EAAE,EAAEvF,UAAU,CAACwF,QAAQ,CAAC;MAC/BC,EAAE,EAAE,CAAC,EAAE,EAAEzF,UAAU,CAACwF,QAAQ,CAAC;MAC7BxD,aAAa,EAAE,CAAC,EAAE,EAAEhC,UAAU,CAACwF,QAAQ,CAAC;MACxCE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC1F,UAAU,CAACwF,QAAQ,EAAExF,UAAU,CAAC2F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC5F,UAAU,CAAC2F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC7F,UAAU,CAAC2F,GAAG,CAAC,CAAC,CAAC,CAAC;KACjC,CAAC;EACJ;EAEQN,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACnB,oBAAoB,EAAE;MAC7B,IAAI,CAACU,UAAU,CAACkB,UAAU,CAAC;QACzBP,IAAI,EAAE,IAAI,CAACrB,oBAAoB,CAACqB,IAAI;QACpCE,EAAE,EAAE,IAAI,CAACvB,oBAAoB,CAACuB,EAAE;QAChCzD,aAAa,EAAE,IAAI,CAACkC,oBAAoB,CAAClC,aAAa;QACtD0D,MAAM,EAAE,IAAI,CAACxB,oBAAoB,CAAC6B,UAAU,EAAEL,MAAM,IAAI,CAAC;QACzDE,QAAQ,EAAE,IAAI,CAAC1B,oBAAoB,CAAC6B,UAAU,EAAEH,QAAQ,IAAI,CAAC;QAC7DC,OAAO,EAAE,IAAI,CAAC3B,oBAAoB,CAAC6B,UAAU,EAAEF,OAAO,IAAI;OAC3D,CAAC;;EAEN;EAEQV,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAEiC,IAAI,EAAE5C,OAAO,EAAE;MACtC;;IAGF;IACA,IAAI,CAACoB,cAAc,CAACC,OAAO,GAAG,EAAE;IAChC,IAAI,CAACD,cAAc,CAACE,OAAO,GAAG,EAAE;IAChC,IAAI,CAACF,cAAc,CAACG,cAAc,GAAG,EAAE;IACvC,IAAI,CAACvB,OAAO,GAAG,EAAE;IAEjB;IACA,IAAI,CAACW,aAAa,CAACiC,IAAI,CAAC5C,OAAO,CAAC6C,OAAO,CAACC,MAAM,IAAG;MAC/C,MAAMC,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAACF,MAAM,CAAC;MACvD,IAAI,CAAC9C,OAAO,CAACiD,IAAI,CAACF,YAAY,CAAC;MAE/B;MACA,MAAMG,SAAS,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC,EAAED,SAAS,IAAI,CAAC;MAEjD,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC9B,cAAc,CAACC,OAAO,CAAC4B,IAAI,CAACF,YAAY,CAAC;OAC/C,MAAM,IAAIG,SAAS,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC9B,cAAc,CAACE,OAAO,CAAC2B,IAAI,CAACF,YAAY,CAAC;OAC/C,MAAM;QACL,IAAI,CAAC3B,cAAc,CAACG,cAAc,CAAC0B,IAAI,CAACF,YAAY,CAAC;;IAEzD,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3B,cAAc,CAACC,OAAO,CAAC+B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;IAC7D,IAAI,CAAC2B,cAAc,CAACE,OAAO,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;IAC7D,IAAI,CAAC2B,cAAc,CAACG,cAAc,CAAC6B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;IACpE,IAAI,CAACO,OAAO,CAACoD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5D,KAAK,GAAG6D,CAAC,CAAC7D,KAAK,CAAC;IAE9C;IACA,IAAI,CAACoB,aAAa,GAAG,IAAI,CAAC0C,gBAAgB,EAAE;EAC9C;EAEQC,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACzB,oBAAoB,EAAE;EAC7B;EAEQiB,qBAAqBA,CAACF,MAAc;IAC1C,MAAMW,SAAS,GAAGX,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;IACjC,MAAMO,UAAU,GAAGZ,MAAM,CAACa,MAAM,CAAC,CAAC,CAAC;IAEnC,OAAO;MACLvF,OAAO,EAAEqF,SAAS,EAAErF,OAAO,EAAE6C,IAAI,IAAI,SAAS;MAC9C/C,WAAW,EAAE,IAAI,CAAC0F,cAAc,CAACH,SAAS,EAAErF,OAAO,EAAEyF,EAAE,IAAI,EAAE,CAAC;MAC9DpE,KAAK,EAAEiE,UAAU,EAAEjE,KAAK,EAAEqE,MAAM,IAAI,CAAC;MACrCtE,QAAQ,EAAEkE,UAAU,EAAEjE,KAAK,EAAED,QAAQ,IAAI,KAAK;MAC9CN,KAAK,EAAEuE,SAAS,EAAEP,SAAS,IAAI,CAAC;MAChCnE,QAAQ,EAAE,IAAI,CAACgF,cAAc,CAACN,SAAS,EAAE1E,QAAQ,IAAI,CAAC,CAAC;MACvDF,aAAa,EAAE,IAAI,CAACmF,mBAAmB,CAACP,SAAS,EAAEQ,SAAS,EAAEC,IAAI,IAAI,EAAE,CAAC;MACzE5E,WAAW,EAAE,IAAI,CAAC0E,mBAAmB,CAACP,SAAS,EAAEU,OAAO,EAAED,IAAI,IAAI,EAAE,CAAC;MACrEtF,aAAa,EAAE,IAAI,CAACwF,mBAAmB,CAACX,SAAS,EAAEQ,SAAS,EAAEC,IAAI,IAAI,EAAE,CAAC;MACzE7E,WAAW,EAAE,IAAI,CAAC+E,mBAAmB,CAACX,SAAS,EAAEU,OAAO,EAAED,IAAI,IAAI,EAAE,CAAC;MACrEpF,gBAAgB,EAAE2E,SAAS,EAAEQ,SAAS,EAAEI,OAAO,EAAErD,IAAI,IAAIyC,SAAS,EAAEQ,SAAS,EAAEK,IAAI,EAAErD,IAAI,IAAI,EAAE;MAC/F1B,cAAc,EAAEkE,SAAS,EAAEU,OAAO,EAAEE,OAAO,EAAErD,IAAI,IAAIyC,SAAS,EAAEU,OAAO,EAAEG,IAAI,EAAErD,IAAI,IAAI,EAAE;MACzF5C,YAAY,EAAEoF,SAAS,EAAEc,QAAQ,IAAI,EAAE;MACvCjG,YAAY,EAAEmF,SAAS,EAAEe,WAAW,EAAEvD,IAAI,IAAI,kBAAkB;MAChEzC,cAAc,EAAEkF,UAAU,EAAEe,QAAQ,EAAEC,kBAAkB,EAAEC,QAAQ,EAAE,GAAG,GAAG,IAAI,KAAK;MACnFlG,gBAAgB,EAAE,IAAI,CAACmG,mBAAmB,CAAClB,UAAU,EAAEmB,mBAAmB,IAAI,EAAE,CAAC;MACjFzF,UAAU,EAAE,IAAI,CAAC0F,YAAY,CAACpB,UAAU,EAAEqB,QAAQ,IAAI,EAAE,CAAC;MACzDlF,UAAU,EAAE;KACb;EACH;EAEQ+D,cAAcA,CAACoB,WAAmB;IACxC,MAAM5G,OAAO,GAAG,IAAI,CAAC2C,cAAc,CAACkE,IAAI,CAAC5B,CAAC,IAAIA,CAAC,CAACrC,IAAI,KAAKgE,WAAW,CAAC;IACrE,OAAO5G,OAAO,EAAE8C,IAAI,IAAI,6BAA6B;EACvD;EAEQ6C,cAAcA,CAACmB,eAAuB;IAC5C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,eAAe,GAAG,EAAE,CAAC;IAC9C,MAAMI,OAAO,GAAGJ,eAAe,GAAG,EAAE;IACpC,OAAO,GAAGC,KAAK,KAAKG,OAAO,GAAG;EAChC;EAEQC,UAAUA,CAACC,UAAkB;IACnC;IACA,OAAOA,UAAU,IAAI,OAAO;EAC9B;EAEQxB,mBAAmBA,CAACyB,UAAkB;IAC5C,IAAI,CAACA,UAAU,EAAE,OAAO,OAAO;IAC/B,IAAI;MACF,MAAMvB,IAAI,GAAG,IAAIwB,IAAI,CAACD,UAAU,CAAC;MACjC,OAAOvB,IAAI,CAACyB,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,MAAM;MACN,OAAO,OAAO;;EAElB;EAEQ1B,mBAAmBA,CAACqB,UAAkB;IAC5C,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,IAAI;MACF,MAAMvB,IAAI,GAAG,IAAIwB,IAAI,CAACD,UAAU,CAAC;MACjC,OAAOvB,IAAI,CAAC6B,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,MAAM;MACN,OAAO,EAAE;;EAEb;EAEQtB,mBAAmBA,CAACuB,WAAkB;IAC5C,IAAI,CAACA,WAAW,IAAIA,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAC1D,MAAMC,YAAY,GAAGF,WAAW,CAAC,CAAC,CAAC;IACnC,IAAIE,YAAY,CAACC,KAAK,EAAE;MACtB,OAAO,GAAGD,YAAY,CAACC,KAAK,IAAI;;IAElC,IAAID,YAAY,CAACE,MAAM,EAAE;MACvB,OAAO,GAAGF,YAAY,CAACE,MAAM,IAAI;;IAEnC,OAAO,KAAK;EACd;EAEQzB,YAAYA,CAACC,QAAe;IAClC,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAK;IAC3B,OAAOA,QAAQ,CAACyB,IAAI,CAACC,OAAO,IAC1BA,OAAO,CAACxF,IAAI,EAAEyF,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAC9CF,OAAO,CAAC5C,EAAE,EAAE6C,WAAW,EAAE,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAC7C;EACH;EAEA;;;EAGApD,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzC,oBAAoB,EAAE,OAAO,EAAE;IAEzC,MAAMqB,IAAI,GAAG,IAAI,CAACrB,oBAAoB,CAACqB,IAAI;IAC3C,MAAME,EAAE,GAAG,IAAI,CAACvB,oBAAoB,CAACuB,EAAE;IACvC,MAAM6B,IAAI,GAAG,IAAIwB,IAAI,CAAC,IAAI,CAAC5E,oBAAoB,CAAClC,aAAa,CAAC,CAACmH,kBAAkB,CAAC,OAAO,EAAE;MACzFG,IAAI,EAAE,SAAS;MACfD,KAAK,EAAE,SAAS;MAChBD,GAAG,EAAE;KACN,CAAC;IACF,MAAM1D,MAAM,GAAG,IAAI,CAACxB,oBAAoB,CAAC6B,UAAU,EAAEL,MAAM,IAAI,CAAC;IAEhE,OAAO,GAAGH,IAAI,MAAME,EAAE,KAAK6B,IAAI,KAAK5B,MAAM,UAAU;EACtD;EAEA;;;EAGA3D,UAAUA,CAAC8G,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,IAAI;MACF,MAAMvB,IAAI,GAAG,IAAIwB,IAAI,CAACD,UAAU,CAAC;MACjC,OAAOvB,IAAI,CAAC6B,kBAAkB,CAAC,OAAO,EAAE;QACtCC,GAAG,EAAE,SAAS;QACdC,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE;OACP,CAAC;KACH,CAAC,MAAM;MACN,OAAOT,UAAU;;EAErB;EAEA;;;EAGArF,YAAYA,CAAA;IACV,IAAI,CAACG,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAxE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACsD,oBAAoB,EAAE;MAC7B,IAAI,CAACF,SAAS,GAAG,IAAI;MACrB,IAAI,CAAChD,KAAK,GAAG,IAAI;MAEjB,IAAI,CAAC8C,aAAa,CAACkG,mBAAmB,CAAC,IAAI,CAAC9F,oBAAoB,CAAC,CAAC+F,SAAS,CAAC;QAC1EC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACnG,SAAS,GAAG,KAAK;UACtB,IAAImG,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B,IAAI,CAACtG,aAAa,GAAGoG,QAAQ;YAC7B,IAAI,CAAChF,oBAAoB,EAAE;YAC3B,IAAI,CAACyB,iBAAiB,EAAE;WACzB,MAAM;YACL,IAAI,CAAC5F,KAAK,GAAG,kCAAkC;;QAEnD,CAAC;QACDA,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACgD,SAAS,GAAG,KAAK;UACtB,IAAI,CAAChD,KAAK,GAAG,sDAAsD;UACnEsJ,OAAO,CAACtJ,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;;;EAGAuJ,mBAAmBA,CAAC/I,OAAsB;IACxCA,OAAO,CAAC+C,MAAM,GAAG,CAAC/C,OAAO,CAAC+C,MAAM;IAChC;EACF;EAEA;;;EAGAiG,WAAWA,CAAA;IACT,IAAI,CAAC7G,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAqF,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7F,UAAU,CAAC8F,KAAK,EAAE;MACzB,IAAI,CAAC1G,SAAS,GAAG,IAAI;MAErB,MAAM2G,SAAS,GAAG,IAAI,CAAC/F,UAAU,CAACgG,KAAK;MACvC,MAAMhG,UAAU,GAAqB;QACnCiG,QAAQ,EAAE,QAAQ;QAClBtF,IAAI,EAAEoF,SAAS,CAACpF,IAAI;QACpBE,EAAE,EAAEkF,SAAS,CAAClF,EAAE;QAChBzD,aAAa,EAAE2I,SAAS,CAAC3I,aAAa;QACtC+D,UAAU,EAAE;UACVL,MAAM,EAAEiF,SAAS,CAACjF,MAAM;UACxBE,QAAQ,EAAE+E,SAAS,CAAC/E,QAAQ;UAC5BC,OAAO,EAAE8E,SAAS,CAAC9E;SACpB;QACDiF,KAAK,EAAE,SAAS;QAChBC,aAAa,EAAE,KAAK;QACpBC,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;OACX;MAED,IAAI,CAACpH,aAAa,CAACkG,mBAAmB,CAACpF,UAAU,CAAC,CAACqF,SAAS,CAAC;QAC3DC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACnG,SAAS,GAAG,KAAK;UACtB,IAAImG,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B,IAAI,CAACtG,aAAa,GAAGoG,QAAQ;YAC7B,IAAI,CAACjG,oBAAoB,GAAGU,UAAU;YACtC,IAAI,CAACO,oBAAoB,EAAE;;QAE/B,CAAC;QACDnE,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACgD,SAAS,GAAG,KAAK;UACtBsG,OAAO,CAACtJ,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;;;EAGAmK,kBAAkBA,CAACC,QAAkD;IACnE,OAAO,IAAI,CAAC5G,cAAc,CAAC4G,QAAQ,CAAC;EACtC;EAEA;;;EAGAC,mBAAmBA,CAACD,QAAkD;IACpE,MAAMhI,OAAO,GAAG,IAAI,CAACoB,cAAc,CAAC4G,QAAQ,CAAC;IAC7C,IAAIhI,OAAO,CAACoG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IACrC,OAAOhB,IAAI,CAAC7C,GAAG,CAAC,GAAGvC,OAAO,CAACkI,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC1I,KAAK,CAAC,CAAC;EAC/C;EAEA;;;EAGA2I,WAAWA,CAAC3I,KAAa,EAAED,QAAA,GAAmB,KAAK;IACjD,OAAO,GAAGC,KAAK,IAAID,QAAQ,EAAE;EAC/B;EAEA;;;EAGA6I,YAAYA,CAACL,QAAkD;IAC7D,QAAQA,QAAQ;MACd,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,gBAAgB;QAAE,OAAO,UAAU;MACxC;QAAS,OAAO,EAAE;;EAEtB;EAEA;;;EAGAM,2BAA2BA,CAACtD,WAAmB,EAAEgD,QAAkD;IACjG,MAAMhI,OAAO,GAAG,IAAI,CAACoB,cAAc,CAAC4G,QAAQ,CAAC;IAC7C,OAAOhI,OAAO,CAACiF,IAAI,CAACnC,MAAM,IAAIA,MAAM,CAAC1E,OAAO,CAACuI,QAAQ,CAAC3B,WAAW,CAAC,CAAC,IAAI,IAAI;EAC7E;;;uBA1XW3E,sBAAsB,EAAAxD,EAAA,CAAA0L,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA5L,EAAA,CAAA0L,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA7L,EAAA,CAAA0L,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA/L,EAAA,CAAA0L,iBAAA,CAAAM,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAtBzI,sBAAsB;MAAA0I,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvCnCxM,EAAA,CAAAC,cAAA,aAAsC;UAI5BD,EAAA,CAAAE,MAAA,4BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,GAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI5BH,EAAA,CAAAC,cAAA,gBAAiE;UAAzBD,EAAA,CAAAI,UAAA,mBAAAsM,wDAAA;YAAA,OAASD,GAAA,CAAAlJ,YAAA,EAAc;UAAA,EAAC;UAC9DvD,EAAA,CAAAgB,SAAA,WAA2B;UAAChB,EAAA,CAAAE,MAAA,sBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAiB,UAAA,KAAA0L,sCAAA,iBAKM;UAGN3M,EAAA,CAAAiB,UAAA,KAAA2L,sCAAA,iBAIM;UAGN5M,EAAA,CAAAiB,UAAA,KAAA4L,sCAAA,iBA8FM;UAGN7M,EAAA,CAAAiB,UAAA,KAAA6L,sCAAA,iBAOM;UACR9M,EAAA,CAAAG,YAAA,EAAM;;;UAlIGH,EAAA,CAAAY,SAAA,GAAmB;UAAnBZ,EAAA,CAAAa,iBAAA,CAAA4L,GAAA,CAAAzI,aAAA,CAAmB;UAUpBhE,EAAA,CAAAY,SAAA,GAAe;UAAfZ,EAAA,CAAAmB,UAAA,SAAAsL,GAAA,CAAA1I,SAAA,CAAe;UAQf/D,EAAA,CAAAY,SAAA,GAAW;UAAXZ,EAAA,CAAAmB,UAAA,SAAAsL,GAAA,CAAA1L,KAAA,CAAW;UAOXf,EAAA,CAAAY,SAAA,GAAgD;UAAhDZ,EAAA,CAAAmB,UAAA,UAAAsL,GAAA,CAAA1I,SAAA,KAAA0I,GAAA,CAAA1L,KAAA,IAAA0L,GAAA,CAAAtJ,OAAA,CAAAoG,MAAA,KAAgD;UAiGhDvJ,EAAA,CAAAY,SAAA,GAAkD;UAAlDZ,EAAA,CAAAmB,UAAA,UAAAsL,GAAA,CAAA1I,SAAA,KAAA0I,GAAA,CAAA1L,KAAA,IAAA0L,GAAA,CAAAtJ,OAAA,CAAAoG,MAAA,OAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}