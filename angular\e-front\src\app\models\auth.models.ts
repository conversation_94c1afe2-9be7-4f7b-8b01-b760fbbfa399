export interface AuthRequest {
  Agency: string;
  User: string;
  Password: string;
}

export interface AuthResponse {
  header: {
    requestId: string;
    success: boolean;
    responseMessage: string;
    messages?: Array<{
      id: number;
      code: string;
      messageType: number;
      message: string;
    }>;
  };
  body: {
    token: string;
    expiresOn: string;
    tokenId: number;
    userInfo: {
      code: string;
      name: string;
      mainAgency?: any;
      agency: {
        code: string;
        name: string;
        registerCode: string;
      };
      office: {
        code: string;
        name: string;
      };
      operator: {
        code: string;
        name: string;
        thumbnail: string;
      };
      market: {
        code: string;
        name: string;
        favicon: string;
      };
    };
    loggedInWithMasterKey?: boolean;
  };
}

export interface LoginCredentials {
  agency: string;
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  expiresOn?: string;
  message?: string;
  user?: {
    code: string;
    name: string;
    agencyCode: string;
    agencyName: string;
    office?: {
      code: string;
      name: string;
    };
    operator?: {
      code: string;
      name: string;
      thumbnail: string;
    };
    market?: {
      code: string;
      name: string;
      favicon: string;
    };
  };
}
