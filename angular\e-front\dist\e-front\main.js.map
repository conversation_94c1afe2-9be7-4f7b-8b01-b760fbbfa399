{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AACuD;AACgB;AACS;AACT;AACuB;AAC9C;;;AAEhD,MAAMM,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,SAAS;EAAEC,SAAS,EAAE;AAAM,CAAE,EACtD;EAAEF,IAAI,EAAE,QAAQ;EAAEG,SAAS,EAAET,gFAAeA;AAAA,CAAE,EAC9C;EAAEM,IAAI,EAAE,WAAW;EAAEG,SAAS,EAAER,yFAAkB;EAAES,WAAW,EAAE,CAACN,yDAAS;AAAC,CAAE,EAC9E;EAAEE,IAAI,EAAE,SAAS;EAAEG,SAAS,EAAEP,gFAAe;EAAEQ,WAAW,EAAE,CAACN,yDAAS;AAAC,CAAE,EACzE;EAAEE,IAAI,EAAE,gBAAgB;EAAEG,SAAS,EAAEN,uGAAsB;EAAEO,WAAW,EAAE,CAACN,yDAAS;AAAC,CAAE,EACvF;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAS,CAAE,CACtC;AAMK,MAAOI,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBZ,yDAAY,CAACa,OAAO,CAACP,MAAM,CAAC,EAC5BN,yDAAY;IAAA;EAAA;;;sHAEXY,gBAAgB;IAAAE,OAAA,GAAAC,yDAAA;IAAAC,OAAA,GAFjBhB,yDAAY;EAAA;AAAA;;;;;;;;;;;;;;;;;;ACZlB,MAAOiB,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,SAAS;;;;uBADNF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNzBE,uDAAA,oBAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACA2B;AACQ;AACV;AAEA;AACT;AACwB;AACS;AACT;AACuB;AACE;;AAqB1F,MAAOO,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRlB,wDAAY;IAAA;EAAA;;;gBAPtBY,oEAAa,EACbjB,iEAAgB,EAChBkB,+DAAmB,EACnBC,uDAAW,EACXC,mEAAgB;IAAA;EAAA;;;sHAKPE,SAAS;IAAAE,YAAA,GAjBlBnB,wDAAY,EACZhB,gFAAe,EACfC,yFAAkB,EAClBC,gFAAe,EACfC,uGAAsB,EACtB6B,yGAAqB;IAAAnB,OAAA,GAGrBe,oEAAa,EACbjB,iEAAgB,EAChBkB,+DAAmB,EACnBC,uDAAW,EACXC,mEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;IEnBZL,4DAAA,YAAkD;IAAAA,oDAAA,GAAoC;IAAAA,0DAAA,EAAI;;;;IAAxCA,uDAAA,GAAoC;IAApCA,gEAAA,mBAAAe,MAAA,CAAAC,WAAA,CAAAC,IAAA,KAAoC;;;;;IAc1FjB,4DAAA,cAA2C;IAEfA,oDAAA,GAAiD;IAAAA,0DAAA,EAAO;IAChFA,4DAAA,eAAsB;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;IACjDA,4DAAA,eAAuB;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAO;;;;IAFvBA,uDAAA,GAAiD;IAAjDA,gEAAA,KAAAkB,MAAA,CAAAF,WAAA,CAAAG,UAAA,0BAAiD;;;ADf3E,MAAO5C,kBAAkB;EAG7BgB,YACU6B,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAL,WAAW,GAAQ,IAAI;EAKpB;EAEHM,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACT,WAAW,GAAGS,IAAI;IACzB,CAAC,CAAC;IAEF;IACA,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;;EAErC;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,IAAI,CAACP,MAAM,CAACM,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAE,MAAMA,CAAA;IACJ,IAAI,CAACT,WAAW,CAACS,MAAM,EAAE;IACzB,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnC;;;uBAjCWpD,kBAAkB,EAAAyB,+DAAA,CAAAZ,+DAAA,GAAAY,+DAAA,CAAAgC,mDAAA;IAAA;EAAA;;;YAAlBzD,kBAAkB;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAuC,MAAA;MAAAtC,QAAA,WAAAuC,4BAAArC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BE,4DAAA,aAAiC;UAMCA,oDAAA,oBAAa;UAAAA,0DAAA,EAAO;UAE9CA,wDAAA,IAAAqC,+BAAA,eAA0F;UAC5FrC,0DAAA,EAAM;UACNA,4DAAA,aAA0B;UAEOA,uDAAA,aAA2B;UAACA,oDAAA,aAAI;UAAAA,0DAAA,EAAI;UAEjEA,4DAAA,YAA6B;UAAAA,uDAAA,aAA6B;UAACA,oDAAA,cAAK;UAAAA,0DAAA,EAAI;UACpEA,4DAAA,YAA6B;UAAAA,uDAAA,aAA4B;UAACA,oDAAA,kBAAS;UAAAA,0DAAA,EAAI;UACvEA,4DAAA,kBAAiD;UAAnBA,wDAAA,mBAAAuC,qDAAA;YAAA,OAASxC,GAAA,CAAA8B,MAAA,EAAQ;UAAA,EAAC;UAC9C7B,uDAAA,aAAmC;UAACA,oDAAA,gBACtC;UAAAA,0DAAA,EAAS;UAIfA,wDAAA,KAAAwC,kCAAA,kBAMM;UACRxC,0DAAA,EAAS;UAGTA,4DAAA,gBAA6B;UAOnBA,uDAAA,aAAqC;UACvCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAO;UAI/CA,4DAAA,eAAkC;UAE9BA,uDAAA,aAAiC;UACnCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAO;UAI7CA,4DAAA,eAAgC;UAE5BA,uDAAA,aAA4B;UAC9BA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAO;UAK7CA,4DAAA,eAAiB;UAIXA,uDAAA,aAA2B;UAC7BA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UAIzCA,4DAAA,eAA8B;UAE1BA,uDAAA,aAAsC;UACxCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UAIzCA,4DAAA,eAAqC;UAEjCA,uDAAA,aAA+B;UACjCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAO;UAKlDA,4DAAA,eAA8B;UAEWA,wDAAA,mBAAAyC,kDAAA;YAAA,OAAS1C,GAAA,CAAA6B,iBAAA,EAAmB;UAAA,EAAC;UAClE5B,4DAAA,eAAuB;UACrBA,uDAAA,aAA4B;UAC9BA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAO;UAIzCA,4DAAA,eAAkC;UAE9BA,uDAAA,aAAkC;UACpCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAO;UAK/CA,4DAAA,eAAiB;UAIXA,uDAAA,aAAmC;UACrCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAO;UAI5CA,4DAAA,eAAqC;UAEjCA,uDAAA,aAAkC;UACpCA,0DAAA,EAAM;UACNA,4DAAA,gBAAyB;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAO;;;UAlHjBA,uDAAA,GAAiB;UAAjBA,wDAAA,SAAAD,GAAA,CAAAiB,WAAA,CAAiB;UAc5BhB,uDAAA,IAAiB;UAAjBA,wDAAA,SAAAD,GAAA,CAAAiB,WAAA,CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBuB;;;;;;;;IC8D1DhB,4DAAA,WAAyB;IAAAA,oDAAA,mBAAY;IAAAA,0DAAA,EAAO;;;;;IAC5CA,4DAAA,WAAwB;IACtBA,uDAAA,YAAsC;IAACA,oDAAA,qBACzC;IAAAA,0DAAA,EAAO;;;;;;IAWTA,4DAAA,iBAIyC;IAAvCA,wDAAA,mBAAA4C,kEAAA;MAAA,MAAAC,WAAA,GAAA7C,2DAAA,CAAA+C,IAAA;MAAA,MAAAC,UAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAlD,2DAAA;MAAA,OAASA,yDAAA,CAAAkD,MAAA,CAAAG,mBAAA,CAAAL,UAAA,CAA4B;IAAA,EAAC;IACtChD,4DAAA,cAAsC;IACTA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;;;;IAHtDA,yDAAA,WAAAgD,UAAA,CAAAO,MAAA,CAA+B;IAGFvD,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAgD,UAAA,CAAAS,IAAA,CAAkB;;;;;IAWjDzD,4DAAA,cAAmE;IAE9BA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;;;;IAAzBA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA0D,WAAA,CAAAD,IAAA,CAAkB;;;;;IAWrDzD,qEAAA,GAAqF;IACnFA,4DAAA,cAAwB;IACFA,oDAAA,GAA+B;IAAAA,0DAAA,EAAO;IAC1DA,4DAAA,eAAuB;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAO;IAErCA,mEAAA,EAAe;;;;;IAHSA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAA6D,OAAA,CAAAC,WAAA,CAAAC,UAAA,CAAAC,KAAA,EAA+B;;;;;IAIvDhE,qEAAA,GAA4E;IAC1EA,4DAAA,cAAuB;IAAAA,oDAAA,QAAC;IAAAA,0DAAA,EAAM;IAChCA,mEAAA,EAAe;;;;;IATjBA,4DAAA,cAA+D;IAC7DA,wDAAA,IAAAiE,qDAAA,2BAKe;IACfjE,wDAAA,IAAAkE,qDAAA,2BAEe;IACjBlE,0DAAA,EAAM;;;;;IATWA,uDAAA,GAA2D;IAA3DA,wDAAA,SAAAmE,MAAA,CAAAC,2BAAA,CAAAC,WAAA,CAAAZ,IAAA,aAA2D;IAM3DzD,uDAAA,GAA2D;IAA3DA,wDAAA,UAAAmE,MAAA,CAAAC,2BAAA,CAAAC,WAAA,CAAAZ,IAAA,aAA2D;;;;;IAY1EzD,qEAAA,GAAqF;IACnFA,4DAAA,cAAwB;IACFA,oDAAA,GAA+B;IAAAA,0DAAA,EAAO;IAC1DA,4DAAA,eAAuB;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAO;IAErCA,mEAAA,EAAe;;;;;IAHSA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAsE,OAAA,CAAAR,WAAA,CAAAS,UAAA,CAAAP,KAAA,EAA+B;;;;;IAIvDhE,qEAAA,GAA4E;IAC1EA,4DAAA,cAAuB;IAAAA,oDAAA,QAAC;IAAAA,0DAAA,EAAM;IAChCA,mEAAA,EAAe;;;;;IATjBA,4DAAA,cAA+D;IAC7DA,wDAAA,IAAAwE,qDAAA,2BAKe;IACfxE,wDAAA,IAAAyE,qDAAA,2BAEe;IACjBzE,0DAAA,EAAM;;;;;IATWA,uDAAA,GAA2D;IAA3DA,wDAAA,SAAA0E,MAAA,CAAAN,2BAAA,CAAAO,WAAA,CAAAlB,IAAA,aAA2D;IAM3DzD,uDAAA,GAA2D;IAA3DA,wDAAA,UAAA0E,MAAA,CAAAN,2BAAA,CAAAO,WAAA,CAAAlB,IAAA,aAA2D;;;;;IAY1EzD,qEAAA,GAA4F;IAC1FA,4DAAA,cAAwB;IACFA,oDAAA,GAA+B;IAAAA,0DAAA,EAAO;IAC1DA,4DAAA,eAAuB;IAAAA,oDAAA,UAAG;IAAAA,0DAAA,EAAO;IAErCA,mEAAA,EAAe;;;;;IAHSA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAA4E,OAAA,CAAAd,WAAA,CAAAe,UAAA,CAAAb,KAAA,EAA+B;;;;;IAIvDhE,qEAAA,GAAmF;IACjFA,4DAAA,cAAuB;IAAAA,oDAAA,QAAC;IAAAA,0DAAA,EAAM;IAChCA,mEAAA,EAAe;;;;;IATjBA,4DAAA,cAA+D;IAC7DA,wDAAA,IAAA8E,qDAAA,2BAKe;IACf9E,wDAAA,IAAA+E,qDAAA,2BAEe;IACjB/E,0DAAA,EAAM;;;;;IATWA,uDAAA,GAAkE;IAAlEA,wDAAA,SAAAgF,MAAA,CAAAZ,2BAAA,CAAAa,WAAA,CAAAxB,IAAA,oBAAkE;IAMlEzD,uDAAA,GAAkE;IAAlEA,wDAAA,UAAAgF,MAAA,CAAAZ,2BAAA,CAAAa,WAAA,CAAAxB,IAAA,oBAAkE;;;;;IAQvFzD,4DAAA,cAAoF;IAEhFA,uDAAA,YAA4B;IAC5BA,4DAAA,SAAI;IAAAA,oDAAA,uBAAgB;IAAAA,0DAAA,EAAK;IACzBA,4DAAA,QAAG;IAAAA,oDAAA,yCAAkC;IAAAA,0DAAA,EAAI;;;ADnI3C,MAAOvB,sBAAsB;EAuBjCc,YACU8B,MAAc,EACd6D,KAAqB,EACrBC,EAAe,EACfC,aAA4B;IAH5B,KAAA/D,MAAM,GAANA,MAAM;IACN,KAAA6D,KAAK,GAALA,KAAK;IACL,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IA1BvB,KAAAC,aAAa,GAAgC,IAAI;IAEjD,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,oBAAoB,GAAQ,IAAI;IAEhC;IACA,KAAAC,cAAc,GAAoB,CAChC;MAAE/B,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,kBAAkB;MAAEwE,IAAI,EAAE,sCAAsC;MAAElC,MAAM,EAAE;IAAK,CAAE,EACrG;MAAEE,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,WAAW;MAAEwE,IAAI,EAAE,+BAA+B;MAAElC,MAAM,EAAE;IAAK,CAAE,EACvF;MAAEE,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,QAAQ;MAAEwE,IAAI,EAAE,4BAA4B;MAAElC,MAAM,EAAE;IAAK,CAAE,EACjF;MAAEE,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,UAAU;MAAEwE,IAAI,EAAE,8BAA8B;MAAElC,MAAM,EAAE;IAAK,CAAE,EACrF;MAAEE,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,aAAa;MAAEwE,IAAI,EAAE,iCAAiC;MAAElC,MAAM,EAAE;IAAK,CAAE,EAC3F;MAAEE,IAAI,EAAE,IAAI;MAAExC,IAAI,EAAE,UAAU;MAAEwE,IAAI,EAAE,8BAA8B;MAAElC,MAAM,EAAE;IAAK,CAAE,CACtF;IAED;IACA,KAAAmC,cAAc,GAAG;MACfC,OAAO,EAAE,EAAoB;MAC7BC,OAAO,EAAE,EAAoB;MAC7BC,cAAc,EAAE;KACjB;IAQC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,gBAAgB,EAAE;EAC3C;EAEAzE,QAAQA,CAAA;IACN;IACA,MAAM0E,UAAU,GAAG,IAAI,CAAC3E,MAAM,CAAC4E,oBAAoB,EAAE;IACrD,IAAID,UAAU,EAAEE,MAAM,CAACC,KAAK,GAAG,SAAS,CAAC,EAAE;MACzC,IAAI,CAACd,aAAa,GAAGW,UAAU,CAACE,MAAM,CAACC,KAAK,CAAC,SAAS,CAAC;MACvD,IAAI,CAACZ,oBAAoB,GAAGS,UAAU,CAACE,MAAM,CAACC,KAAK,CAAC,cAAc,CAAC;MACnE,IAAI,CAACC,oBAAoB,EAAE;KAC5B,MAAM;MACL;MACA,IAAI,CAAC/E,MAAM,CAACM,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;;IAGpC;IACA,IAAI,IAAI,CAAC4D,oBAAoB,EAAE;MAC7B,IAAI,CAACc,oBAAoB,EAAE;;EAE/B;EAEQN,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAACZ,EAAE,CAACmB,KAAK,CAAC;MACnBC,IAAI,EAAE,CAAC,EAAE,EAAE5D,sDAAU,CAAC6D,QAAQ,CAAC;MAC/BC,EAAE,EAAE,CAAC,EAAE,EAAE9D,sDAAU,CAAC6D,QAAQ,CAAC;MAC7BE,aAAa,EAAE,CAAC,EAAE,EAAE/D,sDAAU,CAAC6D,QAAQ,CAAC;MACxCG,MAAM,EAAE,CAAC,CAAC,EAAE,CAAChE,sDAAU,CAAC6D,QAAQ,EAAE7D,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAClE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAACnE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC;KACjC,CAAC;EACJ;EAEQP,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAACd,oBAAoB,EAAE;MAC7B,IAAI,CAACO,UAAU,CAACiB,UAAU,CAAC;QACzBR,IAAI,EAAE,IAAI,CAAChB,oBAAoB,CAACgB,IAAI;QACpCE,EAAE,EAAE,IAAI,CAAClB,oBAAoB,CAACkB,EAAE;QAChCC,aAAa,EAAE,IAAI,CAACnB,oBAAoB,CAACmB,aAAa;QACtDC,MAAM,EAAE,IAAI,CAACpB,oBAAoB,CAACyB,UAAU,EAAEL,MAAM,IAAI,CAAC;QACzDE,QAAQ,EAAE,IAAI,CAACtB,oBAAoB,CAACyB,UAAU,EAAEH,QAAQ,IAAI,CAAC;QAC7DC,OAAO,EAAE,IAAI,CAACvB,oBAAoB,CAACyB,UAAU,EAAEF,OAAO,IAAI;OAC3D,CAAC;;EAEN;EAEQV,oBAAoBA,CAAA;IAC1B,IAAI,CAAC,IAAI,CAACf,aAAa,EAAE4B,IAAI,EAAEC,OAAO,EAAE;MACtC;;IAGF;IACA,IAAI,CAACxB,cAAc,CAACC,OAAO,GAAG,EAAE;IAChC,IAAI,CAACD,cAAc,CAACE,OAAO,GAAG,EAAE;IAChC,IAAI,CAACF,cAAc,CAACG,cAAc,GAAG,EAAE;IAEvC;IACA,IAAI,CAACR,aAAa,CAAC4B,IAAI,CAACC,OAAO,CAACC,OAAO,CAACC,MAAM,IAAG;MAC/C,MAAMC,YAAY,GAAG,IAAI,CAACC,qBAAqB,CAACF,MAAM,CAAC;MAEvD;MACA,MAAMG,SAAS,GAAGH,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC,EAAED,SAAS,IAAI,CAAC;MAEjD,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnB,IAAI,CAAC7B,cAAc,CAACC,OAAO,CAAC8B,IAAI,CAACJ,YAAY,CAAC;OAC/C,MAAM,IAAIE,SAAS,KAAK,CAAC,EAAE;QAC1B,IAAI,CAAC7B,cAAc,CAACE,OAAO,CAAC6B,IAAI,CAACJ,YAAY,CAAC;OAC/C,MAAM;QACL,IAAI,CAAC3B,cAAc,CAACG,cAAc,CAAC4B,IAAI,CAACJ,YAAY,CAAC;;IAEzD,CAAC,CAAC;IAEF;IACA,IAAI,CAAC3B,cAAc,CAACC,OAAO,CAAC+B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3D,KAAK,GAAG4D,CAAC,CAAC5D,KAAK,CAAC;IAC7D,IAAI,CAAC0B,cAAc,CAACE,OAAO,CAAC8B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3D,KAAK,GAAG4D,CAAC,CAAC5D,KAAK,CAAC;IAC7D,IAAI,CAAC0B,cAAc,CAACG,cAAc,CAAC6B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3D,KAAK,GAAG4D,CAAC,CAAC5D,KAAK,CAAC;EACtE;EAEQsD,qBAAqBA,CAACF,MAAc;IAC1C,MAAMS,SAAS,GAAGT,MAAM,CAACI,KAAK,CAAC,CAAC,CAAC;IACjC,MAAMM,UAAU,GAAGV,MAAM,CAACW,MAAM,CAAC,CAAC,CAAC;IAEnC,OAAO;MACLC,OAAO,EAAEH,SAAS,EAAEG,OAAO,EAAE/G,IAAI,IAAI,SAAS;MAC9CgH,WAAW,EAAE,IAAI,CAACC,cAAc,CAACL,SAAS,EAAEG,OAAO,EAAEvE,IAAI,IAAI,EAAE,CAAC;MAChEO,KAAK,EAAE8D,UAAU,EAAE9D,KAAK,EAAEmE,MAAM,IAAI,CAAC;MACrCC,QAAQ,EAAEN,UAAU,EAAE9D,KAAK,EAAEoE,QAAQ,IAAI,KAAK;MAC9CC,KAAK,EAAER,SAAS,EAAEN,SAAS,IAAI,CAAC;MAChCe,QAAQ,EAAE,IAAI,CAACC,cAAc,CAACV,SAAS,EAAES,QAAQ,IAAI,CAAC,CAAC;MACvDE,aAAa,EAAE,IAAI,CAACC,UAAU,CAACZ,SAAS,EAAEa,SAAS,EAAEjF,IAAI,IAAI,EAAE,CAAC;MAChEkF,WAAW,EAAE,IAAI,CAACF,UAAU,CAACZ,SAAS,EAAEe,OAAO,EAAEnF,IAAI,IAAI,EAAE,CAAC;MAC5DoF,YAAY,EAAEhB,SAAS,EAAEiB,QAAQ,IAAI;KACtC;EACH;EAEQZ,cAAcA,CAACa,WAAmB;IACxC,MAAMf,OAAO,GAAG,IAAI,CAACxC,cAAc,CAACwD,IAAI,CAACrB,CAAC,IAAIA,CAAC,CAAClE,IAAI,KAAKsF,WAAW,CAAC;IACrE,OAAOf,OAAO,EAAEvC,IAAI,IAAI,6BAA6B;EACvD;EAEQ8C,cAAcA,CAACU,eAAuB;IAC5C,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,eAAe,GAAG,EAAE,CAAC;IAC9C,MAAMI,OAAO,GAAGJ,eAAe,GAAG,EAAE;IACpC,OAAO,GAAGC,KAAK,KAAKG,OAAO,GAAG;EAChC;EAEQZ,UAAUA,CAACa,UAAkB;IACnC;IACA,OAAOA,UAAU,IAAI,OAAO;EAC9B;EAEA;;;EAGAC,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAChE,oBAAoB,EAAE,OAAO,EAAE;IAEzC,MAAMgB,IAAI,GAAG,IAAI,CAAChB,oBAAoB,CAACgB,IAAI;IAC3C,MAAME,EAAE,GAAG,IAAI,CAAClB,oBAAoB,CAACkB,EAAE;IACvC,MAAM+C,IAAI,GAAG,IAAIC,IAAI,CAAC,IAAI,CAAClE,oBAAoB,CAACmB,aAAa,CAAC,CAACgD,kBAAkB,CAAC,OAAO,EAAE;MACzFC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,GAAG,EAAE;KACN,CAAC;IACF,MAAMlD,MAAM,GAAG,IAAI,CAACpB,oBAAoB,CAACyB,UAAU,EAAEL,MAAM,IAAI,CAAC;IAEhE,OAAO,GAAGJ,IAAI,MAAME,EAAE,KAAK+C,IAAI,KAAK7C,MAAM,UAAU;EACtD;EAEA;;;EAGAtD,mBAAmBA,CAAC2E,OAAsB;IACxCA,OAAO,CAACzE,MAAM,GAAG,CAACyE,OAAO,CAACzE,MAAM;IAChC;EACF;EAEA;;;EAGAuG,WAAWA,CAAA;IACT,IAAI,CAACzI,MAAM,CAACM,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAoI,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjE,UAAU,CAACkE,KAAK,EAAE;MACzB,IAAI,CAAC1E,SAAS,GAAG,IAAI;MAErB,MAAM2E,SAAS,GAAG,IAAI,CAACnE,UAAU,CAACoE,KAAK;MACvC,MAAMpE,UAAU,GAAqB;QACnCqE,QAAQ,EAAE,QAAQ;QAClB5D,IAAI,EAAE0D,SAAS,CAAC1D,IAAI;QACpBE,EAAE,EAAEwD,SAAS,CAACxD,EAAE;QAChBC,aAAa,EAAEuD,SAAS,CAACvD,aAAa;QACtCM,UAAU,EAAE;UACVL,MAAM,EAAEsD,SAAS,CAACtD,MAAM;UACxBE,QAAQ,EAAEoD,SAAS,CAACpD,QAAQ;UAC5BC,OAAO,EAAEmD,SAAS,CAACnD;SACpB;QACDsD,KAAK,EAAE,SAAS;QAChBC,aAAa,EAAE,KAAK;QACpBC,eAAe,EAAE,KAAK;QACtBC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE;OACX;MAED,IAAI,CAACpF,aAAa,CAACqF,mBAAmB,CAAC3E,UAAU,CAAC,CAACtE,SAAS,CAAC;QAC3DkJ,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACrF,SAAS,GAAG,KAAK;UACtB,IAAIqF,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B,IAAI,CAACxF,aAAa,GAAGsF,QAAQ;YAC7B,IAAI,CAACpF,oBAAoB,GAAGO,UAAU;YACtC,IAAI,CAACM,oBAAoB,EAAE;;QAE/B,CAAC;QACD0E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACxF,SAAS,GAAG,KAAK;UACtByF,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACvC;OACD,CAAC;;EAEN;EAEA;;;EAGAE,kBAAkBA,CAACC,QAAkD;IACnE,OAAO,IAAI,CAACvF,cAAc,CAACuF,QAAQ,CAAC;EACtC;EAEA;;;EAGAC,mBAAmBA,CAACD,QAAkD;IACpE,MAAM/D,OAAO,GAAG,IAAI,CAACxB,cAAc,CAACuF,QAAQ,CAAC;IAC7C,IAAI/D,OAAO,CAACiE,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IACrC,OAAOhC,IAAI,CAACvC,GAAG,CAAC,GAAGM,OAAO,CAACkE,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACrH,KAAK,CAAC,CAAC;EAC/C;EAEA;;;EAGAF,WAAWA,CAACE,KAAa,EAAEoE,QAAA,GAAmB,KAAK;IACjD,OAAO,GAAGpE,KAAK,IAAIoE,QAAQ,EAAE;EAC/B;EAEA;;;EAGAkD,YAAYA,CAACL,QAAkD;IAC7D,QAAQA,QAAQ;MACd,KAAK,SAAS;QAAE,OAAO,UAAU;MACjC,KAAK,SAAS;QAAE,OAAO,QAAQ;MAC/B,KAAK,gBAAgB;QAAE,OAAO,UAAU;MACxC;QAAS,OAAO,EAAE;;EAEtB;EAEA;;;EAGA7G,2BAA2BA,CAAC2E,WAAmB,EAAEkC,QAAkD;IACjG,MAAM/D,OAAO,GAAG,IAAI,CAACxB,cAAc,CAACuF,QAAQ,CAAC;IAC7C,OAAO/D,OAAO,CAAC8B,IAAI,CAAC5B,MAAM,IAAIA,MAAM,CAACY,OAAO,CAACuD,QAAQ,CAACxC,WAAW,CAAC,CAAC,IAAI,IAAI;EAC7E;;;uBA/PWtK,sBAAsB,EAAAuB,+DAAA,CAAAZ,mDAAA,GAAAY,+DAAA,CAAAZ,2DAAA,GAAAY,+DAAA,CAAAgC,uDAAA,GAAAhC,+DAAA,CAAA0L,mEAAA;IAAA;EAAA;;;YAAtBjN,sBAAsB;MAAAgB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAuC,MAAA;MAAAtC,QAAA,WAAAgM,gCAAA9L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/BnCE,4DAAA,aAAsC;UAGTA,oDAAA,GAAwB;UAAAA,0DAAA,EAAK;UAIxDA,4DAAA,aAAuC;UAKxBA,oDAAA,WAAI;UAAAA,0DAAA,EAAQ;UACnBA,uDAAA,gBAIuC;UACzCA,0DAAA,EAAM;UAGNA,4DAAA,cAAwB;UACfA,oDAAA,UAAE;UAAAA,0DAAA,EAAQ;UACjBA,uDAAA,gBAIoC;UACtCA,0DAAA,EAAM;UAGNA,4DAAA,cAAwB;UACfA,oDAAA,oBAAY;UAAAA,0DAAA,EAAQ;UAC3BA,4DAAA,cAAgC;UAC9BA,uDAAA,iBAGkC;UAClCA,4DAAA,eAA2B;UACcA,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UACjDA,4DAAA,kBAAuC;UAAAA,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UAOzDA,4DAAA,eAA4B;UAIxBA,uDAAA,aAA+B;UAACA,oDAAA,2BAClC;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAG0B;UAAxBA,wDAAA,mBAAA6L,yDAAA;YAAA,OAAS9L,GAAA,CAAA+J,WAAA,EAAa;UAAA,EAAC;UACvB9J,oDAAA,oBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAIyB;UADvBA,wDAAA,mBAAA8L,yDAAA;YAAA,OAAS/L,GAAA,CAAAgK,aAAA,EAAe;UAAA,EAAC;UAEzB/J,wDAAA,KAAA+L,uCAAA,mBAA4C;UAC5C/L,wDAAA,KAAAgM,uCAAA,mBAEO;UACThM,0DAAA,EAAS;UAMfA,4DAAA,eAA6B;UAIvBA,wDAAA,KAAAiM,yCAAA,qBAQS;UACXjM,0DAAA,EAAM;UAIRA,4DAAA,eAAiC;UAG7BA,uDAAA,eAAwC;UACxCA,wDAAA,KAAAkM,sCAAA,kBAIM;UACRlM,0DAAA,EAAM;UAGNA,4DAAA,eAAsB;UAEOA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAO;UAE1CA,wDAAA,KAAAmM,sCAAA,kBAUM;UACRnM,0DAAA,EAAM;UAGNA,4DAAA,eAAsB;UAEOA,oDAAA,cAAM;UAAAA,0DAAA,EAAO;UAExCA,wDAAA,KAAAoM,sCAAA,kBAUM;UACRpM,0DAAA,EAAM;UAGNA,4DAAA,eAAsB;UAEOA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAO;UAE1CA,wDAAA,KAAAqM,sCAAA,kBAUM;UACRrM,0DAAA,EAAM;UAIRA,wDAAA,KAAAsM,sCAAA,kBAMM;UAGNtM,4DAAA,eAA+B;UAE3BA,uDAAA,eAAwC;UACxCA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAG9CA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAG9CA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAG9CA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAG9CA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAG9CA,4DAAA,eAA4B;UAESA,oDAAA,UAAE;UAAAA,0DAAA,EAAO;UAMhDA,4DAAA,eAAsB;UAEOA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAO;UAE1CA,4DAAA,eAAwB;UAEAA,oDAAA,WAAG;UAAAA,0DAAA,EAAO;UAC9BA,4DAAA,gBAAuB;UAAAA,oDAAA,WAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,eAAwB;UAEAA,oDAAA,WAAG;UAAAA,0DAAA,EAAO;UAC9BA,4DAAA,gBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UAEAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAC9BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAKlCA,4DAAA,gBAAsB;UAEOA,oDAAA,eAAM;UAAAA,0DAAA,EAAO;UAExCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UAEAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAC9BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UAEAA,oDAAA,aAAI;UAAAA,0DAAA,EAAO;UAC/BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UAEAA,oDAAA,aAAI;UAAAA,0DAAA,EAAO;UAC/BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UAEAA,oDAAA,aAAI;UAAAA,0DAAA,EAAO;UAC/BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAMvCA,4DAAA,gBAAsB;UAEOA,oDAAA,iBAAQ;UAAAA,0DAAA,EAAO;UAE1CA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;UAEhCA,4DAAA,gBAAwB;UAEAA,oDAAA,aAAI;UAAAA,0DAAA,EAAO;UAC/BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UAEAA,oDAAA,aAAI;UAAAA,0DAAA,EAAO;UAC/BA,4DAAA,iBAAuB;UAAAA,oDAAA,YAAG;UAAAA,0DAAA,EAAO;UAGrCA,4DAAA,gBAAwB;UACCA,oDAAA,UAAC;UAAAA,0DAAA,EAAM;;;UAzSXA,uDAAA,GAAwB;UAAxBA,+DAAA,CAAAD,GAAA,CAAAwJ,gBAAA,GAAwB;UAK3CvJ,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAD,GAAA,CAAA+F,UAAA,CAAwB;UAuDxB9F,uDAAA,IAAsB;UAAtBA,wDAAA,aAAAD,GAAA,CAAAuF,SAAA,CAAsB;UACftF,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAAuF,SAAA,CAAgB;UAChBtF,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAAuF,SAAA,CAAe;UAcFtF,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAAyF,cAAA,CAAiB;UAgBSxF,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAAyF,cAAA,CAAiB;UAYrBxF,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAAyF,cAAA,CAAiB;UAkBjBxF,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAAyF,cAAA,CAAiB;UAkBjBxF,uDAAA,GAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAAyF,cAAA,CAAiB;UAe/BxF,uDAAA,GAAgD;UAAhDA,wDAAA,UAAAD,GAAA,CAAAsF,aAAA,kBAAAtF,GAAA,CAAAsF,aAAA,CAAA4B,IAAA,kBAAAlH,GAAA,CAAAsF,aAAA,CAAA4B,IAAA,CAAAC,OAAA,kBAAAnH,GAAA,CAAAsF,aAAA,CAAA4B,IAAA,CAAAC,OAAA,CAAAiE,MAAA,QAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7JlB;;;;;;;;;;ICqDlDnL,4DAAA,cAA2D;IACzDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAe,MAAA,CAAAwL,eAAA,cACF;;;;;IAWAvM,4DAAA,cAAyD;IACvDA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAkB,MAAA,CAAAqL,eAAA,YACF;;;;;IAaAvM,4DAAA,cAAoE;IAClEA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAwM,MAAA,CAAAD,eAAA,uBACF;;;;;IAsCAvM,4DAAA,cAAiE;IAC/DA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA6D,OAAA,CAAA0I,eAAA,oBACF;;;;;IAlCNvM,4DAAA,cAAmD;IAI7CA,uDAAA,2BAKmB;IACrBA,0DAAA,EAAM;IAGNA,4DAAA,cAAuC;IACrCA,uDAAA,2BAKmB;IACrBA,0DAAA,EAAM;IAGNA,4DAAA,cAAmC;IAE/BA,uDAAA,gBAIqB;IAEvBA,0DAAA,EAAM;IACNA,wDAAA,KAAAyM,sCAAA,kBAEM;IACRzM,0DAAA,EAAM;;;;;;IA9BFA,uDAAA,GAAuC;IAAvCA,wDAAA,aAAA0M,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,yBAAAH,OAAA,CAAAxC,KAAA,CAAuC;IAUvClK,uDAAA,GAAyC;IAAzCA,wDAAA,aAAA8M,OAAA,GAAAH,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAC,OAAA,CAAA5C,KAAA,CAAyC;IAiBflK,uDAAA,GAAmC;IAAnCA,wDAAA,SAAA2M,MAAA,CAAAJ,eAAA,eAAmC;;;;;;IAQrEvM,4DAAA,cAAsF;IAK9EA,wDAAA,2BAAA+M,0EAAAC,MAAA;MAAA,MAAAnK,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAC,WAAA,GAAArK,WAAA,CAAAI,SAAA;MAAA,OAAajD,yDAAA,CAAAkN,WAAA,CAAA3G,IAAA,GAAAyG,MAAA,CAC3B;IAAA,EADwC,8BAAAG,6EAAAH,MAAA;MAAA,MAAAnK,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAG,KAAA,GAAAvK,WAAA,CAAAwK,KAAA;MAAA,MAAAC,OAAA,GAAAtN,2DAAA;MAAA,OAGNA,yDAAA,CAAAsN,OAAA,CAAAC,yBAAA,CAAAP,MAAA,EAAAI,KAAA,EAAqC,MAAM,CAAC;IAAA,EAHtC;IAI5BpN,0DAAA,EAAmB;IAIrBA,4DAAA,cAAuC;IAEnCA,wDAAA,2BAAAwN,0EAAAR,MAAA;MAAA,MAAAnK,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAC,WAAA,GAAArK,WAAA,CAAAI,SAAA;MAAA,OAAajD,yDAAA,CAAAkN,WAAA,CAAAzG,EAAA,GAAAuG,MAAA,CAC3B;IAAA,EADsC,8BAAAS,6EAAAT,MAAA;MAAA,MAAAnK,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAG,KAAA,GAAAvK,WAAA,CAAAwK,KAAA;MAAA,MAAAK,OAAA,GAAA1N,2DAAA;MAAA,OAGJA,yDAAA,CAAA0N,OAAA,CAAAH,yBAAA,CAAAP,MAAA,EAAAI,KAAA,EAAqC,IAAI,CAAC;IAAA,EAHtC;IAI1BpN,0DAAA,EAAmB;IAIrBA,4DAAA,cAAmC;IAI7BA,wDAAA,2BAAA2N,+DAAAX,MAAA;MAAA,MAAAnK,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAC,WAAA,GAAArK,WAAA,CAAAI,SAAA;MAAA,OAAajD,yDAAA,CAAAkN,WAAA,CAAA1D,IAAA,GAAAwD,MAAA,CAC7B;IAAA,EAD0C;IAF5BhN,0DAAA,EAIqB;IACrBA,uDAAA,YAA6C;IAC/CA,0DAAA,EAAM;IAIRA,4DAAA,kBAA4E;IAA3BA,wDAAA,mBAAA4N,yDAAA;MAAA,MAAA/K,WAAA,GAAA7C,2DAAA,CAAAiN,IAAA;MAAA,MAAAG,KAAA,GAAAvK,WAAA,CAAAwK,KAAA;MAAA,MAAAQ,OAAA,GAAA7N,2DAAA;MAAA,OAASA,yDAAA,CAAA6N,OAAA,CAAAC,aAAA,CAAAV,KAAA,CAAgB;IAAA,EAAC;IACzEpN,uDAAA,aAA4B;IAC9BA,0DAAA,EAAS;;;;IAhCLA,uDAAA,GAA0B;IAA1BA,wDAAA,YAAAkN,WAAA,CAAA3G,IAAA,CAA0B;IAU1BvG,uDAAA,GAAwB;IAAxBA,wDAAA,YAAAkN,WAAA,CAAAzG,EAAA,CAAwB;IAYtBzG,uDAAA,GAA0B;IAA1BA,wDAAA,YAAAkN,WAAA,CAAA1D,IAAA,CAA0B;;;;;;IAepCxJ,4DAAA,cAAiE;IAClBA,wDAAA,mBAAA+N,wDAAA;MAAA/N,2DAAA,CAAAgO,IAAA;MAAA,MAAAC,OAAA,GAAAjO,2DAAA;MAAA,OAASA,yDAAA,CAAAiO,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACjElO,uDAAA,YAA2B;IAC3BA,oDAAA,mBACF;IAAAA,0DAAA,EAAS;;;;;IAsFPA,4DAAA,iBAAqE;IACnEA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAFqCA,wDAAA,UAAAmO,UAAA,CAAAjE,KAAA,CAAsB;IAClElK,uDAAA,GACF;IADEA,gEAAA,MAAAmO,UAAA,CAAAC,KAAA,MACF;;;;;IAkBMpO,4DAAA,iBAAmE;IACjEA,oDAAA,GACF;IAAAA,0DAAA,EAAS;;;;IAFmCA,wDAAA,UAAAqO,UAAA,CAAAnE,KAAA,CAAsB;IAChElK,uDAAA,GACF;IADEA,gEAAA,MAAAqO,UAAA,CAAAD,KAAA,MACF;;;;;IAJJpO,4DAAA,eAAiD;IAE7CA,wDAAA,IAAAsO,0CAAA,qBAES;IACXtO,0DAAA,EAAS;;;;IAHoBA,uDAAA,GAAe;IAAfA,wDAAA,YAAAuO,MAAA,CAAAC,YAAA,CAAe;;;;;IAiBpDxO,4DAAA,WAAyB;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAO;;;;;IAC1CA,4DAAA,WAAwB;IACtBA,uDAAA,YAAsC;IAACA,oDAAA,qBACzC;IAAAA,0DAAA,EAAO;;;;;;IAcXA,4DAAA,cAGqC;IAAnCA,wDAAA,mBAAAyO,sDAAA;MAAA,MAAA5L,WAAA,GAAA7C,2DAAA,CAAA0O,IAAA;MAAA,MAAAC,UAAA,GAAA9L,WAAA,CAAAI,SAAA;MAAA,MAAA2L,OAAA,GAAA5O,2DAAA;MAAA,OAASA,yDAAA,CAAA4O,OAAA,CAAAC,gBAAA,CAAAF,UAAA,CAAwB;IAAA,EAAC;IAClC3O,4DAAA,cAAyB;IACvBA,uDAAA,WAA4B;IAC9BA,0DAAA,EAAM;IACNA,4DAAA,cAA4B;IAExBA,oDAAA,oBAAY;IAAAA,4DAAA,aAAQ;IAAAA,oDAAA,GAAiB;IAAAA,0DAAA,EAAS;IAACA,oDAAA,UAAE;IAAAA,4DAAA,aAAQ;IAAAA,oDAAA,IAAe;IAAAA,0DAAA,EAAS;IAACA,oDAAA,IACpF;;IAAAA,0DAAA,EAAM;;;;IADgBA,uDAAA,GAAiB;IAAjBA,+DAAA,CAAA2O,UAAA,CAAApI,IAAA,CAAiB;IAAoBvG,uDAAA,GAAe;IAAfA,+DAAA,CAAA2O,UAAA,CAAAlI,EAAA,CAAe;IAAUzG,uDAAA,GACpF;IADoFA,gEAAA,SAAAA,yDAAA,QAAA2O,UAAA,CAAAnF,IAAA,sBACpF;;;;;IAKJxJ,4DAAA,cAA4E;IAC1EA,uDAAA,YAA6B;IAC7BA,4DAAA,QAAG;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAI;IACzBA,4DAAA,YAAO;IAAAA,oDAAA,mDAA4C;IAAAA,0DAAA,EAAQ;;;;;;IAK/DA,4DAAA,cAA6E;IACnCA,wDAAA,mBAAA+O,yDAAA;MAAA/O,2DAAA,CAAAgP,IAAA;MAAA,MAAAC,OAAA,GAAAjP,2DAAA;MAAA,OAASA,yDAAA,CAAAiP,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IACrElP,uDAAA,YAA4B;IAACA,oDAAA,kBAC/B;IAAAA,0DAAA,EAAS;;;ADlUX,MAAOxB,eAAe;EAmC1Be,YACU4F,EAAe,EACfC,aAA4B,EAC5BhE,WAAwB,EACxBC,MAAc;IAHd,KAAA8D,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAhE,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IArChB,KAAAiE,SAAS,GAAG,KAAK;IACjB,KAAAtE,WAAW,GAAQ,IAAI;IAGvB;IACA,KAAAmJ,QAAQ,GAAG,QAAQ;IACnB,KAAAgF,cAAc,GAAG,KAAK;IACtB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,kBAAkB,GAAoB,EAAE;IAExC;IACA,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,WAAW,GAAG,CAAC;IAEf;IACA,KAAAC,aAAa,GAAG,SAAS;IAEzB;IACA,KAAAC,cAAc,GAAG,CACf;MAAExF,KAAK,EAAE,KAAK;MAAEkE,KAAK,EAAE;IAAS,CAAE,EAClC;MAAElE,KAAK,EAAE,MAAM;MAAEkE,KAAK,EAAE;IAAM,CAAE,EAChC;MAAElE,KAAK,EAAE,MAAM;MAAEkE,KAAK,EAAE;IAAM,CAAE,EAChC;MAAElE,KAAK,EAAE,OAAO;MAAEkE,KAAK,EAAE;IAAO,CAAE,CACnC;IAED;IACA,KAAAI,YAAY,GAAG,CACb;MAAEtE,KAAK,EAAE,GAAG;MAAEkE,KAAK,EAAE;IAAY,CAAE,EACnC;MAAElE,KAAK,EAAE,GAAG;MAAEkE,KAAK,EAAE;IAAY,CAAE,EACnC;MAAElE,KAAK,EAAE,GAAG;MAAEkE,KAAK,EAAE;IAAY,CAAE,CACpC;IAQC,IAAI,CAACxB,UAAU,GAAG,IAAI,CAAC+C,UAAU,EAAE;IACnC,IAAI,CAACC,eAAe,GAAG,IAAI,CAACxK,aAAa,CAACwK,eAAe;EAC3D;EAEAtO,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,WAAW,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACT,WAAW,GAAGS,IAAI;IACzB,CAAC,CAAC;IAEF,IAAI,CAAC,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE,EAAE;MACvC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;MACjC;;IAGF;IACA,IAAI,CAACkO,eAAe,EAAE;EACxB;EAEA;;;EAGQF,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACxK,EAAE,CAACmB,KAAK,CAAC;MACnB6D,QAAQ,EAAE,CAAC,QAAQ,EAAExH,sDAAU,CAAC6D,QAAQ,CAAC;MACzCD,IAAI,EAAE,CAAC,EAAE,EAAE5D,sDAAU,CAAC6D,QAAQ,CAAC;MAC/BC,EAAE,EAAE,CAAC,EAAE,EAAE9D,sDAAU,CAAC6D,QAAQ,CAAC;MAC7BE,aAAa,EAAE,CAAC,EAAE,EAAE/D,sDAAU,CAAC6D,QAAQ,CAAC;MACxCsJ,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBnJ,MAAM,EAAE,CAAC,CAAC,EAAE,CAAChE,sDAAU,CAAC6D,QAAQ,EAAE7D,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAClE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCE,OAAO,EAAE,CAAC,CAAC,EAAE,CAACnE,sDAAU,CAACiE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACjCwD,KAAK,EAAE,CAAC,SAAS,EAAEzH,sDAAU,CAAC6D,QAAQ,CAAC;MACvCuJ,gBAAgB,EAAE,CAAC,EAAE,CAAC;MACtB1F,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,eAAe,EAAE,CAAC,KAAK,CAAC;MACxBC,OAAO,EAAE,CAAC,KAAK,CAAC;MAChBC,QAAQ,EAAE,CAAC,KAAK,CAAC;MACjBgE,YAAY,EAAE,CAAC,GAAG;KACnB,CAAC;EACJ;EAEA;;;EAGQqB,eAAeA,CAAA;IACrB,MAAMG,KAAK,GAAG,IAAIvG,IAAI,EAAE;IACxB,MAAMwG,QAAQ,GAAG,IAAIxG,IAAI,CAACuG,KAAK,CAAC;IAChCC,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,MAAMC,QAAQ,GAAG,IAAI3G,IAAI,CAACuG,KAAK,CAAC;IAChCI,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAExC,IAAI,CAACvD,UAAU,CAAC7F,UAAU,CAAC;MACzBL,aAAa,EAAE,IAAI,CAAC2J,UAAU,CAACJ,QAAQ,CAAC;MACxCH,UAAU,EAAE,IAAI,CAACO,UAAU,CAACD,QAAQ;KACrC,CAAC;EACJ;EAEA;;;EAGQC,UAAUA,CAAC7G,IAAU;IAC3B,OAAOA,IAAI,CAAC8G,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACzC;EAEA;;;EAGAC,gBAAgBA,CAACC,IAAY;IAC3B,IAAI,CAACtG,QAAQ,GAAGsG,IAAI;IACpB,IAAI,CAACtB,cAAc,GAAGsB,IAAI,KAAK,WAAW;IAE1C,IAAI,CAAC7D,UAAU,CAAC7F,UAAU,CAAC;MAAEoD,QAAQ,EAAEsG;IAAI,CAAE,CAAC;IAE9C,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAAC7D,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE6D,aAAa,CAAC,CAAC/N,sDAAU,CAAC6D,QAAQ,CAAC,CAAC;KACxE,MAAM;MACL,IAAI,CAACoG,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE8D,eAAe,EAAE;;IAEtD,IAAI,CAAC/D,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE+D,sBAAsB,EAAE;IAE3D;IACA,IAAIH,IAAI,KAAK,WAAW,EAAE;MACxB,IAAI,CAACpB,kBAAkB,GAAG,EAAE;;EAEhC;EAEA;;;EAGAnB,UAAUA,CAAA;IACR,MAAM2C,UAAU,GAAkB;MAChCtK,IAAI,EAAE,EAAE;MACRE,EAAE,EAAE,EAAE;MACN+C,IAAI,EAAE;KACP;IACD,IAAI,CAAC6F,kBAAkB,CAAC5H,IAAI,CAACoJ,UAAU,CAAC;EAC1C;EAEA;;;EAGA/C,aAAaA,CAACT,KAAa;IACzB,IAAI,CAACgC,kBAAkB,CAACyB,MAAM,CAACzD,KAAK,EAAE,CAAC,CAAC;EAC1C;EAEA;;;EAGA0D,sBAAsBA,CAACC,QAA8B;IACnD,IAAI,CAACpE,UAAU,CAAC7F,UAAU,CAAC;MAAER,IAAI,EAAEyK,QAAQ,CAACC;IAAW,CAAE,CAAC;EAC5D;EAEAC,oBAAoBA,CAACF,QAA8B;IACjD,IAAI,CAACpE,UAAU,CAAC7F,UAAU,CAAC;MAAEN,EAAE,EAAEuK,QAAQ,CAACC;IAAW,CAAE,CAAC;EAC1D;EAEA;;;EAGA1D,yBAAyBA,CAACyD,QAA8B,EAAEG,YAAoB,EAAEC,KAAoB;IAClG,IAAI,IAAI,CAAC/B,kBAAkB,CAAC8B,YAAY,CAAC,EAAE;MACzC,IAAI,CAAC9B,kBAAkB,CAAC8B,YAAY,CAAC,CAACC,KAAK,CAAC,GAAGJ,QAAQ,CAACC,WAAW;;EAEvE;EAEA;;;EAGAI,oBAAoBA,CAACZ,IAAuC,EAAEa,SAAkB;IAC9E,MAAMC,YAAY,GAAG,IAAI,CAAC3E,UAAU,CAACC,GAAG,CAAC4D,IAAI,CAAC,EAAEvG,KAAK,IAAI,CAAC;IAC1D,IAAIsH,QAAQ,GAAGF,SAAS,GAAGC,YAAY,GAAG,CAAC,GAAGpI,IAAI,CAACsI,GAAG,CAAC,CAAC,EAAEF,YAAY,GAAG,CAAC,CAAC;IAE3E;IACA,IAAId,IAAI,KAAK,QAAQ,IAAIe,QAAQ,GAAG,CAAC,EAAE;MACrCA,QAAQ,GAAG,CAAC;;IAGd,IAAI,CAAC5E,UAAU,CAAC7F,UAAU,CAAC;MAAE,CAAC0J,IAAI,GAAGe;IAAQ,CAAE,CAAC;IAEhD;IACA,IAAIf,IAAI,KAAK,QAAQ,EAAE,IAAI,CAACnB,UAAU,GAAGkC,QAAQ;IACjD,IAAIf,IAAI,KAAK,UAAU,EAAE,IAAI,CAAClB,UAAU,GAAGiC,QAAQ;IACnD,IAAIf,IAAI,KAAK,SAAS,EAAE,IAAI,CAACjB,WAAW,GAAGgC,QAAQ;EACrD;EAEA;;;EAGAE,kBAAkBA,CAAA;IAChB,MAAM/K,MAAM,GAAG,IAAI,CAACiG,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAE3C,KAAK,IAAI,CAAC;IACxD,MAAMrD,QAAQ,GAAG,IAAI,CAAC+F,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAE3C,KAAK,IAAI,CAAC;IAC5D,MAAMpD,OAAO,GAAG,IAAI,CAAC8F,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAE3C,KAAK,IAAI,CAAC;IAC1D,OAAOvD,MAAM,GAAGE,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGA6K,aAAaA,CAACC,WAAmB;IAC/B,IAAI,CAACnC,aAAa,GAAGmC,WAAW;IAChC,IAAI,CAAChF,UAAU,CAAC7F,UAAU,CAAC;MAAEqD,KAAK,EAAEwH;IAAW,CAAE,CAAC;EACpD;EAEA;;;EAGAC,cAAcA,CAAA;IACZ,IAAI,CAACzC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtC,IAAI,CAACxC,UAAU,CAAC7F,UAAU,CAAC;MAAEyD,QAAQ,EAAE,IAAI,CAAC4E;IAAY,CAAE,CAAC;EAC7D;EAEA;;;EAGA0C,aAAaA,CAAA;IACX,MAAMvL,IAAI,GAAG,IAAI,CAACqG,UAAU,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE3C,KAAK;IAC/C,MAAMzD,EAAE,GAAG,IAAI,CAACmG,UAAU,CAACC,GAAG,CAAC,IAAI,CAAC,EAAE3C,KAAK;IAE3C,IAAI,CAAC0C,UAAU,CAAC7F,UAAU,CAAC;MACzBR,IAAI,EAAEE,EAAE;MACRA,EAAE,EAAEF;KACL,CAAC;EACJ;EAEA;;;EAGAwL,QAAQA,CAAA;IACN,IAAI,IAAI,CAACnF,UAAU,CAAC5C,KAAK,EAAE;MACzB,IAAI,CAAC1E,SAAS,GAAG,IAAI;MAErB,MAAM2E,SAAS,GAAG,IAAI,CAAC2C,UAAU,CAAC1C,KAAK;MACvC,MAAMpE,UAAU,GAAqB;QACnCqE,QAAQ,EAAEF,SAAS,CAACE,QAAQ;QAC5B5D,IAAI,EAAE0D,SAAS,CAAC1D,IAAI;QACpBE,EAAE,EAAEwD,SAAS,CAACxD,EAAE;QAChBC,aAAa,EAAEuD,SAAS,CAACvD,aAAa;QACtCoJ,UAAU,EAAE7F,SAAS,CAAC6F,UAAU;QAChC9I,UAAU,EAAE;UACVL,MAAM,EAAEsD,SAAS,CAACtD,MAAM;UACxBE,QAAQ,EAAEoD,SAAS,CAACpD,QAAQ;UAC5BC,OAAO,EAAEmD,SAAS,CAACnD;SACpB;QACDsD,KAAK,EAAEH,SAAS,CAACG,KAAK;QACtB2F,gBAAgB,EAAE9F,SAAS,CAAC8F,gBAAgB;QAC5C1F,aAAa,EAAEJ,SAAS,CAACI,aAAa;QACtCC,eAAe,EAAEL,SAAS,CAACK,eAAe;QAC1CC,OAAO,EAAEN,SAAS,CAACM,OAAO;QAC1BC,QAAQ,EAAEP,SAAS,CAACO;OACrB;MAED;MACA,IAAI,CAACpF,aAAa,CAAC4M,gBAAgB,CAAClM,UAAU,CAAC;MAE/C;MACA,IAAImM,gBAAgB;MAEpB,QAAQnM,UAAU,CAACqE,QAAQ;QACzB,KAAK,QAAQ;UACX8H,gBAAgB,GAAG,IAAI,CAAC7M,aAAa,CAACqF,mBAAmB,CAAC3E,UAAU,CAAC;UACrE;QACF,KAAK,WAAW;UACdmM,gBAAgB,GAAG,IAAI,CAAC7M,aAAa,CAAC8M,sBAAsB,CAACpM,UAAU,CAAC;UACxE;QACF,KAAK,WAAW;UACdmM,gBAAgB,GAAG,IAAI,CAAC7M,aAAa,CAAC+M,sBAAsB,CAACrM,UAAU,CAAC;UACxE;QACF;UACEmM,gBAAgB,GAAG,IAAI,CAAC7M,aAAa,CAACqF,mBAAmB,CAAC3E,UAAU,CAAC;;MAGzEmM,gBAAgB,CAACzQ,SAAS,CAAC;QACzBkJ,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACrF,SAAS,GAAG,KAAK;UACtB,IAAIqF,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3BE,OAAO,CAACqH,GAAG,CAAC,wBAAwB,EAAEzH,QAAQ,CAAC;YAC/C;YACA,IAAI,CAACtJ,MAAM,CAACM,QAAQ,CAAC,CAAC,iBAAiB,CAAC,EAAE;cACxCwE,KAAK,EAAE;gBACLkM,OAAO,EAAE1H,QAAQ;gBACjB2H,YAAY,EAAExM;;aAEjB,CAAC;WACH,MAAM;YACLiF,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEH,QAAQ,CAACC,MAAM,CAAC2H,QAAQ,CAAC;YACzD;;QAEJ,CAAC;;QACDzH,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACxF,SAAS,GAAG,KAAK;UACtByF,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACrC;QACF;OACD,CAAC;KACH,MAAM;MACL;MACA0H,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7F,UAAU,CAAC8F,QAAQ,CAAC,CAACvL,OAAO,CAACwL,GAAG,IAAG;QAClD,IAAI,CAAC/F,UAAU,CAACC,GAAG,CAAC8F,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC3C,CAAC,CAAC;;EAEN;EAEA;;;EAGA/D,gBAAgBA,CAACgE,MAAoB;IACnC,IAAI,CAACjG,UAAU,CAAC7F,UAAU,CAAC;MACzBR,IAAI,EAAEsM,MAAM,CAACtM,IAAI;MACjBE,EAAE,EAAEoM,MAAM,CAACpM,EAAE;MACbC,aAAa,EAAEmM,MAAM,CAACrJ,IAAI;MAC1B7C,MAAM,EAAEkM,MAAM,CAAC7L,UAAU;MACzBH,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE;KACV,CAAC;EACJ;EAEA;;;EAGAoI,mBAAmBA,CAAA;IACjB,IAAI,CAAC9J,aAAa,CAAC8J,mBAAmB,EAAE;EAC1C;EAEA;;;EAGA3C,eAAeA,CAACuG,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACnG,UAAU,CAACC,GAAG,CAACiG,WAAW,CAAC;IAChD,IAAIC,OAAO,EAAEC,MAAM,IAAID,OAAO,CAACE,OAAO,EAAE;MACtC,IAAIF,OAAO,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC9B,OAAO,GAAGF,WAAW,cAAc;;MAErC,IAAIC,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACzB,OAAO,oBAAoBD,OAAO,CAACC,MAAM,CAAC,KAAK,CAAC,CAACpM,GAAG,EAAE;;;IAG1D,OAAO,EAAE;EACX;;;uBApVWpI,eAAe,EAAAwB,+DAAA,CAAAZ,uDAAA,GAAAY,+DAAA,CAAAgC,mEAAA,GAAAhC,+DAAA,CAAA0L,+DAAA,GAAA1L,+DAAA,CAAAkT,mDAAA;IAAA;EAAA;;;YAAf1U,eAAe;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAuC,MAAA;MAAAtC,QAAA,WAAAuT,yBAAArT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpB5BE,4DAAA,aAA8B;UAOpBA,uDAAA,WAA4B;UAC5BA,4DAAA,SAAI;UAAAA,oDAAA,8BAAuB;UAAAA,0DAAA,EAAK;UAElCA,4DAAA,WAA2B;UAAAA,oDAAA,gDAAyC;UAAAA,0DAAA,EAAI;UAG1EA,4DAAA,eAA2E;UAA5CA,wDAAA,sBAAAoT,mDAAA;YAAA,OAAYrT,GAAA,CAAAgS,QAAA,EAAU;UAAA,EAAC;UAEpD/R,4DAAA,cAAgC;UAK5BA,wDAAA,mBAAAqT,kDAAA;YAAA,OAAStT,GAAA,CAAAyQ,gBAAA,CAAiB,QAAQ,CAAC;UAAA,EAAC;UACpCxQ,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,iBAI0C;UAAxCA,wDAAA,mBAAAsT,kDAAA;YAAA,OAASvT,GAAA,CAAAyQ,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvCxQ,oDAAA,oBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,iBAI0C;UAAxCA,wDAAA,mBAAAuT,kDAAA;YAAA,OAASxT,GAAA,CAAAyQ,gBAAA,CAAiB,WAAW,CAAC;UAAA,EAAC;UACvCxQ,oDAAA,+BACF;UAAAA,0DAAA,EAAS;UAIXA,4DAAA,eAAmC;UAYvBA,wDAAA,8BAAAwT,uEAAAxG,MAAA;YAAA,OAAoBjN,GAAA,CAAAgR,sBAAA,CAAA/D,MAAA,CAA8B;UAAA,EAAC;UACrDhN,0DAAA,EAAmB;UACnBA,wDAAA,KAAAyT,+BAAA,kBAEM;UACRzT,0DAAA,EAAM;UAGNA,4DAAA,eAAuC;UAKnCA,wDAAA,8BAAA0T,uEAAA1G,MAAA;YAAA,OAAoBjN,GAAA,CAAAmR,oBAAA,CAAAlE,MAAA,CAA4B;UAAA,EAAC;UACnDhN,0DAAA,EAAmB;UACnBA,wDAAA,KAAA2T,+BAAA,kBAEM;UACR3T,0DAAA,EAAM;UAGNA,4DAAA,eAAmC;UAE/BA,uDAAA,iBAIqB;UAEvBA,0DAAA,EAAM;UACNA,wDAAA,KAAA4T,+BAAA,kBAEM;UACR5T,0DAAA,EAAM;UAKVA,wDAAA,KAAA6T,+BAAA,mBAqCM;UAGN7T,wDAAA,KAAA8T,+BAAA,mBAuCM;UAGN9T,wDAAA,KAAA+T,+BAAA,kBAKM;UACR/T,0DAAA,EAAM;UAIRA,4DAAA,eAAqC;UAG1BA,oDAAA,mCAA2B;UAAAA,0DAAA,EAAQ;UAC1CA,4DAAA,eAAgC;UAGCA,uDAAA,aAA2B;UAAAA,0DAAA,EAAO;UAC/DA,4DAAA,gBAA8B;UAAAA,oDAAA,IAA0C;UAAAA,0DAAA,EAAO;UAC/EA,4DAAA,eAA8B;UACcA,wDAAA,mBAAAgU,kDAAA;YAAA,OAASjU,GAAA,CAAAsR,oBAAA,CAAqB,QAAQ,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UACpGA,4DAAA,kBAAyF;UAA/CA,wDAAA,mBAAAiU,kDAAA;YAAA,OAASlU,GAAA,CAAAsR,oBAAA,CAAqB,QAAQ,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UAKvGA,4DAAA,eAA4B;UACGA,uDAAA,aAA4B;UAAAA,0DAAA,EAAO;UAChEA,4DAAA,gBAA8B;UAAAA,oDAAA,IAA4C;UAAAA,0DAAA,EAAO;UACjFA,4DAAA,eAA8B;UACcA,wDAAA,mBAAAkU,kDAAA;YAAA,OAASnU,GAAA,CAAAsR,oBAAA,CAAqB,UAAU,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UACtGA,4DAAA,kBAA2F;UAAjDA,wDAAA,mBAAAmU,kDAAA;YAAA,OAASpU,GAAA,CAAAsR,oBAAA,CAAqB,UAAU,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UAKzGA,4DAAA,eAA4B;UACGA,uDAAA,aAA2B;UAAAA,0DAAA,EAAO;UAC/DA,4DAAA,gBAA8B;UAAAA,oDAAA,IAA2C;UAAAA,0DAAA,EAAO;UAChFA,4DAAA,eAA8B;UACcA,wDAAA,mBAAAoU,kDAAA;YAAA,OAASrU,GAAA,CAAAsR,oBAAA,CAAqB,SAAS,EAAE,KAAK,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UACrGA,4DAAA,kBAA0F;UAAhDA,wDAAA,mBAAAqU,kDAAA;YAAA,OAAStU,GAAA,CAAAsR,oBAAA,CAAqB,SAAS,EAAE,IAAI,CAAC;UAAA,EAAC;UAACrR,oDAAA,SAAC;UAAAA,0DAAA,EAAS;UAKxGA,4DAAA,eAA6B;UAKzBA,wDAAA,mBAAAsU,kDAAA;YAAA,OAASvU,GAAA,CAAA4R,aAAA,CAAc,SAAS,CAAC;UAAA,EAAC;UAClC3R,uDAAA,aAA4B;UAACA,oDAAA,iBAC/B;UAAAA,0DAAA,EAAS;UAMfA,4DAAA,eAAsC;UACNA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAQ;UACvDA,4DAAA,kBAAwF;UACrEA,oDAAA,yBAAiB;UAAAA,0DAAA,EAAS;UAC3CA,4DAAA,kBAAmB;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAS;UAC5CA,4DAAA,kBAAmB;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAS;UACtCA,4DAAA,kBAAmB;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAS;UACrCA,4DAAA,kBAAmB;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAS;UACpCA,4DAAA,kBAAmB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAS;UAM/CA,4DAAA,eAAgC;UAGAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAQ;UACpDA,4DAAA,eAA6B;UAEHA,oDAAA,eAAO;UAAAA,0DAAA,EAAS;UACtCA,4DAAA,kBAAqB;UAAAA,oDAAA,wBAAe;UAAAA,0DAAA,EAAS;UAMnDA,4DAAA,gBAA0B;UACIA,oDAAA,gBAAO;UAAAA,0DAAA,EAAQ;UAC3CA,4DAAA,gBAA6B;UAEzBA,wDAAA,MAAAuU,mCAAA,qBAES;UACXvU,0DAAA,EAAS;UAKbA,4DAAA,gBAA0B;UACIA,oDAAA,iBAAQ;UAAAA,0DAAA,EAAQ;UAC5CA,4DAAA,gBAA6B;UAMvBA,wDAAA,oBAAAwU,mDAAA;YAAA,OAAUzU,GAAA,CAAA8R,cAAA,EAAgB;UAAA,EAAC;UAJ7B7R,0DAAA,EAI8B;UAC9BA,4DAAA,kBAA6C;UAC3CA,wDAAA,MAAAyU,iCAAA,mBAMO;UACTzU,0DAAA,EAAQ;UAOhBA,4DAAA,gBAAmC;UAK/BA,wDAAA,MAAA0U,iCAAA,mBAA0C;UAC1C1U,wDAAA,MAAA2U,iCAAA,mBAEO;UACT3U,0DAAA,EAAS;UAMfA,4DAAA,gBAAmC;UAE3BA,oDAAA,wBAAe;UAAAA,0DAAA,EAAK;UACxBA,4DAAA,UAAG;UAAAA,oDAAA,kDAAyC;UAAAA,0DAAA,EAAI;UAGlDA,4DAAA,gBAA2B;UACzBA,wDAAA,MAAA4U,gCAAA,mBAYM;;UAGN5U,wDAAA,MAAA6U,gCAAA,kBAIM;;UACR7U,0DAAA,EAAM;UAGNA,wDAAA,MAAA8U,gCAAA,kBAIM;;UACR9U,0DAAA,EAAM;;;;;;;;UA3UEA,uDAAA,IAAwB;UAAxBA,wDAAA,cAAAD,GAAA,CAAA6M,UAAA,CAAwB;UAMxB5M,uDAAA,GAAsC;UAAtCA,yDAAA,WAAAD,GAAA,CAAAoK,QAAA,cAAsC;UAOtCnK,uDAAA,GAAyC;UAAzCA,yDAAA,WAAAD,GAAA,CAAAoK,QAAA,iBAAyC;UAOzCnK,uDAAA,GAAyC;UAAzCA,yDAAA,WAAAD,GAAA,CAAAoK,QAAA,iBAAyC;UAqBPnK,uDAAA,GAA6B;UAA7BA,wDAAA,SAAAD,GAAA,CAAAwM,eAAA,SAA6B;UAa7BvM,uDAAA,GAA2B;UAA3BA,wDAAA,SAAAD,GAAA,CAAAwM,eAAA,OAA2B;UAe3BvM,uDAAA,GAAsC;UAAtCA,wDAAA,SAAAD,GAAA,CAAAwM,eAAA,kBAAsC;UAQ3CvM,uDAAA,GAAoB;UAApBA,wDAAA,SAAAD,GAAA,CAAAoP,cAAA,CAAoB;UAwCDnP,uDAAA,GAAuB;UAAvBA,wDAAA,YAAAD,GAAA,CAAAsP,kBAAA,CAAuB;UA0CtCrP,uDAAA,GAA8B;UAA9BA,wDAAA,SAAAD,GAAA,CAAAoK,QAAA,iBAA8B;UAkB7BnK,uDAAA,IAA0C;UAA1CA,+DAAA,GAAA+U,QAAA,GAAAhV,GAAA,CAAA6M,UAAA,CAAAC,GAAA,6BAAAkI,QAAA,CAAA7K,KAAA,OAA0C;UAU1ClK,uDAAA,IAA4C;UAA5CA,+DAAA,GAAAgV,QAAA,GAAAjV,GAAA,CAAA6M,UAAA,CAAAC,GAAA,+BAAAmI,QAAA,CAAA9K,KAAA,OAA4C;UAU5ClK,uDAAA,IAA2C;UAA3CA,+DAAA,GAAAiV,QAAA,GAAAlV,GAAA,CAAA6M,UAAA,CAAAC,GAAA,8BAAAoI,QAAA,CAAA/K,KAAA,OAA2C;UAYvElK,uDAAA,GAA4C;UAA5CA,yDAAA,WAAAD,GAAA,CAAA0P,aAAA,eAA4C;UAwCnBzP,uDAAA,IAAiB;UAAjBA,wDAAA,YAAAD,GAAA,CAAA2P,cAAA,CAAiB;UAkBb1P,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAD,GAAA,CAAAqP,YAAA,CAAkB;UAkBrDpP,uDAAA,GAA2C;UAA3CA,wDAAA,aAAAD,GAAA,CAAAuF,SAAA,KAAAvF,GAAA,CAAA6M,UAAA,CAAA5C,KAAA,CAA2C;UACpChK,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAAuF,SAAA,CAAgB;UAChBtF,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAAuF,SAAA,CAAe;UAiBLtF,uDAAA,GAA0B;UAA1BA,wDAAA,YAAAA,yDAAA,UAAAD,GAAA,CAAA6P,eAAA,EAA0B;UAczC5P,uDAAA,GAA6C;UAA7CA,wDAAA,WAAAmV,QAAA,GAAAnV,yDAAA,UAAAD,GAAA,CAAA6P,eAAA,oBAAAuF,QAAA,CAAAhK,MAAA,QAA6C;UAQtBnL,uDAAA,GAA4C;UAA5CA,wDAAA,WAAAoV,QAAA,GAAApV,yDAAA,UAAAD,GAAA,CAAA6P,eAAA,oBAAAwF,QAAA,CAAAjK,MAAA,MAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnV4C;AACpD;AAC9B;AAC+C;;;;;;;ICDtFnL,uDAAA,WAAsD;;;;IAAtCA,wDAAA,CAAAe,MAAA,CAAA+U,IAAA,CAAc;;;;;IAa9B9V,4DAAA,aAA+C;IAC7CA,uDAAA,WAAsC;IACxCA,0DAAA,EAAM;;;;;IAkBAA,4DAAA,cAA4F;IAC1FA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA+V,aAAA,CAAAC,OAAA,MACF;;;;;;IAfJhW,4DAAA,cAKmC;IADjCA,wDAAA,mBAAAiW,gEAAA;MAAA,MAAApT,WAAA,GAAA7C,2DAAA,CAAAkW,IAAA;MAAA,MAAAH,aAAA,GAAAlT,WAAA,CAAAI,SAAA;MAAA,MAAAkT,OAAA,GAAAnW,2DAAA;MAAA,OAASA,yDAAA,CAAAmW,OAAA,CAAAC,gBAAA,CAAAL,aAAA,CAA4B;IAAA,EAAC,wBAAAM,qEAAA;MAAA,MAAAxT,WAAA,GAAA7C,2DAAA,CAAAkW,IAAA;MAAA,MAAAI,IAAA,GAAAzT,WAAA,CAAAwK,KAAA;MAAA,MAAAkJ,OAAA,GAAAvW,2DAAA;MAAA,OAAAA,yDAAA,CAAAuW,OAAA,CAAAC,aAAA,GAAAF,IAAA;IAAA;IAGtCtW,4DAAA,cAA6B;IAC3BA,uDAAA,QAAsD;IACxDA,0DAAA,EAAM;IAENA,4DAAA,cAAgC;IACDA,oDAAA,GAA4B;IAAAA,0DAAA,EAAM;IAC/DA,wDAAA,IAAAyW,gDAAA,kBAEM;IACRzW,0DAAA,EAAM;IAENA,4DAAA,cAA6B;IAEzBA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;;;IAlBTA,yDAAA,aAAAsW,IAAA,KAAAnS,MAAA,CAAAqS,aAAA,CAAsC;IAKjCxW,uDAAA,GAA8C;IAA9CA,wDAAA,CAAAmE,MAAA,CAAAuS,mBAAA,CAAAX,aAAA,CAAAtF,IAAA,EAA8C;IAIpBzQ,uDAAA,GAA4B;IAA5BA,+DAAA,CAAA+V,aAAA,CAAA9E,WAAA,CAA4B;IACnDjR,uDAAA,GAAyD;IAAzDA,wDAAA,SAAA+V,aAAA,CAAAtF,IAAA,kBAAAsF,aAAA,CAAAC,OAAA,CAAyD;IAMtChW,uDAAA,GAAmC;IAAnCA,wDAAA,WAAA+V,aAAA,CAAAtF,IAAA,CAAmC;IAC1DzQ,uDAAA,GACF;IADEA,gEAAA,MAAA+V,aAAA,CAAAtF,IAAA,MACF;;;;;IAKNzQ,4DAAA,cAAuE;IACrEA,uDAAA,YAA6B;IAC7BA,4DAAA,WAAM;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAO;;;;;IA9BnCA,4DAAA,cAA0D;IAEtDA,wDAAA,IAAA2W,0CAAA,mBAuBM;IACR3W,0DAAA,EAAM;IAENA,wDAAA,IAAA4W,0CAAA,kBAGM;IACR5W,0DAAA,EAAM;;;;IA7BuBA,uDAAA,GAAgB;IAAhBA,wDAAA,YAAA2M,MAAA,CAAAkK,WAAA,CAAgB;IAyBrC7W,uDAAA,GAA4C;IAA5CA,wDAAA,SAAA2M,MAAA,CAAAkK,WAAA,CAAA1L,MAAA,WAAAwB,MAAA,CAAArH,SAAA,CAA4C;;;AD9BhD,MAAOhF,qBAAqB;EAmBhCf,YACUuX,mBAAwC,EACxCC,UAAsB;IADtB,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,UAAU,GAAVA,UAAU;IApBX,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAlB,IAAI,GAAW,EAAE;IACjB,KAAAmB,QAAQ,GAAY,KAAK;IACxB,KAAAC,gBAAgB,GAAG,IAAI7B,uDAAY,EAAwB;IAIrE,KAAAnL,KAAK,GAAW,EAAE;IAClB,KAAA2M,WAAW,GAA2B,EAAE;IACxC,KAAAM,eAAe,GAAY,KAAK;IAChC,KAAAX,aAAa,GAAW,CAAC,CAAC;IAC1B,KAAAlR,SAAS,GAAY,KAAK;IAElB,KAAA8R,aAAa,GAAG,IAAI5B,yCAAO,EAAU;IACrC,KAAA6B,QAAQ,GAAG,IAAI7B,yCAAO,EAAQ;IAC9B,KAAA8B,QAAQ,GAAIpN,KAAa,IAAI,CAAE,CAAC;IAChC,KAAAqN,SAAS,GAAG,MAAK,CAAE,CAAC;EAKzB;EAEHjW,QAAQA,CAAA;IACN;IACA,IAAI,CAAC8V,aAAa,CAACI,IAAI,CACrB/B,4DAAY,CAAC,GAAG,CAAC,EACjBC,oEAAoB,EAAE,EACtBC,yDAAS,CAAC8B,KAAK,IAAI,IAAI,CAACX,mBAAmB,CAACY,eAAe,CAACD,KAAK,CAAC,CAAC,EACnE7B,yDAAS,CAAC,IAAI,CAACyB,QAAQ,CAAC,CACzB,CAAC7V,SAAS,CAACqV,WAAW,IAAG;MACxB,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAACM,eAAe,GAAGN,WAAW,CAAC1L,MAAM,GAAG,CAAC;MAC7C,IAAI,CAAC7F,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC;IAEF;IACAqS,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACrE;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACV,QAAQ,CAAC3M,IAAI,EAAE;IACpB,IAAI,CAAC2M,QAAQ,CAACW,QAAQ,EAAE;IACxBL,QAAQ,CAACM,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACJ,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EACxE;EAEA;EACAI,UAAUA,CAAChO,KAAa;IACtB,IAAI,CAACA,KAAK,GAAGA,KAAK,IAAI,EAAE;IACxB,IAAI,IAAI,CAACiO,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAAClO,KAAK,GAAG,IAAI,CAACA,KAAK;;EAEtD;EAEAmO,gBAAgBA,CAACC,EAA2B;IAC1C,IAAI,CAAChB,QAAQ,GAAGgB,EAAE;EACpB;EAEAC,iBAAiBA,CAACD,EAAc;IAC9B,IAAI,CAACf,SAAS,GAAGe,EAAE;EACrB;EAEAE,gBAAgBA,CAACC,UAAmB;IAClC,IAAI,IAAI,CAACN,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAACM,QAAQ,GAAGD,UAAU;;EAEzD;EAEAE,OAAOA,CAACC,KAAY;IAClB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA0B;IAC/C,IAAI,CAAC3O,KAAK,GAAG2O,MAAM,CAAC3O,KAAK;IACzB,IAAI,CAACoN,QAAQ,CAAC,IAAI,CAACpN,KAAK,CAAC;IAEzB,IAAI,IAAI,CAACA,KAAK,CAACiB,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAAC7F,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC8R,aAAa,CAAC1M,IAAI,CAAC,IAAI,CAACR,KAAK,CAAC;KACpC,MAAM;MACL,IAAI,CAAC2M,WAAW,GAAG,EAAE;MACrB,IAAI,CAACM,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC7R,SAAS,GAAG,KAAK;;IAGxB,IAAI,CAACkR,aAAa,GAAG,CAAC,CAAC;EACzB;EAEAsC,OAAOA,CAAA;IACL,IAAI,CAACvB,SAAS,EAAE;IAChB,IAAI,IAAI,CAACrN,KAAK,CAACiB,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACgM,eAAe,GAAG,IAAI,CAACN,WAAW,CAAC1L,MAAM,GAAG,CAAC;KACnD,MAAM,IAAI,IAAI,CAACjB,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;MAClC;MACA,IAAI,CAAC2L,mBAAmB,CAACiC,sBAAsB,EAAE,CAACvX,SAAS,CAACwX,YAAY,IAAG;QACzE,IAAI,CAACnC,WAAW,GAAGmC,YAAY;QAC/B,IAAI,CAAC7B,eAAe,GAAG,IAAI;MAC7B,CAAC,CAAC;;EAEN;EAEA8B,SAASA,CAACL,KAAoB;IAC5B,IAAI,CAAC,IAAI,CAACzB,eAAe,EAAE;IAE3B,QAAQyB,KAAK,CAACjG,GAAG;MACf,KAAK,WAAW;QACdiG,KAAK,CAACM,cAAc,EAAE;QACtB,IAAI,CAAC1C,aAAa,GAAGrN,IAAI,CAACvC,GAAG,CAAC,IAAI,CAAC4P,aAAa,GAAG,CAAC,EAAE,IAAI,CAACK,WAAW,CAAC1L,MAAM,GAAG,CAAC,CAAC;QAClF;MACF,KAAK,SAAS;QACZyN,KAAK,CAACM,cAAc,EAAE;QACtB,IAAI,CAAC1C,aAAa,GAAGrN,IAAI,CAACsI,GAAG,CAAC,IAAI,CAAC+E,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD;MACF,KAAK,OAAO;QACVoC,KAAK,CAACM,cAAc,EAAE;QACtB,IAAI,IAAI,CAAC1C,aAAa,IAAI,CAAC,IAAI,IAAI,CAACA,aAAa,GAAG,IAAI,CAACK,WAAW,CAAC1L,MAAM,EAAE;UAC3E,IAAI,CAACiL,gBAAgB,CAAC,IAAI,CAACS,WAAW,CAAC,IAAI,CAACL,aAAa,CAAC,CAAC;;QAE7D;MACF,KAAK,QAAQ;QACX,IAAI,CAAC2C,eAAe,EAAE;QACtB;;EAEN;EAEA/C,gBAAgBA,CAACpF,QAA8B;IAC7C,IAAI,CAAC9G,KAAK,GAAG8G,QAAQ,CAACC,WAAW;IACjC,IAAI,CAACqG,QAAQ,CAAC,IAAI,CAACpN,KAAK,CAAC;IACzB,IAAI,CAACgN,gBAAgB,CAACkC,IAAI,CAACpI,QAAQ,CAAC;IACpC,IAAI,CAACmI,eAAe,EAAE;IAEtB,IAAI,IAAI,CAAChB,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAACC,aAAa,CAAClO,KAAK,GAAG,IAAI,CAACA,KAAK;;EAEtD;EAEAiP,eAAeA,CAAA;IACb,IAAI,CAAChC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACX,aAAa,GAAG,CAAC,CAAC;EACzB;EAEQqB,eAAeA,CAACe,KAAY;IAClC,IAAI,CAAC,IAAI,CAAC7B,UAAU,CAACqB,aAAa,CAACiB,QAAQ,CAACT,KAAK,CAACC,MAAc,CAAC,EAAE;MACjE,IAAI,CAACM,eAAe,EAAE;;EAE1B;EAEAzC,mBAAmBA,CAACjG,IAAY;IAC9B,QAAQA,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,cAAc;MACvB,KAAK,MAAM;QACT,OAAO,aAAa;MACtB,KAAK,SAAS;QACZ,OAAO,aAAa;MACtB;QACE,OAAO,uBAAuB;;EAEpC;;;uBA5JWnQ,qBAAqB,EAAAN,+DAAA,CAAAZ,+EAAA,GAAAY,+DAAA,CAAAA,qDAAA;IAAA;EAAA;;;YAArBM,qBAAqB;MAAAb,SAAA;MAAA+Z,SAAA,WAAAC,4BAAA3Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;kFARrB,CACT;QACE4Z,OAAO,EAAEnE,6DAAiB;QAC1BoE,WAAW,EAAErE,yDAAU,CAAC,MAAMhV,qBAAqB,CAAC;QACpDsZ,KAAK,EAAE;OACR,CACF;MAAAla,KAAA;MAAAC,IAAA;MAAAuC,MAAA;MAAAtC,QAAA,WAAAia,+BAAA/Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBHE,4DAAA,aAAkC;UAE9BA,wDAAA,IAAA8Z,kCAAA,eAAsD;UACtD9Z,4DAAA,kBAWqB;UAHnBA,wDAAA,mBAAA+Z,sDAAA/M,MAAA;YAAA,OAASjN,GAAA,CAAA4Y,OAAA,CAAA3L,MAAA,CAAe;UAAA,EAAC,mBAAAgN,sDAAA;YAAA,OAChBja,GAAA,CAAA+Y,OAAA,EAAS;UAAA,EADO,qBAAAmB,wDAAAjN,MAAA;YAAA,OAEdjN,GAAA,CAAAkZ,SAAA,CAAAjM,MAAA,CAAiB;UAAA,EAFH;UAR3BhN,0DAAA,EAWqB;UACrBA,wDAAA,IAAAka,oCAAA,iBAEM;UACRla,0DAAA,EAAM;UAENA,wDAAA,IAAAma,oCAAA,iBAgCM;UACRna,0DAAA,EAAM;;;UAnDEA,uDAAA,GAAU;UAAVA,wDAAA,SAAAD,GAAA,CAAA+V,IAAA,CAAU;UAOZ9V,uDAAA,GAAwB;UAAxBA,yDAAA,cAAAD,GAAA,CAAA+V,IAAA,CAAwB,aAAA/V,GAAA,CAAAkX,QAAA;UAHxBjX,wDAAA,gBAAAD,GAAA,CAAAiX,WAAA,CAA2B,aAAAjX,GAAA,CAAAkX,QAAA;UASvBjX,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAAuF,SAAA,CAAe;UAKjBtF,uDAAA,GAAqB;UAArBA,wDAAA,SAAAD,GAAA,CAAAoX,eAAA,CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBuC;;;;;;;;;ICiBhEnX,4DAAA,cAAkD;IAE9CA,4DAAA,EAAkH;IAAlHA,4DAAA,cAAkH;IAChHA,uDAAA,iBAAuE;IAGzEA,0DAAA,EAAM;IACNA,6DAAA,EAAM;IAANA,4DAAA,WAAM;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;IAC/BA,4DAAA,iBAAiE;IAAvBA,wDAAA,mBAAAsa,wDAAA;MAAAta,2DAAA,CAAAua,GAAA;MAAA,MAAAC,MAAA,GAAAxa,2DAAA;MAAA,OAASA,yDAAA,CAAAwa,MAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC9Dza,4DAAA,EAA+F;IAA/FA,4DAAA,cAA+F;IAC7FA,uDAAA,gBAA4E;IAE9EA,0DAAA,EAAM;;;;IALFA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAe,MAAA,CAAA2Z,YAAA,CAAkB;;;;;;IA8BxB1a,6DAAA,EAAkG;IAAlGA,4DAAA,cAAkG;IAChGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAkB,MAAA,CAAAqL,eAAA,gBACF;;;;;;IAqBAvM,6DAAA,EAAsG;IAAtGA,4DAAA,cAAsG;IACpGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAAwM,MAAA,CAAAD,eAAA,kBACF;;;;;IA2BIvM,4DAAA,EAAqH;IAArHA,4DAAA,cAAqH;IACnHA,uDAAA,eAAoG;IAEtGA,0DAAA,EAAM;;;;;IACNA,4DAAA,EAAoH;IAApHA,4DAAA,cAAoH;IAClHA,uDAAA,eAA0M;IAI5MA,0DAAA,EAAM;;;;;IAGVA,4DAAA,cAAsG;IACpGA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,GACF;IADEA,gEAAA,MAAA0E,MAAA,CAAA6H,eAAA,kBACF;;;;;IAUAvM,4DAAA,WAAyB;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAO;;;;;IACvCA,4DAAA,eAAgD;IAC9CA,4DAAA,EAAuH;IAAvHA,4DAAA,cAAuH;IACrHA,uDAAA,eAAoK;IAEpKA,4DAAA,WAAM;IAEFA,uDAAA,eAAmE;IAErEA,0DAAA,EAAiB;IAGrBA,oDAAA,sBACF;IAAAA,0DAAA,EAAO;;;ADrIT,MAAO1B,eAAe;EAM1BiB,YACUob,WAAwB,EACxBvZ,WAAwB,EACxBC,MAAc;IAFd,KAAAsZ,WAAW,GAAXA,WAAW;IACX,KAAAvZ,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAiE,SAAS,GAAG,KAAK;IACjB,KAAAoV,YAAY,GAAG,EAAE;IACjB,KAAAE,YAAY,GAAG,KAAK;IAOlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACF,WAAW,CAACrU,KAAK,CAAC;MACvCwU,MAAM,EAAE,CAAC,EAAE,EAAE,CAACnY,sDAAU,CAAC6D,QAAQ,EAAE7D,sDAAU,CAACoY,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrY,sDAAU,CAAC6D,QAAQ,EAAE7D,sDAAU,CAACoY,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtY,sDAAU,CAAC6D,QAAQ,EAAE7D,sDAAU,CAACoY,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAzZ,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACF,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;EAGAoQ,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC8I,UAAU,CAAC7Q,KAAK,EAAE;MACzB,IAAI,CAAC1E,SAAS,GAAG,IAAI;MACrB,IAAI,CAACoV,YAAY,GAAG,EAAE;MAEtB,MAAMQ,WAAW,GAAqB;QACpCJ,MAAM,EAAE,IAAI,CAACD,UAAU,CAAC3Q,KAAK,CAAC4Q,MAAM,CAACK,IAAI,EAAE;QAC3CH,QAAQ,EAAE,IAAI,CAACH,UAAU,CAAC3Q,KAAK,CAAC8Q,QAAQ,CAACG,IAAI,EAAE;QAC/CF,QAAQ,EAAE,IAAI,CAACJ,UAAU,CAAC3Q,KAAK,CAAC+Q;OACjC;MAED,IAAI,CAAC7Z,WAAW,CAACga,KAAK,CAACF,WAAW,CAAC,CAAC1Z,SAAS,CAAC;QAC5CkJ,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACE,OAAO,EAAE;YACpB;YACA,IAAI,CAACxJ,MAAM,CAACM,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;QAExC,CAAC;QACDmJ,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACxF,SAAS,GAAG,KAAK;UACtB,IAAI,CAACoV,YAAY,GAAG5P,KAAK,CAACuQ,OAAO,IAAI,iCAAiC;UAEtE;UACA,IAAI,CAACR,UAAU,CAAC9T,UAAU,CAAC;YAAEkU,QAAQ,EAAE;UAAE,CAAE,CAAC;QAC9C,CAAC;QACDjD,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAAC1S,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACgW,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAACX,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGQU,oBAAoBA,CAAA;IAC1B9I,MAAM,CAACC,IAAI,CAAC,IAAI,CAACoI,UAAU,CAACnI,QAAQ,CAAC,CAACvL,OAAO,CAACwL,GAAG,IAAG;MAClD,MAAMI,OAAO,GAAG,IAAI,CAAC8H,UAAU,CAAChO,GAAG,CAAC8F,GAAG,CAAC;MACxCI,OAAO,EAAEH,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGA4I,cAAcA,CAAC1I,WAAmB;IAChC,OAAO,IAAI,CAAC+H,UAAU,CAAChO,GAAG,CAACiG,WAAW,CAAC;EACzC;EAEA;;;EAGA2I,QAAQA,CAAC3I,WAAmB,EAAE4I,SAAiB;IAC7C,MAAM3I,OAAO,GAAG,IAAI,CAACyI,cAAc,CAAC1I,WAAW,CAAC;IAChD,OAAO,CAAC,EAAEC,OAAO,EAAE0I,QAAQ,CAACC,SAAS,CAAC,IAAI3I,OAAO,EAAEE,OAAO,CAAC;EAC7D;EAEA;;;EAGA1G,eAAeA,CAACuG,WAAmB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACyI,cAAc,CAAC1I,WAAW,CAAC;IAEhD,IAAIC,OAAO,EAAE0I,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACE,mBAAmB,CAAC7I,WAAW,CAAC,cAAc;;IAG/D,IAAIC,OAAO,EAAE0I,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMG,cAAc,GAAG7I,OAAO,CAACC,MAAM,GAAG,WAAW,CAAC,EAAE4I,cAAc;MACpE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAAC7I,WAAW,CAAC,qBAAqB8I,cAAc,aAAa;;IAGjG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAAC7I,WAAmB;IAC7C,MAAM+I,UAAU,GAA8B;MAC5Cf,MAAM,EAAE,QAAQ;MAChBE,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX;IACD,OAAOY,UAAU,CAAC/I,WAAW,CAAC,IAAIA,WAAW;EAC/C;EAEA;;;EAGA2H,UAAUA,CAAA;IACR,IAAI,CAACC,YAAY,GAAG,EAAE;EACxB;;;uBAjIWpc,eAAe,EAAA0B,+DAAA,CAAAZ,uDAAA,GAAAY,+DAAA,CAAAgC,+DAAA,GAAAhC,+DAAA,CAAA0L,mDAAA;IAAA;EAAA;;;YAAfpN,eAAe;MAAAmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAuC,MAAA;MAAAtC,QAAA,WAAAkc,yBAAAhc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5BE,4DAAA,aAA8B;UAMpBA,4DAAA,EAA+F;UAA/FA,4DAAA,aAA+F;UAC7FA,uDAAA,cAA4H;UAG9HA,0DAAA,EAAM;UAGVA,6DAAA,EAAyB;UAAzBA,4DAAA,YAAyB;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAK;UAC3CA,4DAAA,aAA2B;UAAAA,oDAAA,sCAA8B;UAAAA,0DAAA,EAAI;UAI/DA,wDAAA,KAAA+b,+BAAA,mBAeM;UAGN/b,4DAAA,gBAA2E;UAA5CA,wDAAA,sBAAAgc,mDAAA;YAAA,OAAYjc,GAAA,CAAAgS,QAAA,EAAU;UAAA,EAAC;UAEpD/R,4DAAA,eAAwB;UACiBA,oDAAA,mBAAW;UAAAA,0DAAA,EAAQ;UAC1DA,4DAAA,eAA6B;UAC3BA,uDAAA,iBAQE;UACFA,4DAAA,EAAkH;UAAlHA,4DAAA,eAAkH;UAChHA,uDAAA,gBAAsQ;UAExQA,0DAAA,EAAM;UAERA,wDAAA,KAAAic,+BAAA,kBAEM;UACRjc,0DAAA,EAAM;UAGNA,6DAAA,EAAwB;UAAxBA,4DAAA,eAAwB;UACmBA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAQ;UACzDA,4DAAA,eAA6B;UAC3BA,uDAAA,iBAQE;UACFA,4DAAA,EAAkH;UAAlHA,4DAAA,eAAkH;UAChHA,uDAAA,gBAAsQ;UAExQA,0DAAA,EAAM;UAERA,wDAAA,KAAAkc,+BAAA,kBAEM;UACRlc,0DAAA,EAAM;UAGNA,6DAAA,EAAwB;UAAxBA,4DAAA,eAAwB;UACmBA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAQ;UACzDA,4DAAA,eAA6B;UAC3BA,uDAAA,iBAQE;UACFA,4DAAA,EAAkH;UAAlHA,4DAAA,eAAkH;UAChHA,uDAAA,gBAAgG;UAGlGA,0DAAA,EAAM;UACNA,6DAAA,EAKC;UALDA,4DAAA,kBAKC;UAFCA,wDAAA,mBAAAmc,kDAAA;YAAA,OAASpc,GAAA,CAAAwb,wBAAA,EAA0B;UAAA,EAAC;UAGpCvb,wDAAA,KAAAoc,oCAAA,kBAGM;UACNpc,wDAAA,KAAAqc,oCAAA,kBAKM;UACRrc,0DAAA,EAAS;UAEXA,wDAAA,KAAAsc,+BAAA,kBAEM;UACRtc,0DAAA,EAAM;UAGNA,4DAAA,kBAKC;UACCA,wDAAA,KAAAuc,gCAAA,mBAAuC;UACvCvc,wDAAA,KAAAwc,gCAAA,mBAYO;UACTxc,0DAAA,EAAS;UAIXA,4DAAA,eAA2B;UAEvBA,oDAAA,sDACF;UAAAA,0DAAA,EAAI;UAKRA,4DAAA,eAAmC;UACjCA,uDAAA,eAAyD;UAG3DA,0DAAA,EAAM;;;UA/I0BA,uDAAA,IAAkB;UAAlBA,wDAAA,SAAAD,GAAA,CAAA2a,YAAA,CAAkB;UAkB1C1a,uDAAA,GAAwB;UAAxBA,wDAAA,cAAAD,GAAA,CAAA8a,UAAA,CAAwB;UAUtB7a,uDAAA,GAAiF;UAAjFA,yDAAA,UAAAD,GAAA,CAAA0b,QAAA,0BAAA1b,GAAA,CAAA0b,QAAA,wBAAiF;UAS5Dzb,uDAAA,GAAuE;UAAvEA,wDAAA,SAAAD,GAAA,CAAA0b,QAAA,0BAAA1b,GAAA,CAAA0b,QAAA,wBAAuE;UAc5Fzb,uDAAA,GAAqF;UAArFA,yDAAA,UAAAD,GAAA,CAAA0b,QAAA,4BAAA1b,GAAA,CAAA0b,QAAA,0BAAqF;UAShEzb,uDAAA,GAA2E;UAA3EA,wDAAA,SAAAD,GAAA,CAAA0b,QAAA,4BAAA1b,GAAA,CAAA0b,QAAA,0BAA2E;UAchGzb,uDAAA,GAAqF;UAArFA,yDAAA,UAAAD,GAAA,CAAA0b,QAAA,4BAAA1b,GAAA,CAAA0b,QAAA,0BAAqF;UAJrFzb,wDAAA,SAAAD,GAAA,CAAA6a,YAAA,uBAA2C;UAiB3C5a,uDAAA,GAAoE;UAApEA,yDAAA,eAAAD,GAAA,CAAA6a,YAAA,qCAAoE;UAE9D5a,uDAAA,GAAmB;UAAnBA,wDAAA,UAAAD,GAAA,CAAA6a,YAAA,CAAmB;UAInB5a,uDAAA,GAAkB;UAAlBA,wDAAA,SAAAD,GAAA,CAAA6a,YAAA,CAAkB;UAQH5a,uDAAA,GAA2E;UAA3EA,wDAAA,SAAAD,GAAA,CAAA0b,QAAA,4BAAA1b,GAAA,CAAA0b,QAAA,0BAA2E;UAUpGzb,uDAAA,GAA2B;UAA3BA,yDAAA,YAAAD,GAAA,CAAAuF,SAAA,CAA2B;UAD3BtF,wDAAA,aAAAD,GAAA,CAAAuF,SAAA,IAAAvF,GAAA,CAAA8a,UAAA,CAAA6B,OAAA,CAA4C;UAGrC1c,uDAAA,GAAgB;UAAhBA,wDAAA,UAAAD,GAAA,CAAAuF,SAAA,CAAgB;UAChBtF,uDAAA,GAAe;UAAfA,wDAAA,SAAAD,GAAA,CAAAuF,SAAA,CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5HxB,MAAO5G,SAAS;EAEpBa,YACU6B,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHrC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACoC,WAAW,CAACM,eAAe,EAAE,EAAE;MACtC,OAAO,IAAI;KACZ,MAAM;MACL;MACA,OAAO,IAAI,CAACL,MAAM,CAACsb,aAAa,CAAC,CAAC,SAAS,CAAC,CAAC;;EAEjD;;;uBAdWje,SAAS,EAAAsB,sDAAA,CAAAZ,+DAAA,GAAAY,sDAAA,CAAAgC,mDAAA;IAAA;EAAA;;;aAATtD,SAAS;MAAAme,OAAA,EAATne,SAAS,CAAAoe,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;ACL2C;AACA;AACT;AAEO;;;AAKvD,MAAOhb,WAAW;EAWtBxC,YAAoB8d,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAVP,KAAAC,OAAO,GAAG,GAAGF,kEAAW,CAACG,MAAM,GAAGH,kEAAW,CAACI,YAAY,EAAE;IAC5D,KAAAC,SAAS,GAAG,YAAY;IACxB,KAAAC,QAAQ,GAAG,WAAW;IAE/B,KAAAC,sBAAsB,GAAG,IAAIV,iDAAe,CAAU,IAAI,CAACW,QAAQ,EAAE,CAAC;IACvE,KAAAC,gBAAgB,GAAG,IAAI,CAACF,sBAAsB,CAACG,YAAY,EAAE;IAE5D,KAAAC,kBAAkB,GAAG,IAAId,iDAAe,CAAM,IAAI,CAACe,cAAc,EAAE,CAAC;IACrE,KAAAzc,YAAY,GAAG,IAAI,CAACwc,kBAAkB,CAACD,YAAY,EAAE;EAErB;EAEvC;;;EAGA1C,KAAKA,CAACF,WAA6B;IACjC,MAAM+C,WAAW,GAAgB;MAC/BC,MAAM,EAAEhD,WAAW,CAACJ,MAAM;MAC1BqD,IAAI,EAAEjD,WAAW,CAACF,QAAQ;MAC1BoD,QAAQ,EAAElD,WAAW,CAACD;KACvB;IAED,MAAMoD,OAAO,GAAG,IAAIrB,6DAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,OAAO,IAAI,CAACK,IAAI,CAACiB,IAAI,CAAe,IAAI,CAAChB,OAAO,EAAEW,WAAW,EAAE;MAAEI;IAAO,CAAE,CAAC,CACxE7G,IAAI,CACHpM,mDAAG,CAACT,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAAC1D,IAAI,CAACsX,KAAK,EAAE;QAClD;QACA,IAAI,CAACC,QAAQ,CAAC7T,QAAQ,CAAC1D,IAAI,CAACsX,KAAK,CAAC;QAClC,MAAME,QAAQ,GAAG;UACfC,EAAE,EAAE/T,QAAQ,CAAC1D,IAAI,CAACxF,IAAI,CAACid,EAAE;UACzBzd,IAAI,EAAE0J,QAAQ,CAAC1D,IAAI,CAACxF,IAAI,CAACR,IAAI;UAC7B0d,KAAK,EAAEhU,QAAQ,CAAC1D,IAAI,CAACxF,IAAI,CAACkd,KAAK;UAC/B7D,MAAM,EAAEnQ,QAAQ,CAAC1D,IAAI,CAAC6T,MAAM,CAAC7Z,IAAI;UACjCE,UAAU,EAAEwJ,QAAQ,CAAC1D,IAAI,CAAC6T,MAAM,CAACrX;SAClC;QACD,IAAI,CAACmb,WAAW,CAACH,QAAQ,CAAC;QAE1B;QACA,IAAI,CAACd,sBAAsB,CAACjT,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAACqT,kBAAkB,CAACrT,IAAI,CAAC+T,QAAQ,CAAC;QAEtC,OAAO;UACL5T,OAAO,EAAE,IAAI;UACb0T,KAAK,EAAE5T,QAAQ,CAAC1D,IAAI,CAACsX,KAAK;UAC1B9c,IAAI,EAAEgd,QAAQ;UACdpD,OAAO,EAAE;SACV;OACF,MAAM;QACL,MAAM,IAAIwD,KAAK,CAAClU,QAAQ,CAACC,MAAM,CAACkU,eAAe,IAAI,uBAAuB,CAAC;;IAE/E,CAAC,CAAC,EACF3B,0DAAU,CAACrS,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAI4P,YAAY,GAAG,gCAAgC;MAEnD,IAAI5P,KAAK,CAACA,KAAK,EAAEF,MAAM,EAAEkU,eAAe,EAAE;QACxCpE,YAAY,GAAG5P,KAAK,CAACA,KAAK,CAACF,MAAM,CAACkU,eAAe;OAClD,MAAM,IAAIhU,KAAK,CAACuQ,OAAO,EAAE;QACxBX,YAAY,GAAG5P,KAAK,CAACuQ,OAAO;OAC7B,MAAM,IAAIvQ,KAAK,CAACiU,MAAM,KAAK,GAAG,EAAE;QAC/BrE,YAAY,GAAG,qBAAqB;OACrC,MAAM,IAAI5P,KAAK,CAACiU,MAAM,KAAK,CAAC,EAAE;QAC7BrE,YAAY,GAAG,6BAA6B;;MAG9C,OAAOwC,gDAAU,CAAC,OAAO;QACvBrS,OAAO,EAAE,KAAK;QACdwQ,OAAO,EAAEX;OACV,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA7Y,MAAMA,CAAA;IACJ,IAAI,CAACmd,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACtB,sBAAsB,CAACjT,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAACqT,kBAAkB,CAACrT,IAAI,CAAC,IAAI,CAAC;EACpC;EAEA;;;EAGAhJ,eAAeA,CAAA;IACb,OAAO,IAAI,CAACkc,QAAQ,EAAE,IAAI,CAAC,IAAI,CAACsB,cAAc,EAAE;EAClD;EAEA;;;EAGAlB,cAAcA,CAAA;IACZ,MAAMS,QAAQ,GAAGU,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC1B,QAAQ,CAAC;IACpD,OAAOe,QAAQ,GAAGY,IAAI,CAACC,KAAK,CAACb,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEA;;;EAGAc,QAAQA,CAAA;IACN,OAAOJ,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC3B,SAAS,CAAC;EAC7C;EAEA;;;EAGQe,QAAQA,CAACD,KAAa;IAC5BY,YAAY,CAACK,OAAO,CAAC,IAAI,CAAC/B,SAAS,EAAEc,KAAK,CAAC;EAC7C;EAEA;;;EAGQS,WAAWA,CAAA;IACjBG,YAAY,CAACM,UAAU,CAAC,IAAI,CAAChC,SAAS,CAAC;EACzC;EAEA;;;EAGQmB,WAAWA,CAACH,QAAa;IAC/BU,YAAY,CAACK,OAAO,CAAC,IAAI,CAAC9B,QAAQ,EAAE2B,IAAI,CAACK,SAAS,CAACjB,QAAQ,CAAC,CAAC;EAC/D;EAEA;;;EAGQQ,cAAcA,CAAA;IACpBE,YAAY,CAACM,UAAU,CAAC,IAAI,CAAC/B,QAAQ,CAAC;EACxC;EAEA;;;EAGQE,QAAQA,CAAA;IACd,OAAO,CAAC,CAACuB,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC3B,SAAS,CAAC;EAC/C;EAEA;;;;;EAKQyB,cAAcA,CAAA;IACpB;IACA;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAS,cAAcA,CAAA;IACZ,MAAMpB,KAAK,GAAG,IAAI,CAACgB,QAAQ,EAAE;IAC7B,OAAO,IAAIvC,6DAAW,CAAC;MACrB,eAAe,EAAE,UAAUuB,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;;;uBArKWxc,WAAW,EAAA/B,sDAAA,CAAAZ,4DAAA;IAAA;EAAA;;;aAAX2C,WAAW;MAAA8a,OAAA,EAAX9a,WAAW,CAAA+a,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;ACNmC;AACyC;AACnC;;;AAgBvD,MAAOzD,mBAAmB;EAsC9B/Z,YAAoB8d,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IArCP,KAAAC,OAAO,GAAG,GAAGF,kEAAW,CAACG,MAAM,WAAW;IAE3D;IACQ,KAAAuC,aAAa,GAA2B;IAC9C;IACA;MAAEpB,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,uCAAuC;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,eAAe;MAAE+J,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAwD,CAAE,EAC7N;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,mCAAmC;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,eAAe;MAAE+J,IAAI,EAAE,aAAa;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAuD,CAAE,EAC3N;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,yBAAyB;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,gBAAgB;MAAE+J,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAwC,CAAE,EAC9L;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,2BAA2B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,QAAQ;MAAE+J,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAyC,CAAE,EACxL;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,6BAA6B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,sBAAsB;MAAE+J,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA2C,CAAE,EAC1M;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,8BAA8B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,OAAO;MAAE+J,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA4C,CAAE,EAC7L;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,0BAA0B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,WAAW;MAAE+J,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA4C,CAAE,EACjM;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,mBAAmB;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,SAAS;MAAE+J,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAqC,CAAE,EACjL;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,4BAA4B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,aAAa;MAAE+J,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA8C,CAAE,EACvM;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,kBAAkB;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,QAAQ;MAAE+J,IAAI,EAAE,UAAU;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAmC,CAAE,EAC5K;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,sCAAsC;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,SAAS;MAAE+J,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAoD,CAAE,EAC/M;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,6BAA6B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,OAAO;MAAE+J,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA2C,CAAE,EAC3L;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,kCAAkC;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,SAAS;MAAE+J,IAAI,EAAE,YAAY;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAAqD,CAAE,EACjN;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,2BAA2B;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,SAAS;MAAE+J,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,KAAK;MAAE/O,WAAW,EAAE;IAA2C,CAAE;IAE7L;IACA;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,UAAU;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,eAAe;MAAE+J,IAAI,EAAE,UAAU;MAAE9O,WAAW,EAAE;IAAyB,CAAE,EAC9I;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,QAAQ;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,gBAAgB;MAAE+J,IAAI,EAAE,QAAQ;MAAE9O,WAAW,EAAE;IAAwB,CAAE,EAC1I;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,OAAO;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,QAAQ;MAAE+J,IAAI,EAAE,OAAO;MAAE9O,WAAW,EAAE;IAAe,CAAE,EACvH;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,OAAO;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,sBAAsB;MAAE+J,IAAI,EAAE,OAAO;MAAE9O,WAAW,EAAE;IAA6B,CAAE,EACnJ;MAAEyN,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,OAAO;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,OAAO;MAAE+J,IAAI,EAAE,OAAO;MAAE9O,WAAW,EAAE;IAAc,CAAE,EACrH;MAAEyN,EAAE,EAAE,UAAU;MAAEzd,IAAI,EAAE,OAAO;MAAEwC,IAAI,EAAE,KAAK;MAAEgN,IAAI,EAAE,MAAM;MAAEuF,OAAO,EAAE,SAAS;MAAE+J,IAAI,EAAE,OAAO;MAAE9O,WAAW,EAAE;IAAgB,CAAE;IAE9H;IACA;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,eAAe;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,eAAe;MAAE/E,WAAW,EAAE;IAAe,CAAE,EACxH;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,gBAAgB;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,gBAAgB;MAAE/E,WAAW,EAAE;IAAgB,CAAE,EAC3H;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,QAAQ;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,QAAQ;MAAE/E,WAAW,EAAE;IAAQ,CAAE,EACnG;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,SAAS;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,SAAS;MAAE/E,WAAW,EAAE;IAAS,CAAE,EACtG;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,sBAAsB;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,sBAAsB;MAAE/E,WAAW,EAAE;IAAsB,CAAE,EAC7I;MAAEyN,EAAE,EAAE,IAAI;MAAEzd,IAAI,EAAE,OAAO;MAAEwC,IAAI,EAAE,IAAI;MAAEgN,IAAI,EAAE,SAAS;MAAEuF,OAAO,EAAE,OAAO;MAAE/E,WAAW,EAAE;IAAO,CAAE,CACjG;EAEsC;EAEvC;;;EAGAyG,eAAeA,CAACD,KAAa;IAC3B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACtM,MAAM,GAAG,CAAC,EAAE;MAC9B,OAAO0U,wCAAE,CAAC,EAAE,CAAC;;IAGf;IACA,OAAOA,wCAAE,CAAC,IAAI,CAACC,aAAa,CAAC,CAACtI,IAAI,CAChCpM,mDAAG,CAAC6U,SAAS,IACXA,SAAS,CAACC,MAAM,CAAClP,QAAQ,IACvBA,QAAQ,CAAC/P,IAAI,CAACkf,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAC,IACzDnP,QAAQ,CAACvN,IAAI,CAAC0c,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAC,IACxDnP,QAAQ,CAAC+O,IAAI,IAAI/O,QAAQ,CAAC+O,IAAI,CAACI,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAE,IAC3EnP,QAAQ,CAACgF,OAAO,IAAIhF,QAAQ,CAACgF,OAAO,CAACmK,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAE,CACnF,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;KAChB,CACF;IAED;IACA;;;;;;;;;;;;;;;;;;;EAqBF;EAEA;;;EAGArH,sBAAsBA,CAAA;IACpB,OAAO8G,wCAAE,CAAC,IAAI,CAACC,aAAa,CAACI,MAAM,CAAClP,QAAQ,IAC1C,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAACzF,QAAQ,CAACyF,QAAQ,CAACvN,IAAI,CAAC,CACjF,CAAC;EACJ;EAEA;;;EAGQ4c,yBAAyBA,CAAC1V,QAAa;IAC7C;IACA;IACA,OAAOA,QAAQ,CAAC2V,IAAI,EAAElV,GAAG,CAAEmV,IAAS,KAAM;MACxC7B,EAAE,EAAE6B,IAAI,CAAC7B,EAAE;MACXzd,IAAI,EAAEsf,IAAI,CAACtf,IAAI;MACfwC,IAAI,EAAE8c,IAAI,CAAC9c,IAAI;MACfgN,IAAI,EAAE8P,IAAI,CAAC9P,IAAI;MACfuF,OAAO,EAAEuK,IAAI,CAACvK,OAAO,EAAE/U,IAAI;MAC3B8e,IAAI,EAAEQ,IAAI,CAACR,IAAI,EAAE9e,IAAI;MACrB+e,OAAO,EAAEO,IAAI,CAACP,OAAO,EAAE/e,IAAI;MAC3BgQ,WAAW,EAAE,IAAI,CAACuP,iBAAiB,CAACD,IAAI;KACzC,CAAC,CAAC,IAAI,EAAE;EACX;EAEA;;;EAGQC,iBAAiBA,CAACxP,QAAa;IACrC,IAAIA,QAAQ,CAACP,IAAI,KAAK,SAAS,EAAE;MAC/B,OAAO,GAAGO,QAAQ,CAAC+O,IAAI,EAAE9e,IAAI,IAAI+P,QAAQ,CAAC/P,IAAI,KAAK+P,QAAQ,CAACvN,IAAI,OAAOuN,QAAQ,CAAC/P,IAAI,EAAE;KACvF,MAAM,IAAI+P,QAAQ,CAACP,IAAI,KAAK,MAAM,EAAE;MACnC,OAAO,GAAGO,QAAQ,CAAC/P,IAAI,KAAK+P,QAAQ,CAACgF,OAAO,EAAE/U,IAAI,EAAE;KACrD,MAAM;MACL,OAAO+P,QAAQ,CAAC/P,IAAI;;EAExB;;;uBA1HWqY,mBAAmB,EAAAtZ,sDAAA,CAAAZ,4DAAA;IAAA;EAAA;;;aAAnBka,mBAAmB;MAAAuD,OAAA,EAAnBvD,mBAAmB,CAAAwD,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;ACjB2C;AACA;AACT;AAQO;;;AAKvD,MAAOpR,aAAa;EAQxBpM,YAAoB8d,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAPP,KAAAC,OAAO,GAAG,GAAGF,kEAAW,CAACG,MAAM,UAAU;IACzC,KAAAoD,mBAAmB,GAAG,wBAAwB;IAC9C,KAAAC,mBAAmB,GAAG,CAAC;IAEhC,KAAAC,qBAAqB,GAAG,IAAI5D,iDAAe,CAAiB,IAAI,CAAC6D,iBAAiB,EAAE,CAAC;IACtF,KAAAlR,eAAe,GAAG,IAAI,CAACiR,qBAAqB,CAAC/C,YAAY,EAAE;EAE3B;EAEvC;;;EAGArT,mBAAmBA,CAAC3E,UAA4B;IAC9C,MAAMib,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAAClb,UAAU,CAAC;IACnD,OAAO,IAAI,CAACmb,aAAa,CAACF,OAAO,CAAC;EACpC;EAEA;;;EAGA7O,sBAAsBA,CAACpM,UAA4B;IACjD,MAAMib,OAAO,GAAG,IAAI,CAACG,qBAAqB,CAACpb,UAAU,CAAC;IACtD,OAAO,IAAI,CAACmb,aAAa,CAACF,OAAO,CAAC;EACpC;EAEA;;;EAGA5O,sBAAsBA,CAACrM,UAA4B;IACjD,MAAMib,OAAO,GAAG,IAAI,CAACI,qBAAqB,CAACrb,UAAU,CAAC;IACtD,OAAO,IAAI,CAACmb,aAAa,CAACF,OAAO,CAAC;EACpC;EAEA;;;EAGQE,aAAaA,CAACF,OAA4B;IAChD,MAAMxC,KAAK,GAAGY,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,IAAI,CAACb,KAAK,EAAE;MACV,OAAOrB,gDAAU,CAAC,gCAAgC,CAAC;;IAGrD,MAAMmB,OAAO,GAAG,IAAIrB,6DAAW,CAAC;MAC9B,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAUuB,KAAK;KACjC,CAAC;IAEF,OAAO,IAAI,CAAClB,IAAI,CAACiB,IAAI,CAAuB,GAAG,IAAI,CAAChB,OAAO,cAAc,EAAEyD,OAAO,EAAE;MAAE1C;IAAO,CAAE,CAAC,CAC7F7G,IAAI,CACHkJ,mDAAG,CAAC/V,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;QAC3BE,OAAO,CAACqH,GAAG,CAAC,2BAA2B,EAAEzH,QAAQ,CAAC;OACnD,MAAM;QACLI,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEH,QAAQ,CAACC,MAAM,CAAC2H,QAAQ,CAAC;;IAEpE,CAAC,CAAC,EACF4K,0DAAU,CAACrS,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAOoS,gDAAU,CAACpS,KAAK,CAAC;IAC1B,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQkW,kBAAkBA,CAAClb,UAA4B;IACrD,OAAO;MACLsb,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC,GAAG,CAAC;MACnBC,OAAO,EAAExb,UAAU,CAACY,aAAa;MACjC6a,kBAAkB,EAAE,CAAC,IAAI,CAACC,aAAa,CAAC1b,UAAU,CAACS,IAAI,CAAC,CAAC;MACzDkb,gBAAgB,EAAE,CAAC,IAAI,CAACD,aAAa,CAAC1b,UAAU,CAACW,EAAE,CAAC,CAAC;MACrDib,UAAU,EAAE,IAAI,CAACC,eAAe,CAAC7b,UAAU,CAACkB,UAAU,CAAC;MACvD4a,qBAAqB,EAAE9b,UAAU,CAACuE,aAAa;MAC/CwX,sBAAsB,EAAE,KAAK;MAC7BC,wBAAwB,EAAE,KAAK;MAC/BC,6BAA6B,EAAE,KAAK;MACpCC,mBAAmB,EAAE,IAAI;MACzBC,aAAa,EAAE,CAAC,IAAI,CAACC,kBAAkB,CAACpc,UAAU,CAACsE,KAAK,CAAC,CAAC;MAC1D+X,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE;KACX;EACH;EAEA;;;EAGQlB,qBAAqBA,CAACpb,UAA4B;IACxD,MAAMib,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAAClb,UAAU,CAAC;IACnDib,OAAO,CAACM,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9BN,OAAO,CAACsB,UAAU,GAAGvc,UAAU,CAACgK,UAAU;IAC1CiR,OAAO,CAACuB,KAAK,GAAG,IAAI,CAACC,eAAe,CAACzc,UAAU,CAACY,aAAa,EAAEZ,UAAU,CAACgK,UAAW,CAAC;IACtF,OAAOiR,OAAO;EAChB;EAEA;;;EAGQI,qBAAqBA,CAACrb,UAA4B;IACxD,MAAMib,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAAClb,UAAU,CAAC;IACnDib,OAAO,CAACM,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9B,OAAON,OAAO;EAChB;EAEA;;;EAGQS,aAAaA,CAACgB,cAAsB;IAC1C;IACA;IACA,MAAMC,KAAK,GAAGD,cAAc,CAACjS,KAAK,CAAC,GAAG,CAAC;IACvC,MAAM9M,IAAI,GAAGgf,KAAK,CAAC,CAAC,CAAC,CAACtH,IAAI,EAAE,CAACuH,WAAW,EAAE;IAE1C,OAAO;MACLjS,IAAI,EAAE,CAAC;MACPiO,EAAE,EAAEjb,IAAI;MACRxC,IAAI,EAAEuhB,cAAc;MACpB/e,IAAI,EAAEA;KACP;EACH;EAEA;;;EAGQke,eAAeA,CAAC3a,UAAiE;IACvF,MAAM2b,cAAc,GAAG,EAAE;IAEzB,IAAI3b,UAAU,CAACL,MAAM,GAAG,CAAC,EAAE;MACzBgc,cAAc,CAAClb,IAAI,CAAC;QAAEgJ,IAAI,EAAE,CAAC;QAAEmS,KAAK,EAAE5b,UAAU,CAACL;MAAM,CAAE,CAAC,CAAC,CAAC;;;IAG9D,IAAIK,UAAU,CAACH,QAAQ,GAAG,CAAC,EAAE;MAC3B8b,cAAc,CAAClb,IAAI,CAAC;QAAEgJ,IAAI,EAAE,CAAC;QAAEmS,KAAK,EAAE5b,UAAU,CAACH;MAAQ,CAAE,CAAC,CAAC,CAAC;;;IAGhE,IAAIG,UAAU,CAACF,OAAO,GAAG,CAAC,EAAE;MAC1B6b,cAAc,CAAClb,IAAI,CAAC;QAAEgJ,IAAI,EAAE,CAAC;QAAEmS,KAAK,EAAE5b,UAAU,CAACF;MAAO,CAAE,CAAC,CAAC,CAAC;;;IAG/D,OAAO6b,cAAc;EACvB;EAEA;;;EAGQT,kBAAkBA,CAACtQ,WAAmB;IAC5C,QAAQA,WAAW;MACjB,KAAK,SAAS;QAAE,OAAO,CAAC;MACxB,KAAK,UAAU;QAAE,OAAO,CAAC;MACzB,KAAK,OAAO;QAAE,OAAO,CAAC;MACtB;QAAS,OAAO,CAAC;;EAErB;EAEA;;;EAGQ2Q,eAAeA,CAACM,OAAe,EAAE/S,UAAkB;IACzD,MAAMgT,WAAW,GAAG,IAAIrZ,IAAI,CAACoZ,OAAO,CAAC;IACrC,MAAME,aAAa,GAAG,IAAItZ,IAAI,CAACqG,UAAU,CAAC;IAC1C,MAAMkT,QAAQ,GAAGD,aAAa,CAACE,OAAO,EAAE,GAAGH,WAAW,CAACG,OAAO,EAAE;IAChE,OAAO9Z,IAAI,CAAC+Z,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;EACjD;EAEA;;;EAGAhR,gBAAgBA,CAAClM,UAA4B;IAC3C,MAAM+M,MAAM,GAAiB;MAC3B6L,EAAE,EAAEjV,IAAI,CAAC0Z,GAAG,EAAE,CAACC,QAAQ,EAAE;MACzB7c,IAAI,EAAET,UAAU,CAACS,IAAI;MACrBE,EAAE,EAAEX,UAAU,CAACW,EAAE;MACjB+C,IAAI,EAAE1D,UAAU,CAACY,aAAa;MAC9BM,UAAU,EAAElB,UAAU,CAACkB,UAAU,CAACL,MAAM,GAAGb,UAAU,CAACkB,UAAU,CAACH,QAAQ,GAAGf,UAAU,CAACkB,UAAU,CAACF,OAAO;MACzGuc,UAAU,EAAE,IAAI5Z,IAAI;KACrB;IAED,MAAM6Z,QAAQ,GAAG,IAAI,CAACxC,iBAAiB,EAAE;IACzCwC,QAAQ,CAACC,OAAO,CAAC1Q,MAAM,CAAC;IAExB;IACA,MAAM2Q,eAAe,GAAGF,QAAQ,CAAClD,KAAK,CAAC,CAAC,EAAE,IAAI,CAACQ,mBAAmB,CAAC;IAEnEzB,YAAY,CAACK,OAAO,CAAC,IAAI,CAACmB,mBAAmB,EAAEtB,IAAI,CAACK,SAAS,CAAC8D,eAAe,CAAC,CAAC;IAC/E,IAAI,CAAC3C,qBAAqB,CAACnW,IAAI,CAAC8Y,eAAe,CAAC;EAClD;EAEA;;;EAGQ1C,iBAAiBA,CAAA;IACvB,IAAI;MACF,MAAMwC,QAAQ,GAAGnE,YAAY,CAACC,OAAO,CAAC,IAAI,CAACuB,mBAAmB,CAAC;MAC/D,OAAO2C,QAAQ,GAAGjE,IAAI,CAACC,KAAK,CAACgE,QAAQ,CAAC,GAAG,EAAE;KAC5C,CAAC,OAAOxY,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;;EAEb;EAEA;;;EAGAoE,mBAAmBA,CAAA;IACjBiQ,YAAY,CAACM,UAAU,CAAC,IAAI,CAACkB,mBAAmB,CAAC;IACjD,IAAI,CAACE,qBAAqB,CAACnW,IAAI,CAAC,EAAE,CAAC;EACrC;EAEA;;;EAGAqO,sBAAsBA,CAAA;IACpB,MAAMC,YAAY,GAAe,CAC/B;MAAEvI,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,gBAAgB;MAAEwC,IAAI,EAAE;IAAK,CAAE,EAC3D;MAAEgN,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,kBAAkB;MAAEwC,IAAI,EAAE;IAAK,CAAE,EAC7D;MAAEgN,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,eAAe;MAAEwC,IAAI,EAAE;IAAK,CAAE,EAC1D;MAAEgN,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,YAAY;MAAEwC,IAAI,EAAE;IAAK,CAAE,EACvD;MAAEgN,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,YAAY;MAAEwC,IAAI,EAAE;IAAK,CAAE,EACvD;MAAEgN,IAAI,EAAE,CAAC;MAAEiO,EAAE,EAAE,KAAK;MAAEzd,IAAI,EAAE,eAAe;MAAEwC,IAAI,EAAE;IAAK,CAAE,CAC3D;IAED,OAAO,IAAIgd,4CAAU,CAACgD,QAAQ,IAAG;MAC/BA,QAAQ,CAAC/Y,IAAI,CAACsO,YAAY,CAAC;MAC3ByK,QAAQ,CAACzL,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEA;;;EAGAN,eAAeA,CAACD,KAAa;IAC3B,OAAO,IAAI,CAACsB,sBAAsB,EAAE,CAACvB,IAAI,CACvCpM,mDAAG,CAAC4N,YAAY,IACdA,YAAY,CAACkH,MAAM,CAACwD,IAAI,IACtBA,IAAI,CAACziB,IAAI,CAACkf,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAC,IACrDuD,IAAI,CAACjgB,IAAI,CAAC0c,WAAW,EAAE,CAAC5U,QAAQ,CAACkM,KAAK,CAAC0I,WAAW,EAAE,CAAC,CACtD,CACF,CACF;EACH;;;uBAjPWxU,aAAa,EAAA3L,sDAAA,CAAAZ,4DAAA;IAAA;EAAA;;;aAAbuM,aAAa;MAAAkR,OAAA,EAAblR,aAAa,CAAAmR,IAAA;MAAAC,UAAA,EAFZ;IAAM;EAAA;;;;;;;;;;;;;;;ACdb,MAAMK,WAAW,GAAG;EACzBuG,UAAU,EAAE,KAAK;EACjBpG,MAAM,EAAE,uBAAuB;EAC/BC,YAAY,EAAE,aAAa;EAC3BoG,cAAc,EAAE;CACjB;;;;;;;;;;;;;;ACH4C;AAG7CC,sEAAA,EAAwB,CAACE,eAAe,CAACxjB,sDAAS,CAAC,CAChDyjB,KAAK,CAACC,GAAG,IAAIlZ,OAAO,CAACD,KAAK,CAACmZ,GAAG,CAAC,CAAC", "sources": ["./src/app/app-routing.module.ts", "./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/app.module.ts", "./src/app/components/dashboard/dashboard.component.ts", "./src/app/components/dashboard/dashboard.component.html", "./src/app/components/flight-results/flight-results.component.ts", "./src/app/components/flight-results/flight-results.component.html", "./src/app/components/flight/flight.component.ts", "./src/app/components/flight/flight.component.html", "./src/app/components/shared/autocomplete/autocomplete.component.ts", "./src/app/components/shared/autocomplete/autocomplete.component.html", "./src/app/components/signin/signin.component.ts", "./src/app/components/signin/signin.component.html", "./src/app/guards/auth.guard.ts", "./src/app/services/auth.service.ts", "./src/app/services/autocomplete.service.ts", "./src/app/services/flight.service.ts", "./src/environments/environment.ts", "./src/main.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AuthGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/signin', pathMatch: 'full' },\n  { path: 'signin', component: SigninComponent },\n  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard] },\n  { path: 'flights', component: FlightComponent, canActivate: [AuthGuard] },\n  { path: 'flight-results', component: FlightResultsComponent, canActivate: [AuthGuard] },\n  { path: '**', redirectTo: '/signin' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n", "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'e-front';\n}\n", "\n<router-outlet></router-outlet>\n", "import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightComponent } from './components/flight/flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AutocompleteComponent } from './components/shared/autocomplete/autocomplete.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    SigninComponent,\n    DashboardComponent,\n    FlightComponent,\n    FlightResultsComponent,\n    AutocompleteComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    FormsModule,\n    HttpClientModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  currentUser: any = null;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to current user\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    // Check if user is authenticated\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n    }\n  }\n\n  /**\n   * Navigate to flights page\n   */\n  navigateToFlights(): void {\n    this.router.navigate(['/flights']);\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/signin']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header with Block to Book branding -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"header-left\">\n        <div class=\"brand-logo\">\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <p class=\"dashboard-subtitle\" *ngIf=\"currentUser\">Welcome back, {{ currentUser.name }}</p>\n      </div>\n      <div class=\"header-right\">\n        <nav class=\"top-nav\">\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-home\"></i> Home</a>\n\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-wrench\"></i> Tools</a>\n          <a href=\"#\" class=\"nav-link\"><i class=\"fas fa-globe\"></i> Languages</a>\n          <button class=\"logout-button\" (click)=\"logout()\">\n            <i class=\"fas fa-sign-out-alt\"></i> Logout\n          </button>\n        </nav>\n      </div>\n    </div>\n    <div class=\"user-info\" *ngIf=\"currentUser\">\n      <div class=\"agency-info\">\n        <span class=\"agency-id\">{{ currentUser.agencyCode }} - Workspace for Demo</span>\n        <span class=\"balance\">Balance: 8500.48 TND</span>\n        <span class=\"due-info\">Due: -1399.52 TND</span>\n      </div>\n    </div>\n  </header>\n\n  <!-- Main content with navigation cards -->\n  <main class=\"dashboard-main\">\n    <div class=\"navigation-grid\">\n      <!-- First row of cards -->\n      <div class=\"row\">\n        <!-- Booking Queue -->\n        <div class=\"nav-card booking-queue\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-clipboard-list\"></i>\n          </div>\n          <span class=\"card-title\">Booking Queue</span>\n        </div>\n\n        <!-- Commissions -->\n        <div class=\"nav-card commissions\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-percentage\"></i>\n          </div>\n          <span class=\"card-title\">Commissions</span>\n        </div>\n\n        <!-- Sub-Agent -->\n        <div class=\"nav-card sub-agent\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-users\"></i>\n          </div>\n          <span class=\"card-title\">Sub Agent</span>\n        </div>\n      </div>\n\n      <!-- Second row of cards -->\n      <div class=\"row\">\n        <!-- Profile -->\n        <div class=\"nav-card profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user\"></i>\n          </div>\n          <span class=\"card-title\">Profile</span>\n        </div>\n\n        <!-- Finance -->\n        <div class=\"nav-card finance\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-money-bill-wave\"></i>\n          </div>\n          <span class=\"card-title\">Finance</span>\n        </div>\n\n        <!-- Agency Profile -->\n        <div class=\"nav-card agency-profile\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-building\"></i>\n          </div>\n          <span class=\"card-title\">Agency Profile</span>\n        </div>\n      </div>\n\n      <!-- Third row with larger cards -->\n      <div class=\"row featured-row\">\n        <!-- Flights -->\n        <div class=\"nav-card featured flights\" (click)=\"navigateToFlights()\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <span class=\"card-title\">Flights</span>\n        </div>\n\n        <!-- Flight Info -->\n        <div class=\"nav-card flight-info\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-info-circle\"></i>\n          </div>\n          <span class=\"card-title\">Flight Info</span>\n        </div>\n      </div>\n\n      <!-- Fourth row -->\n      <div class=\"row\">\n        <!-- Passengers -->\n        <div class=\"nav-card passengers\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-user-friends\"></i>\n          </div>\n          <span class=\"card-title\">Passengers</span>\n        </div>\n\n        <!-- Credit Request -->\n        <div class=\"nav-card credit-request\">\n          <div class=\"card-icon\">\n            <i class=\"fas fa-credit-card\"></i>\n          </div>\n          <span class=\"card-title\">Credit Request</span>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { FlightService } from '../../services/flight.service';\nimport { FlightSearchResponse, Flight, FlightSearchForm } from '../../models/flight.models';\nimport { Observable } from 'rxjs';\n\ninterface AirlineFilter {\n  code: string;\n  name: string;\n  logo: string;\n  active: boolean;\n}\n\ninterface FlightResult {\n  airline: string;\n  airlineLogo: string;\n  price: number;\n  currency: string;\n  stops: number;\n  duration: string;\n  departureTime: string;\n  arrivalTime: string;\n  flightNumber: string;\n}\n\n@Component({\n  selector: 'app-flight-results',\n  templateUrl: './flight-results.component.html',\n  styleUrls: ['./flight-results.component.css']\n})\nexport class FlightResultsComponent implements OnInit {\n  searchResults: FlightSearchResponse | null = null;\n  searchForm: FormGroup;\n  isLoading = false;\n  originalSearchParams: any = null;\n\n  // Filter options\n  airlineFilters: AirlineFilter[] = [\n    { code: 'TK', name: 'Turkish Airlines', logo: 'assets/airlines/turkish-airlines.png', active: false },\n    { code: 'LH', name: 'Lufthansa', logo: 'assets/airlines/lufthansa.png', active: false },\n    { code: 'EY', name: 'Etihad', logo: 'assets/airlines/etihad.png', active: false },\n    { code: 'MS', name: 'EgyptAir', logo: 'assets/airlines/egyptair.png', active: false },\n    { code: 'AH', name: 'Air Algerie', logo: 'assets/airlines/air-algerie.png', active: false },\n    { code: 'TU', name: 'Tunisair', logo: 'assets/airlines/tunisair.png', active: false }\n  ];\n\n  // Organized flight results by stops\n  flightsByStops = {\n    nonStop: [] as FlightResult[],\n    oneStop: [] as FlightResult[],\n    twoOrMoreStops: [] as FlightResult[]\n  };\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private fb: FormBuilder,\n    private flightService: FlightService\n  ) {\n    this.searchForm = this.createSearchForm();\n  }\n\n  ngOnInit(): void {\n    // Get search results from navigation state or route params\n    const navigation = this.router.getCurrentNavigation();\n    if (navigation?.extras.state?.['results']) {\n      this.searchResults = navigation.extras.state['results'];\n      this.originalSearchParams = navigation.extras.state['searchParams'];\n      this.processSearchResults();\n    } else {\n      // If no results in state, redirect back to search\n      this.router.navigate(['/flights']);\n    }\n\n    // Initialize form with original search parameters\n    if (this.originalSearchParams) {\n      this.initializeSearchForm();\n    }\n  }\n\n  private createSearchForm(): FormGroup {\n    return this.fb.group({\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]]\n    });\n  }\n\n  private initializeSearchForm(): void {\n    if (this.originalSearchParams) {\n      this.searchForm.patchValue({\n        from: this.originalSearchParams.from,\n        to: this.originalSearchParams.to,\n        departureDate: this.originalSearchParams.departureDate,\n        adults: this.originalSearchParams.passengers?.adults || 1,\n        children: this.originalSearchParams.passengers?.children || 0,\n        infants: this.originalSearchParams.passengers?.infants || 0\n      });\n    }\n  }\n\n  private processSearchResults(): void {\n    if (!this.searchResults?.body?.flights) {\n      return;\n    }\n\n    // Reset flight arrays\n    this.flightsByStops.nonStop = [];\n    this.flightsByStops.oneStop = [];\n    this.flightsByStops.twoOrMoreStops = [];\n\n    // Process each flight and categorize by stops\n    this.searchResults.body.flights.forEach(flight => {\n      const flightResult = this.convertToFlightResult(flight);\n\n      // Get stop count from the first flight item\n      const stopCount = flight.items[0]?.stopCount || 0;\n\n      if (stopCount === 0) {\n        this.flightsByStops.nonStop.push(flightResult);\n      } else if (stopCount === 1) {\n        this.flightsByStops.oneStop.push(flightResult);\n      } else {\n        this.flightsByStops.twoOrMoreStops.push(flightResult);\n      }\n    });\n\n    // Sort by price within each category\n    this.flightsByStops.nonStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.oneStop.sort((a, b) => a.price - b.price);\n    this.flightsByStops.twoOrMoreStops.sort((a, b) => a.price - b.price);\n  }\n\n  private convertToFlightResult(flight: Flight): FlightResult {\n    const firstItem = flight.items[0];\n    const firstOffer = flight.offers[0];\n\n    return {\n      airline: firstItem?.airline?.name || 'Unknown',\n      airlineLogo: this.getAirlineLogo(firstItem?.airline?.code || ''),\n      price: firstOffer?.price?.amount || 0,\n      currency: firstOffer?.price?.currency || 'TND',\n      stops: firstItem?.stopCount || 0,\n      duration: this.formatDuration(firstItem?.duration || 0),\n      departureTime: this.formatTime(firstItem?.departure?.code || ''),\n      arrivalTime: this.formatTime(firstItem?.arrival?.code || ''),\n      flightNumber: firstItem?.flightNo || ''\n    };\n  }\n\n  private getAirlineLogo(airlineCode: string): string {\n    const airline = this.airlineFilters.find(a => a.code === airlineCode);\n    return airline?.logo || 'assets/airlines/default.png';\n  }\n\n  private formatDuration(durationMinutes: number): string {\n    const hours = Math.floor(durationMinutes / 60);\n    const minutes = durationMinutes % 60;\n    return `${hours}h ${minutes}m`;\n  }\n\n  private formatTime(timeString: string): string {\n    // Simple time formatting - in real app, this would parse actual time\n    return timeString || '--:--';\n  }\n\n  /**\n   * Get search summary for display\n   */\n  getSearchSummary(): string {\n    if (!this.originalSearchParams) return '';\n\n    const from = this.originalSearchParams.from;\n    const to = this.originalSearchParams.to;\n    const date = new Date(this.originalSearchParams.departureDate).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: '2-digit',\n      day: '2-digit'\n    });\n    const adults = this.originalSearchParams.passengers?.adults || 1;\n\n    return `${from} - ${to}, ${date}, ${adults}Adult(s)`;\n  }\n\n  /**\n   * Toggle airline filter\n   */\n  toggleAirlineFilter(airline: AirlineFilter): void {\n    airline.active = !airline.active;\n    // In a real app, this would filter the results\n  }\n\n  /**\n   * Perform new search\n   */\n  onNewSearch(): void {\n    this.router.navigate(['/flights']);\n  }\n\n  /**\n   * Search again with current form values\n   */\n  onSearchAgain(): void {\n    if (this.searchForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.searchForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: 'oneWay',\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: 'economy',\n        directFlights: false,\n        refundableFares: false,\n        baggage: 'all',\n        calendar: false\n      };\n\n      this.flightService.searchOneWayFlights(searchForm).subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response;\n            this.originalSearchParams = searchForm;\n            this.processSearchResults();\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get flights for a specific stop category\n   */\n  getFlightsForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult[] {\n    return this.flightsByStops[stopType];\n  }\n\n  /**\n   * Get minimum price for a stop category\n   */\n  getMinPriceForStops(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): number | null {\n    const flights = this.flightsByStops[stopType];\n    if (flights.length === 0) return null;\n    return Math.min(...flights.map(f => f.price));\n  }\n\n  /**\n   * Format price display\n   */\n  formatPrice(price: number, currency: string = 'TND'): string {\n    return `${price} ${currency}`;\n  }\n\n  /**\n   * Get stop label\n   */\n  getStopLabel(stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): string {\n    switch (stopType) {\n      case 'nonStop': return 'Non Stop';\n      case 'oneStop': return '1 Stop';\n      case 'twoOrMoreStops': return '2+ Stops';\n      default: return '';\n    }\n  }\n\n  /**\n   * Get flight for specific airline and stop type\n   */\n  getFlightForAirlineAndStops(airlineCode: string, stopType: 'nonStop' | 'oneStop' | 'twoOrMoreStops'): FlightResult | null {\n    const flights = this.flightsByStops[stopType];\n    return flights.find(flight => flight.airline.includes(airlineCode)) || null;\n  }\n}\n", "<div class=\"flight-results-container\">\n  <!-- Search Summary Header -->\n  <div class=\"search-summary-header\">\n    <h1 class=\"search-title\">{{ getSearchSummary() }}</h1>\n  </div>\n\n  <!-- Search Modification Panel -->\n  <div class=\"search-modification-panel\">\n    <form [formGroup]=\"searchForm\" class=\"search-form\">\n      <div class=\"form-row\">\n        <!-- From -->\n        <div class=\"form-group\">\n          <label>From</label>\n          <input\n            type=\"text\"\n            formControlName=\"from\"\n            class=\"form-control\"\n            placeholder=\"IST - Istanbul Airport\">\n        </div>\n\n        <!-- To -->\n        <div class=\"form-group\">\n          <label>To</label>\n          <input\n            type=\"text\"\n            formControlName=\"to\"\n            class=\"form-control\"\n            placeholder=\"TUN - Carthage Arpt\">\n        </div>\n\n        <!-- Departure Date -->\n        <div class=\"form-group\">\n          <label>Departure on</label>\n          <div class=\"date-input-wrapper\">\n            <input\n              type=\"date\"\n              formControlName=\"departureDate\"\n              class=\"form-control date-input\">\n            <div class=\"date-controls\">\n              <button type=\"button\" class=\"date-btn\">-</button>\n              <button type=\"button\" class=\"date-btn\">+</button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Action Buttons -->\n      <div class=\"action-buttons\">\n        <button\n          type=\"button\"\n          class=\"btn btn-secondary email-btn\">\n          <i class=\"fas fa-envelope\"></i> Email Itineraries\n        </button>\n        <button\n          type=\"button\"\n          class=\"btn btn-primary new-search-btn\"\n          (click)=\"onNewSearch()\">\n          NEW SEARCH\n        </button>\n        <button\n          type=\"button\"\n          class=\"btn btn-primary search-again-btn\"\n          (click)=\"onSearchAgain()\"\n          [disabled]=\"isLoading\">\n          <span *ngIf=\"!isLoading\">SEARCH AGAIN</span>\n          <span *ngIf=\"isLoading\">\n            <i class=\"fas fa-spinner fa-spin\"></i> Searching...\n          </span>\n        </button>\n      </div>\n    </form>\n  </div>\n\n  <!-- Results Section -->\n  <div class=\"results-section\">\n    <!-- Airline Filter Tabs -->\n    <div class=\"airline-filters\">\n      <div class=\"filter-tabs\">\n        <button\n          *ngFor=\"let airline of airlineFilters\"\n          class=\"airline-tab\"\n          [class.active]=\"airline.active\"\n          (click)=\"toggleAirlineFilter(airline)\">\n          <div class=\"airline-logo-placeholder\">\n            <span class=\"airline-code\">{{ airline.code }}</span>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- Flight Results Grid -->\n    <div class=\"flight-results-grid\">\n      <!-- Header Row -->\n      <div class=\"grid-header\">\n        <div class=\"stop-category-header\"></div>\n        <div class=\"airline-header\" *ngFor=\"let airline of airlineFilters\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">{{ airline.code }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Non Stop Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">Non Stop</span>\n        </div>\n        <div class=\"price-cell\" *ngFor=\"let airline of airlineFilters\">\n          <ng-container *ngIf=\"getFlightForAirlineAndStops(airline.code, 'nonStop') as flight\">\n            <div class=\"price-info\">\n              <span class=\"price\">{{ formatPrice(flight.price) }}</span>\n              <span class=\"currency\">TND</span>\n            </div>\n          </ng-container>\n          <ng-container *ngIf=\"!getFlightForAirlineAndStops(airline.code, 'nonStop')\">\n            <div class=\"no-flight\">-</div>\n          </ng-container>\n        </div>\n      </div>\n\n      <!-- 1 Stop Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">1 Stop</span>\n        </div>\n        <div class=\"price-cell\" *ngFor=\"let airline of airlineFilters\">\n          <ng-container *ngIf=\"getFlightForAirlineAndStops(airline.code, 'oneStop') as flight\">\n            <div class=\"price-info\">\n              <span class=\"price\">{{ formatPrice(flight.price) }}</span>\n              <span class=\"currency\">TND</span>\n            </div>\n          </ng-container>\n          <ng-container *ngIf=\"!getFlightForAirlineAndStops(airline.code, 'oneStop')\">\n            <div class=\"no-flight\">-</div>\n          </ng-container>\n        </div>\n      </div>\n\n      <!-- 2+ Stops Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">2+ Stops</span>\n        </div>\n        <div class=\"price-cell\" *ngFor=\"let airline of airlineFilters\">\n          <ng-container *ngIf=\"getFlightForAirlineAndStops(airline.code, 'twoOrMoreStops') as flight\">\n            <div class=\"price-info\">\n              <span class=\"price\">{{ formatPrice(flight.price) }}</span>\n              <span class=\"currency\">TND</span>\n            </div>\n          </ng-container>\n          <ng-container *ngIf=\"!getFlightForAirlineAndStops(airline.code, 'twoOrMoreStops')\">\n            <div class=\"no-flight\">-</div>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n\n    <!-- Alternative Layout: List View -->\n    <div class=\"flight-results-list\" *ngIf=\"searchResults?.body?.flights?.length === 0\">\n      <div class=\"no-results\">\n        <i class=\"fas fa-plane\"></i>\n        <h3>No flights found</h3>\n        <p>Try adjusting your search criteria</p>\n      </div>\n    </div>\n\n    <!-- Sample Price Grid (matching the image) -->\n    <div class=\"sample-price-grid\">\n      <div class=\"grid-header\">\n        <div class=\"stop-category-header\"></div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">TK</span>\n          </div>\n        </div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">LH</span>\n          </div>\n        </div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">EY</span>\n          </div>\n        </div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">MS</span>\n          </div>\n        </div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">AH</span>\n          </div>\n        </div>\n        <div class=\"airline-header\">\n          <div class=\"airline-logo-placeholder-small\">\n            <span class=\"airline-code-small\">TU</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Non Stop Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">Non Stop</span>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">531</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">555</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">684</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n      </div>\n\n      <!-- 1 Stop Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">1 Stop</span>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">710</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">1047</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">1062</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">1220</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 2+ Stops Row -->\n      <div class=\"grid-row\">\n        <div class=\"stop-category\">\n          <span class=\"stop-label\">2+ Stops</span>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">1372</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"price-info\">\n            <span class=\"price\">1855</span>\n            <span class=\"currency\">TND</span>\n          </div>\n        </div>\n        <div class=\"price-cell\">\n          <div class=\"no-flight\">-</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { FlightService } from '../../services/flight.service';\nimport { AuthService } from '../../services/auth.service';\nimport { FlightSearchForm, LatestSearch, Location } from '../../models/flight.models';\nimport { AutocompleteLocation } from '../../services/autocomplete.service';\nimport { Observable } from 'rxjs';\n\ninterface FlightSegment {\n  from: string;\n  to: string;\n  date: string;\n}\n\n@Component({\n  selector: 'app-flight',\n  templateUrl: './flight.component.html',\n  styleUrls: ['./flight.component.css']\n})\nexport class FlightComponent implements OnInit {\n  flightForm: FormGroup;\n  isLoading = false;\n  currentUser: any = null;\n  latestSearches$: Observable<LatestSearch[]>;\n\n  // Form state\n  tripType = 'oneWay';\n  showReturnDate = false;\n  showCalendar = false;\n  additionalSegments: FlightSegment[] = [];\n\n  // Passenger counts\n  adultCount = 1;\n  childCount = 0;\n  infantCount = 0;\n\n  // Class selection\n  selectedClass = 'economy';\n\n  // Baggage options\n  baggageOptions = [\n    { value: 'all', label: '--All--' },\n    { value: '20kg', label: '20kg' },\n    { value: '30kg', label: '30kg' },\n    { value: 'extra', label: 'Extra' }\n  ];\n\n  // Calendar options\n  calendarDays = [\n    { value: '1', label: '+/- 1 Days' },\n    { value: '3', label: '+/- 3 Days' },\n    { value: '7', label: '+/- 7 Days' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private flightService: FlightService,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.flightForm = this.createForm();\n    this.latestSearches$ = this.flightService.latestSearches$;\n  }\n\n  ngOnInit(): void {\n    // Check authentication\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/signin']);\n      return;\n    }\n\n    // Set default dates\n    this.setDefaultDates();\n  }\n\n  /**\n   * Create reactive form\n   */\n  private createForm(): FormGroup {\n    return this.fb.group({\n      tripType: ['oneWay', Validators.required],\n      from: ['', Validators.required],\n      to: ['', Validators.required],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1)]],\n      children: [0, [Validators.min(0)]],\n      infants: [0, [Validators.min(0)]],\n      class: ['economy', Validators.required],\n      preferredAirline: [''],\n      directFlights: [false],\n      refundableFares: [false],\n      baggage: ['all'],\n      calendar: [false],\n      calendarDays: ['3']\n    });\n  }\n\n  /**\n   * Set default dates (today and tomorrow)\n   */\n  private setDefaultDates(): void {\n    const today = new Date();\n    const tomorrow = new Date(today);\n    tomorrow.setDate(tomorrow.getDate() + 1);\n\n    const nextWeek = new Date(today);\n    nextWeek.setDate(nextWeek.getDate() + 7);\n\n    this.flightForm.patchValue({\n      departureDate: this.formatDate(tomorrow),\n      returnDate: this.formatDate(nextWeek)\n    });\n  }\n\n  /**\n   * Format date for input field\n   */\n  private formatDate(date: Date): string {\n    return date.toISOString().split('T')[0];\n  }\n\n  /**\n   * Handle trip type change\n   */\n  onTripTypeChange(type: string): void {\n    this.tripType = type;\n    this.showReturnDate = type === 'roundTrip';\n\n    this.flightForm.patchValue({ tripType: type });\n\n    if (type === 'roundTrip') {\n      this.flightForm.get('returnDate')?.setValidators([Validators.required]);\n    } else {\n      this.flightForm.get('returnDate')?.clearValidators();\n    }\n    this.flightForm.get('returnDate')?.updateValueAndValidity();\n\n    // Clear additional segments when switching away from multi-city\n    if (type !== 'multiCity') {\n      this.additionalSegments = [];\n    }\n  }\n\n  /**\n   * Add a new flight segment for multi-city trips\n   */\n  addSegment(): void {\n    const newSegment: FlightSegment = {\n      from: '',\n      to: '',\n      date: ''\n    };\n    this.additionalSegments.push(newSegment);\n  }\n\n  /**\n   * Remove a flight segment\n   */\n  removeSegment(index: number): void {\n    this.additionalSegments.splice(index, 1);\n  }\n\n  /**\n   * Handle location selection for main form fields\n   */\n  onFromLocationSelected(location: AutocompleteLocation): void {\n    this.flightForm.patchValue({ from: location.displayText });\n  }\n\n  onToLocationSelected(location: AutocompleteLocation): void {\n    this.flightForm.patchValue({ to: location.displayText });\n  }\n\n  /**\n   * Handle location selection for additional segments\n   */\n  onSegmentLocationSelected(location: AutocompleteLocation, segmentIndex: number, field: 'from' | 'to'): void {\n    if (this.additionalSegments[segmentIndex]) {\n      this.additionalSegments[segmentIndex][field] = location.displayText;\n    }\n  }\n\n  /**\n   * Handle passenger count changes\n   */\n  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {\n    const currentValue = this.flightForm.get(type)?.value || 0;\n    let newValue = increment ? currentValue + 1 : Math.max(0, currentValue - 1);\n\n    // Ensure at least 1 adult\n    if (type === 'adults' && newValue < 1) {\n      newValue = 1;\n    }\n\n    this.flightForm.patchValue({ [type]: newValue });\n\n    // Update component properties for display\n    if (type === 'adults') this.adultCount = newValue;\n    if (type === 'children') this.childCount = newValue;\n    if (type === 'infants') this.infantCount = newValue;\n  }\n\n  /**\n   * Get total passenger count\n   */\n  getTotalPassengers(): number {\n    const adults = this.flightForm.get('adults')?.value || 0;\n    const children = this.flightForm.get('children')?.value || 0;\n    const infants = this.flightForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Handle class selection\n   */\n  onClassChange(flightClass: string): void {\n    this.selectedClass = flightClass;\n    this.flightForm.patchValue({ class: flightClass });\n  }\n\n  /**\n   * Toggle calendar option\n   */\n  toggleCalendar(): void {\n    this.showCalendar = !this.showCalendar;\n    this.flightForm.patchValue({ calendar: this.showCalendar });\n  }\n\n  /**\n   * Swap from and to locations\n   */\n  swapLocations(): void {\n    const from = this.flightForm.get('from')?.value;\n    const to = this.flightForm.get('to')?.value;\n\n    this.flightForm.patchValue({\n      from: to,\n      to: from\n    });\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.flightForm.valid) {\n      this.isLoading = true;\n\n      const formValue = this.flightForm.value;\n      const searchForm: FlightSearchForm = {\n        tripType: formValue.tripType,\n        from: formValue.from,\n        to: formValue.to,\n        departureDate: formValue.departureDate,\n        returnDate: formValue.returnDate,\n        passengers: {\n          adults: formValue.adults,\n          children: formValue.children,\n          infants: formValue.infants\n        },\n        class: formValue.class,\n        preferredAirline: formValue.preferredAirline,\n        directFlights: formValue.directFlights,\n        refundableFares: formValue.refundableFares,\n        baggage: formValue.baggage,\n        calendar: formValue.calendar\n      };\n\n      // Save to latest searches\n      this.flightService.saveLatestSearch(searchForm);\n\n      // Perform search based on trip type\n      let searchObservable;\n\n      switch (searchForm.tripType) {\n        case 'oneWay':\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n          break;\n        case 'roundTrip':\n          searchObservable = this.flightService.searchRoundTripFlights(searchForm);\n          break;\n        case 'multiCity':\n          searchObservable = this.flightService.searchMultiCityFlights(searchForm);\n          break;\n        default:\n          searchObservable = this.flightService.searchOneWayFlights(searchForm);\n      }\n\n      searchObservable.subscribe({\n        next: (response) => {\n          this.isLoading = false;\n          if (response.header.success) {\n            console.log('Flight search results:', response);\n            // Navigate to results page with search results and parameters\n            this.router.navigate(['/flight-results'], {\n              state: {\n                results: response,\n                searchParams: searchForm\n              }\n            });\n          } else {\n            console.error('Search failed:', response.header.messages);\n            // TODO: Show error message to user\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          console.error('Search error:', error);\n          // TODO: Show error message to user\n        }\n      });\n    } else {\n      // Mark all fields as touched to show validation errors\n      Object.keys(this.flightForm.controls).forEach(key => {\n        this.flightForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Load a previous search\n   */\n  loadLatestSearch(search: LatestSearch): void {\n    this.flightForm.patchValue({\n      from: search.from,\n      to: search.to,\n      departureDate: search.date,\n      adults: search.passengers,\n      children: 0,\n      infants: 0\n    });\n  }\n\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches(): void {\n    this.flightService.clearLatestSearches();\n  }\n\n  /**\n   * Get form control error message\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.flightForm.get(controlName);\n    if (control?.errors && control.touched) {\n      if (control.errors['required']) {\n        return `${controlName} is required`;\n      }\n      if (control.errors['min']) {\n        return `Minimum value is ${control.errors['min'].min}`;\n      }\n    }\n    return '';\n  }\n}\n", "<div class=\"flight-container\">\n  <!-- Main Content -->\n  <div class=\"flight-content\">\n    <!-- Left Panel - Flight Search -->\n    <div class=\"flight-search-panel\">\n      <div class=\"search-header\">\n        <div class=\"search-title\">\n          <i class=\"fas fa-plane\"></i>\n          <h2>Search and Book Flights</h2>\n        </div>\n        <p class=\"search-subtitle\">We're bringing you a new level of comfort</p>\n      </div>\n\n      <form [formGroup]=\"flightForm\" (ngSubmit)=\"onSubmit()\" class=\"flight-form\">\n        <!-- Trip Type Selection -->\n        <div class=\"trip-type-selector\">\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'oneWay'\"\n            (click)=\"onTripTypeChange('oneWay')\">\n            One way\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'roundTrip'\"\n            (click)=\"onTripTypeChange('roundTrip')\">\n            Round Trip\n          </button>\n          <button\n            type=\"button\"\n            class=\"trip-type-btn\"\n            [class.active]=\"tripType === 'multiCity'\"\n            (click)=\"onTripTypeChange('multiCity')\">\n            Multi-City/Stop-Overs\n          </button>\n        </div>\n\n        <!-- Location and Date Selection -->\n        <div class=\"location-date-section\">\n          <!-- Flight Segments -->\n          <div class=\"flight-segments\">\n            <!-- First Segment (always visible) -->\n            <div class=\"flight-segment\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    formControlName=\"from\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    (locationSelected)=\"onFromLocationSelected($event)\">\n                  </app-autocomplete>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('from')\">\n                    {{ getErrorMessage('from') }}\n                  </div>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    formControlName=\"to\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    (locationSelected)=\"onToLocationSelected($event)\">\n                  </app-autocomplete>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('to')\">\n                    {{ getErrorMessage('to') }}\n                  </div>\n                </div>\n\n                <!-- Departure Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"departureDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('departureDate')\">\n                    {{ getErrorMessage('departureDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Second Segment (for Round Trip) -->\n            <div class=\"flight-segment\" *ngIf=\"showReturnDate\">\n              <div class=\"segment-row\">\n                <!-- Return From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [ngModel]=\"flightForm.get('to')?.value\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    [readonly]=\"true\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Return To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [ngModel]=\"flightForm.get('from')?.value\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    [readonly]=\"true\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Return Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      formControlName=\"returnDate\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                  <div class=\"error-message\" *ngIf=\"getErrorMessage('returnDate')\">\n                    {{ getErrorMessage('returnDate') }}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Additional Segments for Multi-City -->\n            <div class=\"flight-segment\" *ngFor=\"let segment of additionalSegments; let i = index\">\n              <div class=\"segment-row\">\n                <!-- From Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [(ngModel)]=\"segment.from\"\n                    placeholder=\"Leaving from (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-departure\"\n                    (locationSelected)=\"onSegmentLocationSelected($event, i, 'from')\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- To Location -->\n                <div class=\"form-group location-group\">\n                  <app-autocomplete\n                    [(ngModel)]=\"segment.to\"\n                    placeholder=\"Going to (City, Country Or Specific Airport)\"\n                    icon=\"fas fa-plane-arrival\"\n                    (locationSelected)=\"onSegmentLocationSelected($event, i, 'to')\">\n                  </app-autocomplete>\n                </div>\n\n                <!-- Date -->\n                <div class=\"form-group date-group\">\n                  <div class=\"date-input-wrapper\">\n                    <input\n                      type=\"date\"\n                      [(ngModel)]=\"segment.date\"\n                      placeholder=\"Choose A Date\"\n                      class=\"date-input\">\n                    <i class=\"fas fa-calendar-alt date-icon\"></i>\n                  </div>\n                </div>\n\n                <!-- Remove Segment Button -->\n                <button type=\"button\" class=\"remove-segment-btn\" (click)=\"removeSegment(i)\">\n                  <i class=\"fas fa-times\"></i>\n                </button>\n              </div>\n            </div>\n\n            <!-- Add Sector Button -->\n            <div class=\"add-sector-section\" *ngIf=\"tripType === 'multiCity'\">\n              <button type=\"button\" class=\"add-sector-btn\" (click)=\"addSegment()\">\n                <i class=\"fas fa-plus\"></i>\n                Add Sector\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Passenger and Class Selection -->\n        <div class=\"passenger-class-section\">\n          <!-- Passenger Count -->\n          <div class=\"form-group passenger-group\">\n            <label>Passenger & Class of travel</label>\n            <div class=\"passenger-controls\">\n              <!-- Adults -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-user\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('adults')?.value || 1 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('adults', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Children -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-child\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('children')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('children', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Infants -->\n              <div class=\"passenger-type\">\n                <span class=\"passenger-icon\"><i class=\"fas fa-baby\"></i></span>\n                <span class=\"passenger-count\">{{ flightForm.get('infants')?.value || 0 }}</span>\n                <div class=\"counter-controls\">\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', false)\">-</button>\n                  <button type=\"button\" class=\"counter-btn\" (click)=\"updatePassengerCount('infants', true)\">+</button>\n                </div>\n              </div>\n\n              <!-- Class Selection -->\n              <div class=\"class-selection\">\n                <button\n                  type=\"button\"\n                  class=\"class-btn\"\n                  [class.active]=\"selectedClass === 'economy'\"\n                  (click)=\"onClassChange('economy')\">\n                  <i class=\"fas fa-chair\"></i> Economy\n                </button>\n              </div>\n            </div>\n          </div>\n\n          <!-- Preferred Airline -->\n          <div class=\"form-group airline-group\">\n            <label for=\"preferredAirline\">Preferred Airline</label>\n            <select id=\"preferredAirline\" formControlName=\"preferredAirline\" class=\"airline-select\">\n              <option value=\"\">Preferred Airline</option>\n              <option value=\"TK\">Turkish Airlines</option>\n              <option value=\"AF\">Air France</option>\n              <option value=\"LH\">Lufthansa</option>\n              <option value=\"EK\">Emirates</option>\n              <option value=\"QR\">Qatar Airways</option>\n            </select>\n          </div>\n        </div>\n\n        <!-- Additional Options -->\n        <div class=\"additional-options\">\n          <!-- Refundable Fares -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Refundable fares</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"refundableFares\" class=\"option-select\">\n                <option value=\"false\">--All--</option>\n                <option value=\"true\">Refundable Only</option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Baggage -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Baggage</label>\n            <div class=\"option-controls\">\n              <select formControlName=\"baggage\" class=\"option-select\">\n                <option *ngFor=\"let option of baggageOptions\" [value]=\"option.value\">\n                  {{ option.label }}\n                </option>\n              </select>\n            </div>\n          </div>\n\n          <!-- Calendar -->\n          <div class=\"option-group\">\n            <label class=\"option-label\">Calendar</label>\n            <div class=\"option-controls\">\n              <div class=\"calendar-toggle\">\n                <input\n                  type=\"checkbox\"\n                  id=\"calendar\"\n                  formControlName=\"calendar\"\n                  (change)=\"toggleCalendar()\">\n                <label for=\"calendar\" class=\"calendar-label\">\n                  <span class=\"calendar-days\" *ngIf=\"showCalendar\">\n                    <select formControlName=\"calendarDays\" class=\"calendar-select\">\n                      <option *ngFor=\"let option of calendarDays\" [value]=\"option.value\">\n                        {{ option.label }}\n                      </option>\n                    </select>\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Search Button -->\n        <div class=\"search-button-section\">\n          <button\n            type=\"submit\"\n            class=\"search-btn\"\n            [disabled]=\"isLoading || !flightForm.valid\">\n            <span *ngIf=\"!isLoading\">SEARCH NOW</span>\n            <span *ngIf=\"isLoading\">\n              <i class=\"fas fa-spinner fa-spin\"></i> Searching...\n            </span>\n          </button>\n        </div>\n      </form>\n    </div>\n\n    <!-- Right Panel - Latest Searches -->\n    <div class=\"latest-searches-panel\">\n      <div class=\"searches-header\">\n        <h3>Latest Searches</h3>\n        <p>We're bringing you a new level of comfort</p>\n      </div>\n\n      <div class=\"searches-list\">\n        <div\n          *ngFor=\"let search of latestSearches$ | async\"\n          class=\"search-item\"\n          (click)=\"loadLatestSearch(search)\">\n          <div class=\"search-icon\">\n            <i class=\"fas fa-plane\"></i>\n          </div>\n          <div class=\"search-details\">\n            <div class=\"search-route\">\n              Coming from <strong>{{ search.from }}</strong> - <strong>{{ search.to }}</strong> on {{ search.date | date:'MMM d, yyyy' }}\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div *ngIf=\"(latestSearches$ | async)?.length === 0\" class=\"empty-searches\">\n          <i class=\"fas fa-search\"></i>\n          <p>No recent searches</p>\n          <small>Your recent flight searches will appear here</small>\n        </div>\n      </div>\n\n      <!-- Clear searches button -->\n      <div class=\"searches-actions\" *ngIf=\"(latestSearches$ | async)?.length! > 0\">\n        <button type=\"button\" class=\"clear-btn\" (click)=\"clearLatestSearches()\">\n          <i class=\"fas fa-trash\"></i> Clear All\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n", "import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild, forwardRef } from '@angular/core';\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject, Observable } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\nimport { AutocompleteService, AutocompleteLocation } from '../../../services/autocomplete.service';\n\n@Component({\n  selector: 'app-autocomplete',\n  templateUrl: './autocomplete.component.html',\n  styleUrls: ['./autocomplete.component.css'],\n  providers: [\n    {\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => AutocompleteComponent),\n      multi: true\n    }\n  ]\n})\nexport class AutocompleteComponent implements OnInit, OnDestroy, ControlValueAccessor {\n  @Input() placeholder: string = '';\n  @Input() icon: string = '';\n  @Input() readonly: boolean = false;\n  @Output() locationSelected = new EventEmitter<AutocompleteLocation>();\n\n  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;\n\n  value: string = '';\n  suggestions: AutocompleteLocation[] = [];\n  showSuggestions: boolean = false;\n  selectedIndex: number = -1;\n  isLoading: boolean = false;\n\n  private searchSubject = new Subject<string>();\n  private destroy$ = new Subject<void>();\n  private onChange = (value: string) => {};\n  private onTouched = () => {};\n\n  constructor(\n    private autocompleteService: AutocompleteService,\n    private elementRef: ElementRef\n  ) {}\n\n  ngOnInit(): void {\n    // Setup search with debouncing\n    this.searchSubject.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(query => this.autocompleteService.searchLocations(query)),\n      takeUntil(this.destroy$)\n    ).subscribe(suggestions => {\n      this.suggestions = suggestions;\n      this.showSuggestions = suggestions.length > 0;\n      this.isLoading = false;\n    });\n\n    // Close suggestions when clicking outside\n    document.addEventListener('click', this.onDocumentClick.bind(this));\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n    document.removeEventListener('click', this.onDocumentClick.bind(this));\n  }\n\n  // ControlValueAccessor implementation\n  writeValue(value: string): void {\n    this.value = value || '';\n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n\n  registerOnChange(fn: (value: string) => void): void {\n    this.onChange = fn;\n  }\n\n  registerOnTouched(fn: () => void): void {\n    this.onTouched = fn;\n  }\n\n  setDisabledState(isDisabled: boolean): void {\n    if (this.inputElement) {\n      this.inputElement.nativeElement.disabled = isDisabled;\n    }\n  }\n\n  onInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.value = target.value;\n    this.onChange(this.value);\n\n    if (this.value.length >= 2) {\n      this.isLoading = true;\n      this.searchSubject.next(this.value);\n    } else {\n      this.suggestions = [];\n      this.showSuggestions = false;\n      this.isLoading = false;\n    }\n\n    this.selectedIndex = -1;\n  }\n\n  onFocus(): void {\n    this.onTouched();\n    if (this.value.length >= 2) {\n      this.showSuggestions = this.suggestions.length > 0;\n    } else if (this.value.length === 0) {\n      // Show popular destinations when focusing on empty input\n      this.autocompleteService.getPopularDestinations().subscribe(destinations => {\n        this.suggestions = destinations;\n        this.showSuggestions = true;\n      });\n    }\n  }\n\n  onKeyDown(event: KeyboardEvent): void {\n    if (!this.showSuggestions) return;\n\n    switch (event.key) {\n      case 'ArrowDown':\n        event.preventDefault();\n        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);\n        break;\n      case 'Enter':\n        event.preventDefault();\n        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {\n          this.selectSuggestion(this.suggestions[this.selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        this.hideSuggestions();\n        break;\n    }\n  }\n\n  selectSuggestion(location: AutocompleteLocation): void {\n    this.value = location.displayText;\n    this.onChange(this.value);\n    this.locationSelected.emit(location);\n    this.hideSuggestions();\n\n    if (this.inputElement) {\n      this.inputElement.nativeElement.value = this.value;\n    }\n  }\n\n  hideSuggestions(): void {\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n  }\n\n  private onDocumentClick(event: Event): void {\n    if (!this.elementRef.nativeElement.contains(event.target as Node)) {\n      this.hideSuggestions();\n    }\n  }\n\n  getLocationTypeIcon(type: string): string {\n    switch (type) {\n      case 'airport':\n        return 'fas fa-plane';\n      case 'city':\n        return 'fas fa-city';\n      case 'country':\n        return 'fas fa-flag';\n      default:\n        return 'fas fa-map-marker-alt';\n    }\n  }\n}\n", "<div class=\"autocomplete-wrapper\">\n  <div class=\"input-wrapper\">\n    <i *ngIf=\"icon\" [class]=\"icon\" class=\"input-icon\"></i>\n    <input\n      #inputElement\n      type=\"text\"\n      [placeholder]=\"placeholder\"\n      [readonly]=\"readonly\"\n      class=\"autocomplete-input\"\n      [class.with-icon]=\"icon\"\n      [class.readonly]=\"readonly\"\n      (input)=\"onInput($event)\"\n      (focus)=\"onFocus()\"\n      (keydown)=\"onKeyDown($event)\"\n      autocomplete=\"off\">\n    <div *ngIf=\"isLoading\" class=\"loading-spinner\">\n      <i class=\"fas fa-spinner fa-spin\"></i>\n    </div>\n  </div>\n\n  <div *ngIf=\"showSuggestions\" class=\"suggestions-dropdown\">\n    <div class=\"suggestions-list\">\n      <div\n        *ngFor=\"let suggestion of suggestions; let i = index\"\n        class=\"suggestion-item\"\n        [class.selected]=\"i === selectedIndex\"\n        (click)=\"selectSuggestion(suggestion)\"\n        (mouseenter)=\"selectedIndex = i\">\n        \n        <div class=\"suggestion-icon\">\n          <i [class]=\"getLocationTypeIcon(suggestion.type)\"></i>\n        </div>\n        \n        <div class=\"suggestion-content\">\n          <div class=\"suggestion-main\">{{ suggestion.displayText }}</div>\n          <div *ngIf=\"suggestion.type === 'airport' && suggestion.country\" class=\"suggestion-details\">\n            {{ suggestion.country }}\n          </div>\n        </div>\n        \n        <div class=\"suggestion-type\">\n          <span class=\"type-badge\" [class]=\"'type-' + suggestion.type\">\n            {{ suggestion.type }}\n          </span>\n        </div>\n      </div>\n    </div>\n    \n    <div *ngIf=\"suggestions.length === 0 && !isLoading\" class=\"no-results\">\n      <i class=\"fas fa-search\"></i>\n      <span>No locations found</span>\n    </div>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { LoginCredentials } from '../../models/auth.models';\n\n@Component({\n  selector: 'app-signin',\n  templateUrl: './signin.component.html',\n  styleUrls: ['./signin.component.css']\n})\nexport class SigninComponent implements OnInit {\n  signinForm: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.signinForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // If user is already authenticated, redirect to dashboard\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.signinForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      const credentials: LoginCredentials = {\n        agency: this.signinForm.value.agency.trim(),\n        username: this.signinForm.value.username.trim(),\n        password: this.signinForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          if (response.success) {\n            // Redirect to dashboard or intended route\n            this.router.navigate(['/dashboard']);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n\n          // Clear password field on error\n          this.signinForm.patchValue({ password: '' });\n        },\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Mark all form fields as touched to show validation errors\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.signinForm.controls).forEach(key => {\n      const control = this.signinForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Get form control for easier access in template\n   */\n  getFormControl(controlName: string) {\n    return this.signinForm.get(controlName);\n  }\n\n  /**\n   * Check if form control has error\n   */\n  hasError(controlName: string, errorType: string): boolean {\n    const control = this.getFormControl(controlName);\n    return !!(control?.hasError(errorType) && control?.touched);\n  }\n\n  /**\n   * Get error message for form control\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.getFormControl(controlName);\n\n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(controlName)} is required`;\n    }\n\n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;\n    }\n\n    return '';\n  }\n\n  /**\n   * Get display name for form field\n   */\n  private getFieldDisplayName(controlName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      agency: 'Agency',\n      username: 'Username',\n      password: 'Password'\n    };\n    return fieldNames[controlName] || controlName;\n  }\n\n  /**\n   * Clear error message\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"signin-container\">\n  <div class=\"signin-card\">\n    <!-- Header -->\n    <div class=\"signin-header\">\n      <div class=\"logo-container\">\n        <div class=\"logo\">\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n      </div>\n      <h1 class=\"signin-title\">Agency Portal</h1>\n      <p class=\"signin-subtitle\">Sign in to your agency account</p>\n    </div>\n\n    <!-- Error Message -->\n    <div class=\"error-container\" *ngIf=\"errorMessage\">\n      <div class=\"error-message\">\n        <svg class=\"error-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Sign-in Form -->\n    <form [formGroup]=\"signinForm\" (ngSubmit)=\"onSubmit()\" class=\"signin-form\">\n      <!-- Agency Field -->\n      <div class=\"form-group\">\n        <label for=\"agency\" class=\"form-label\">Agency Code</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"agency\"\n            formControlName=\"agency\"\n            class=\"form-input\"\n            [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n            placeholder=\"Enter your agency code\"\n            autocomplete=\"organization\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('agency', 'required') || hasError('agency', 'minlength')\">\n          {{ getErrorMessage('agency') }}\n        </div>\n      </div>\n\n      <!-- Username Field -->\n      <div class=\"form-group\">\n        <label for=\"username\" class=\"form-label\">Username</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"username\"\n            formControlName=\"username\"\n            class=\"form-input\"\n            [class.error]=\"hasError('username', 'required') || hasError('username', 'minlength')\"\n            placeholder=\"Enter your username\"\n            autocomplete=\"username\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('username', 'required') || hasError('username', 'minlength')\">\n          {{ getErrorMessage('username') }}\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"form-group\">\n        <label for=\"password\" class=\"form-label\">Password</label>\n        <div class=\"input-container\">\n          <input\n            [type]=\"showPassword ? 'text' : 'password'\"\n            id=\"password\"\n            formControlName=\"password\"\n            class=\"form-input\"\n            [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n            placeholder=\"Enter your password\"\n            autocomplete=\"current-password\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n          <button\n            type=\"button\"\n            class=\"password-toggle\"\n            (click)=\"togglePasswordVisibility()\"\n            [attr.aria-label]=\"showPassword ? 'Hide password' : 'Show password'\"\n          >\n            <svg *ngIf=\"!showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            </svg>\n            <svg *ngIf=\"showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <path d=\"M1 1L23 23\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </button>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('password', 'required') || hasError('password', 'minlength')\">\n          {{ getErrorMessage('password') }}\n        </div>\n      </div>\n\n      <!-- Submit Button -->\n      <button\n        type=\"submit\"\n        class=\"signin-button\"\n        [disabled]=\"isLoading || signinForm.invalid\"\n        [class.loading]=\"isLoading\"\n      >\n        <span *ngIf=\"!isLoading\">Sign In</span>\n        <span *ngIf=\"isLoading\" class=\"loading-content\">\n          <svg class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M12 3C16.9706 3 21 7.02944 21 12\" stroke=\"url(#spinner-gradient)\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n            <defs>\n              <linearGradient id=\"spinner-gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                <stop offset=\"0%\" style=\"stop-color:currentColor;stop-opacity:0\" />\n                <stop offset=\"100%\" style=\"stop-color:currentColor;stop-opacity:1\" />\n              </linearGradient>\n            </defs>\n          </svg>\n          Signing in...\n        </span>\n      </button>\n    </form>\n\n    <!-- Footer -->\n    <div class=\"signin-footer\">\n      <p class=\"footer-text\">\n        Need help? Contact your system administrator\n      </p>\n    </div>\n  </div>\n\n  <!-- Background decoration -->\n  <div class=\"background-decoration\">\n    <div class=\"decoration-circle decoration-circle-1\"></div>\n    <div class=\"decoration-circle decoration-circle-2\"></div>\n    <div class=\"decoration-circle decoration-circle-3\"></div>\n  </div>\n</div>\n", "import { Injectable } from '@angular/core';\nimport { CanActivate, Router, UrlTree } from '@angular/router';\nimport { Observable } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {\n    if (this.authService.isAuthenticated()) {\n      return true;\n    } else {\n      // Redirect to signin page if not authenticated\n      return this.router.createUrlTree(['/signin']);\n    }\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { AuthRequest, AuthResponse, LoginCredentials, LoginResponse } from '../models/auth.models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = `${environment.apiUrl}${environment.authEndpoint}`;\n  private readonly TOKEN_KEY = 'auth_token';\n  private readonly USER_KEY = 'user_data';\n\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Authenticate user with agency credentials\n   */\n  login(credentials: LoginCredentials): Observable<LoginResponse> {\n    const authRequest: AuthRequest = {\n      Agency: credentials.agency,\n      User: credentials.username,\n      Password: credentials.password\n    };\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    return this.http.post<AuthResponse>(this.API_URL, authRequest, { headers })\n      .pipe(\n        map(response => {\n          if (response.header.success && response.body.token) {\n            // Store token and user data\n            this.setToken(response.body.token);\n            const userData = {\n              id: response.body.user.id,\n              name: response.body.user.name,\n              email: response.body.user.email,\n              agency: response.body.agency.name,\n              agencyCode: response.body.agency.code\n            };\n            this.setUserData(userData);\n\n            // Update subjects\n            this.isAuthenticatedSubject.next(true);\n            this.currentUserSubject.next(userData);\n\n            return {\n              success: true,\n              token: response.body.token,\n              user: userData,\n              message: 'Login successful'\n            };\n          } else {\n            throw new Error(response.header.responseMessage || 'Authentication failed');\n          }\n        }),\n        catchError(error => {\n          console.error('Login error:', error);\n          let errorMessage = 'An error occurred during login';\n\n          if (error.error?.header?.responseMessage) {\n            errorMessage = error.error.header.responseMessage;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.status === 401) {\n            errorMessage = 'Invalid credentials';\n          } else if (error.status === 0) {\n            errorMessage = 'Unable to connect to server';\n          }\n\n          return throwError(() => ({\n            success: false,\n            message: errorMessage\n          }));\n        })\n      );\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.removeToken();\n    this.removeUserData();\n    this.isAuthenticatedSubject.next(false);\n    this.currentUserSubject.next(null);\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.hasToken() && !this.isTokenExpired();\n  }\n\n  /**\n   * Get current user data\n   */\n  getCurrentUser(): any {\n    const userData = localStorage.getItem(this.USER_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n\n  /**\n   * Get authentication token\n   */\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set authentication token\n   */\n  private setToken(token: string): void {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n\n  /**\n   * Remove authentication token\n   */\n  private removeToken(): void {\n    localStorage.removeItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set user data\n   */\n  private setUserData(userData: any): void {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));\n  }\n\n  /**\n   * Remove user data\n   */\n  private removeUserData(): void {\n    localStorage.removeItem(this.USER_KEY);\n  }\n\n  /**\n   * Check if token exists\n   */\n  private hasToken(): boolean {\n    return !!localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Check if token is expired (basic check)\n   * Note: This is a simple implementation. In production, you should decode the JWT token\n   * and check the expiration time properly.\n   */\n  private isTokenExpired(): boolean {\n    // For now, we'll assume the token is valid if it exists\n    // In a real implementation, you would decode the JWT and check the exp claim\n    return false;\n  }\n\n  /**\n   * Get authorization headers for API calls\n   */\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { map, debounceTime, distinctUntilChanged, switchMap, catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\nexport interface AutocompleteLocation {\n  id: string;\n  name: string;\n  code: string;\n  type: 'airport' | 'city' | 'country';\n  country?: string;\n  city?: string;\n  airport?: string;\n  displayText: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AutocompleteService {\n  private readonly API_URL = `${environment.apiUrl}/location`;\n  \n  // Mock data for demonstration - in real app, this would come from API\n  private mockLocations: AutocompleteLocation[] = [\n    // Major airports\n    { id: 'JFK', name: 'John F. Kennedy International Airport', code: 'JFK', type: 'airport', country: 'United States', city: 'New York', airport: 'JFK', displayText: 'New York (JFK) - John <PERSON> International Airport' },\n    { id: 'LAX', name: 'Los Angeles International Airport', code: 'LAX', type: 'airport', country: 'United States', city: 'Los Angeles', airport: 'LAX', displayText: 'Los Angeles (LAX) - Los Angeles International Airport' },\n    { id: 'LHR', name: 'London Heathrow Airport', code: 'LHR', type: 'airport', country: 'United Kingdom', city: 'London', airport: 'LHR', displayText: 'London (LHR) - London Heathrow Airport' },\n    { id: 'CDG', name: 'Charles de Gaulle Airport', code: 'CDG', type: 'airport', country: 'France', city: 'Paris', airport: 'CDG', displayText: 'Paris (CDG) - Charles de Gaulle Airport' },\n    { id: 'DXB', name: 'Dubai International Airport', code: 'DXB', type: 'airport', country: 'United Arab Emirates', city: 'Dubai', airport: 'DXB', displayText: 'Dubai (DXB) - Dubai International Airport' },\n    { id: 'NRT', name: 'Narita International Airport', code: 'NRT', type: 'airport', country: 'Japan', city: 'Tokyo', airport: 'NRT', displayText: 'Tokyo (NRT) - Narita International Airport' },\n    { id: 'SIN', name: 'Singapore Changi Airport', code: 'SIN', type: 'airport', country: 'Singapore', city: 'Singapore', airport: 'SIN', displayText: 'Singapore (SIN) - Singapore Changi Airport' },\n    { id: 'FRA', name: 'Frankfurt Airport', code: 'FRA', type: 'airport', country: 'Germany', city: 'Frankfurt', airport: 'FRA', displayText: 'Frankfurt (FRA) - Frankfurt Airport' },\n    { id: 'AMS', name: 'Amsterdam Airport Schiphol', code: 'AMS', type: 'airport', country: 'Netherlands', city: 'Amsterdam', airport: 'AMS', displayText: 'Amsterdam (AMS) - Amsterdam Airport Schiphol' },\n    { id: 'IST', name: 'Istanbul Airport', code: 'IST', type: 'airport', country: 'Turkey', city: 'Istanbul', airport: 'IST', displayText: 'Istanbul (IST) - Istanbul Airport' },\n    { id: 'TUN', name: 'Tunis-Carthage International Airport', code: 'TUN', type: 'airport', country: 'Tunisia', city: 'Tunis', airport: 'TUN', displayText: 'Tunis (TUN) - Tunis-Carthage International Airport' },\n    { id: 'CAI', name: 'Cairo International Airport', code: 'CAI', type: 'airport', country: 'Egypt', city: 'Cairo', airport: 'CAI', displayText: 'Cairo (CAI) - Cairo International Airport' },\n    { id: 'CMN', name: 'Mohammed V International Airport', code: 'CMN', type: 'airport', country: 'Morocco', city: 'Casablanca', airport: 'CMN', displayText: 'Casablanca (CMN) - Mohammed V International Airport' },\n    { id: 'ALG', name: 'Houari Boumediene Airport', code: 'ALG', type: 'airport', country: 'Algeria', city: 'Algiers', airport: 'ALG', displayText: 'Algiers (ALG) - Houari Boumediene Airport' },\n    \n    // Cities\n    { id: 'NYC', name: 'New York', code: 'NYC', type: 'city', country: 'United States', city: 'New York', displayText: 'New York, United States' },\n    { id: 'LON', name: 'London', code: 'LON', type: 'city', country: 'United Kingdom', city: 'London', displayText: 'London, United Kingdom' },\n    { id: 'PAR', name: 'Paris', code: 'PAR', type: 'city', country: 'France', city: 'Paris', displayText: 'Paris, France' },\n    { id: 'DUB', name: 'Dubai', code: 'DUB', type: 'city', country: 'United Arab Emirates', city: 'Dubai', displayText: 'Dubai, United Arab Emirates' },\n    { id: 'TOK', name: 'Tokyo', code: 'TOK', type: 'city', country: 'Japan', city: 'Tokyo', displayText: 'Tokyo, Japan' },\n    { id: 'TUN_CITY', name: 'Tunis', code: 'TUN', type: 'city', country: 'Tunisia', city: 'Tunis', displayText: 'Tunis, Tunisia' },\n    \n    // Countries\n    { id: 'US', name: 'United States', code: 'US', type: 'country', country: 'United States', displayText: 'United States' },\n    { id: 'UK', name: 'United Kingdom', code: 'UK', type: 'country', country: 'United Kingdom', displayText: 'United Kingdom' },\n    { id: 'FR', name: 'France', code: 'FR', type: 'country', country: 'France', displayText: 'France' },\n    { id: 'TN', name: 'Tunisia', code: 'TN', type: 'country', country: 'Tunisia', displayText: 'Tunisia' },\n    { id: 'AE', name: 'United Arab Emirates', code: 'AE', type: 'country', country: 'United Arab Emirates', displayText: 'United Arab Emirates' },\n    { id: 'JP', name: 'Japan', code: 'JP', type: 'country', country: 'Japan', displayText: 'Japan' }\n  ];\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Search locations based on query string\n   */\n  searchLocations(query: string): Observable<AutocompleteLocation[]> {\n    if (!query || query.length < 2) {\n      return of([]);\n    }\n\n    // For now, use mock data. In production, replace with actual API call\n    return of(this.mockLocations).pipe(\n      map(locations => \n        locations.filter(location => \n          location.name.toLowerCase().includes(query.toLowerCase()) ||\n          location.code.toLowerCase().includes(query.toLowerCase()) ||\n          (location.city && location.city.toLowerCase().includes(query.toLowerCase())) ||\n          (location.country && location.country.toLowerCase().includes(query.toLowerCase()))\n        ).slice(0, 10) // Limit to 10 results\n      )\n    );\n\n    // Uncomment this for actual API integration:\n    /*\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return of([]);\n    }\n\n    const headers = new HttpHeaders({\n      'Authorization': `Bearer ${token}`\n    });\n\n    return this.http.get<any>(`${this.API_URL}/search`, {\n      headers,\n      params: { query, limit: '10' }\n    }).pipe(\n      map(response => this.mapApiResponseToLocations(response)),\n      catchError(error => {\n        console.error('Location search error:', error);\n        return of([]);\n      })\n    );\n    */\n  }\n\n  /**\n   * Get popular destinations\n   */\n  getPopularDestinations(): Observable<AutocompleteLocation[]> {\n    return of(this.mockLocations.filter(location => \n      ['JFK', 'LAX', 'LHR', 'CDG', 'DXB', 'TUN', 'IST', 'FRA'].includes(location.code)\n    ));\n  }\n\n  /**\n   * Map API response to AutocompleteLocation format\n   */\n  private mapApiResponseToLocations(response: any): AutocompleteLocation[] {\n    // This would map the actual API response to our interface\n    // Implementation depends on the actual API structure\n    return response.data?.map((item: any) => ({\n      id: item.id,\n      name: item.name,\n      code: item.code,\n      type: item.type,\n      country: item.country?.name,\n      city: item.city?.name,\n      airport: item.airport?.name,\n      displayText: this.formatDisplayText(item)\n    })) || [];\n  }\n\n  /**\n   * Format display text for location\n   */\n  private formatDisplayText(location: any): string {\n    if (location.type === 'airport') {\n      return `${location.city?.name || location.name} (${location.code}) - ${location.name}`;\n    } else if (location.type === 'city') {\n      return `${location.name}, ${location.country?.name}`;\n    } else {\n      return location.name;\n    }\n  }\n}\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { \n  FlightSearchRequest, \n  FlightSearchResponse, \n  FlightSearchForm,\n  LatestSearch,\n  Location \n} from '../models/flight.models';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FlightService {\n  private readonly API_URL = `${environment.apiUrl}/product`;\n  private readonly LATEST_SEARCHES_KEY = 'flight_latest_searches';\n  private readonly MAX_LATEST_SEARCHES = 6;\n\n  private latestSearchesSubject = new BehaviorSubject<LatestSearch[]>(this.getLatestSearches());\n  public latestSearches$ = this.latestSearchesSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Search for one-way flights\n   */\n  searchOneWayFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {\n    const request = this.buildOneWayRequest(searchForm);\n    return this.searchFlights(request);\n  }\n\n  /**\n   * Search for round-trip flights\n   */\n  searchRoundTripFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {\n    const request = this.buildRoundTripRequest(searchForm);\n    return this.searchFlights(request);\n  }\n\n  /**\n   * Search for multi-city flights\n   */\n  searchMultiCityFlights(searchForm: FlightSearchForm): Observable<FlightSearchResponse> {\n    const request = this.buildMultiCityRequest(searchForm);\n    return this.searchFlights(request);\n  }\n\n  /**\n   * Generic flight search method\n   */\n  private searchFlights(request: FlightSearchRequest): Observable<FlightSearchResponse> {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      return throwError('Authentication token not found');\n    }\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n\n    return this.http.post<FlightSearchResponse>(`${this.API_URL}/pricesearch`, request, { headers })\n      .pipe(\n        tap(response => {\n          if (response.header.success) {\n            console.log('Flight search successful:', response);\n          } else {\n            console.error('Flight search failed:', response.header.messages);\n          }\n        }),\n        catchError(error => {\n          console.error('Flight search error:', error);\n          return throwError(error);\n        })\n      );\n  }\n\n  /**\n   * Build one-way flight request\n   */\n  private buildOneWayRequest(searchForm: FlightSearchForm): FlightSearchRequest {\n    return {\n      ProductType: 2, // Flight product type\n      ServiceTypes: ['1'], // One-way service type\n      CheckIn: searchForm.departureDate,\n      DepartureLocations: [this.parseLocation(searchForm.from)],\n      ArrivalLocations: [this.parseLocation(searchForm.to)],\n      Passengers: this.buildPassengers(searchForm.passengers),\n      showOnlyNonStopFlight: searchForm.directFlights,\n      acceptPendingProviders: false,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      calculateFlightFees: true,\n      flightClasses: [this.getFlightClassCode(searchForm.class)],\n      Culture: 'en-US',\n      Currency: 'USD'\n    };\n  }\n\n  /**\n   * Build round-trip flight request\n   */\n  private buildRoundTripRequest(searchForm: FlightSearchForm): FlightSearchRequest {\n    const request = this.buildOneWayRequest(searchForm);\n    request.ServiceTypes = ['2']; // Round-trip service type\n    request.ReturnDate = searchForm.returnDate;\n    request.Night = this.calculateNights(searchForm.departureDate, searchForm.returnDate!);\n    return request;\n  }\n\n  /**\n   * Build multi-city flight request\n   */\n  private buildMultiCityRequest(searchForm: FlightSearchForm): FlightSearchRequest {\n    const request = this.buildOneWayRequest(searchForm);\n    request.ServiceTypes = ['3']; // Multi-city service type\n    return request;\n  }\n\n  /**\n   * Parse location string to Location object\n   */\n  private parseLocation(locationString: string): Location {\n    // For now, assume the format is \"City, Country or Specific Airport\"\n    // In a real implementation, you'd have a location search service\n    const parts = locationString.split(',');\n    const code = parts[0].trim().toUpperCase();\n    \n    return {\n      type: 1, // Airport type\n      id: code,\n      name: locationString,\n      code: code\n    };\n  }\n\n  /**\n   * Build passengers array from form data\n   */\n  private buildPassengers(passengers: { adults: number; children: number; infants: number }): any[] {\n    const passengerArray = [];\n    \n    if (passengers.adults > 0) {\n      passengerArray.push({ type: 1, count: passengers.adults }); // Adult\n    }\n    \n    if (passengers.children > 0) {\n      passengerArray.push({ type: 2, count: passengers.children }); // Child\n    }\n    \n    if (passengers.infants > 0) {\n      passengerArray.push({ type: 3, count: passengers.infants }); // Infant\n    }\n    \n    return passengerArray;\n  }\n\n  /**\n   * Get flight class code\n   */\n  private getFlightClassCode(flightClass: string): number {\n    switch (flightClass) {\n      case 'economy': return 1;\n      case 'business': return 2;\n      case 'first': return 3;\n      default: return 1;\n    }\n  }\n\n  /**\n   * Calculate nights between dates\n   */\n  private calculateNights(checkIn: string, returnDate: string): number {\n    const checkInDate = new Date(checkIn);\n    const returnDateObj = new Date(returnDate);\n    const timeDiff = returnDateObj.getTime() - checkInDate.getTime();\n    return Math.ceil(timeDiff / (1000 * 3600 * 24));\n  }\n\n  /**\n   * Save search to latest searches\n   */\n  saveLatestSearch(searchForm: FlightSearchForm): void {\n    const search: LatestSearch = {\n      id: Date.now().toString(),\n      from: searchForm.from,\n      to: searchForm.to,\n      date: searchForm.departureDate,\n      passengers: searchForm.passengers.adults + searchForm.passengers.children + searchForm.passengers.infants,\n      searchDate: new Date()\n    };\n\n    const searches = this.getLatestSearches();\n    searches.unshift(search);\n    \n    // Keep only the latest searches\n    const limitedSearches = searches.slice(0, this.MAX_LATEST_SEARCHES);\n    \n    localStorage.setItem(this.LATEST_SEARCHES_KEY, JSON.stringify(limitedSearches));\n    this.latestSearchesSubject.next(limitedSearches);\n  }\n\n  /**\n   * Get latest searches from localStorage\n   */\n  private getLatestSearches(): LatestSearch[] {\n    try {\n      const searches = localStorage.getItem(this.LATEST_SEARCHES_KEY);\n      return searches ? JSON.parse(searches) : [];\n    } catch (error) {\n      console.error('Error parsing latest searches:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Clear latest searches\n   */\n  clearLatestSearches(): void {\n    localStorage.removeItem(this.LATEST_SEARCHES_KEY);\n    this.latestSearchesSubject.next([]);\n  }\n\n  /**\n   * Get popular destinations (mock data for now)\n   */\n  getPopularDestinations(): Observable<Location[]> {\n    const destinations: Location[] = [\n      { type: 1, id: 'TUN', name: 'Tunis, Tunisia', code: 'TUN' },\n      { type: 1, id: 'IST', name: 'Istanbul, Turkey', code: 'IST' },\n      { type: 1, id: 'CDG', name: 'Paris, France', code: 'CDG' },\n      { type: 1, id: 'LHR', name: 'London, UK', code: 'LHR' },\n      { type: 1, id: 'DXB', name: 'Dubai, UAE', code: 'DXB' },\n      { type: 1, id: 'JFK', name: 'New York, USA', code: 'JFK' }\n    ];\n    \n    return new Observable(observer => {\n      observer.next(destinations);\n      observer.complete();\n    });\n  }\n\n  /**\n   * Search locations (mock implementation)\n   */\n  searchLocations(query: string): Observable<Location[]> {\n    return this.getPopularDestinations().pipe(\n      map(destinations => \n        destinations.filter(dest => \n          dest.name.toLowerCase().includes(query.toLowerCase()) ||\n          dest.code.toLowerCase().includes(query.toLowerCase())\n        )\n      )\n    );\n  }\n}\n", "export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080',\n  authEndpoint: '/auth/login',\n  flightEndpoint: '/product'\n};\n", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\n\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.error(err));\n"], "names": ["RouterModule", "SigninComponent", "DashboardComponent", "FlightComponent", "FlightResultsComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports", "AppComponent", "constructor", "title", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "BrowserModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "AutocompleteComponent", "AppModule", "bootstrap", "declarations", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "currentUser", "name", "ctx_r1", "agencyCode", "authService", "router", "ngOnInit", "currentUser$", "subscribe", "user", "isAuthenticated", "navigate", "navigateToFlights", "logout", "ɵɵdirectiveInject", "AuthService", "i2", "Router", "consts", "DashboardComponent_Template", "ɵɵtemplate", "DashboardComponent_p_7_Template", "ɵɵlistener", "DashboardComponent_Template_button_click_19_listener", "DashboardComponent_div_22_Template", "DashboardComponent_Template_div_click_58_listener", "ɵɵproperty", "Validators", "FlightResultsComponent_button_37_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "airline_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "toggleAirlineFilter", "ɵɵclassProp", "active", "ɵɵtextInterpolate", "code", "airline_r11", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r13", "formatPrice", "flight_r15", "price", "FlightResultsComponent_div_46_ng_container_1_Template", "FlightResultsComponent_div_46_ng_container_2_Template", "ctx_r4", "getFlightForAirlineAndStops", "airline_r12", "ctx_r17", "flight_r19", "FlightResultsComponent_div_51_ng_container_1_Template", "FlightResultsComponent_div_51_ng_container_2_Template", "ctx_r5", "airline_r16", "ctx_r21", "flight_r23", "FlightResultsComponent_div_56_ng_container_1_Template", "FlightResultsComponent_div_56_ng_container_2_Template", "ctx_r6", "airline_r20", "route", "fb", "flightService", "searchResults", "isLoading", "originalSearchParams", "airlineFilters", "logo", "flightsByStops", "nonStop", "oneStop", "twoOrMoreStops", "searchForm", "createSearchForm", "navigation", "getCurrentNavigation", "extras", "state", "processSearchResults", "initializeSearchForm", "group", "from", "required", "to", "departureDate", "adults", "min", "children", "infants", "patchValue", "passengers", "body", "flights", "for<PERSON>ach", "flight", "flightResult", "convertToFlightResult", "stopCount", "items", "push", "sort", "a", "b", "firstItem", "firstOffer", "offers", "airline", "airlineLogo", "getAirlineLogo", "amount", "currency", "stops", "duration", "formatDuration", "departureTime", "formatTime", "departure", "arrivalTime", "arrival", "flightNumber", "flightNo", "airlineCode", "find", "durationMinutes", "hours", "Math", "floor", "minutes", "timeString", "getSearchSummary", "date", "Date", "toLocaleDateString", "year", "month", "day", "onNewSearch", "onSearchAgain", "valid", "formValue", "value", "tripType", "class", "directFlights", "refundableFares", "baggage", "calendar", "searchOneWayFlights", "next", "response", "header", "success", "error", "console", "getFlightsForStops", "stopType", "getMinPriceForStops", "length", "map", "f", "getStopLabel", "includes", "ActivatedRoute", "FormBuilder", "i3", "FlightService", "FlightResultsComponent_Template", "FlightResultsComponent_Template_button_click_29_listener", "FlightResultsComponent_Template_button_click_31_listener", "FlightResultsComponent_span_32_Template", "FlightResultsComponent_span_33_Template", "FlightResultsComponent_button_37_Template", "FlightResultsComponent_div_41_Template", "FlightResultsComponent_div_46_Template", "FlightResultsComponent_div_51_Template", "FlightResultsComponent_div_56_Template", "FlightResultsComponent_div_57_Template", "getErrorMessage", "ctx_r2", "FlightComponent_div_33_div_10_Template", "tmp_0_0", "ctx_r3", "flightForm", "get", "tmp_2_0", "FlightComponent_div_34_Template_app_autocomplete_ngModelChange_3_listener", "$event", "_r17", "segment_r14", "FlightComponent_div_34_Template_app_autocomplete_locationSelected_3_listener", "i_r15", "index", "ctx_r18", "onSegmentLocationSelected", "FlightComponent_div_34_Template_app_autocomplete_ngModelChange_5_listener", "FlightComponent_div_34_Template_app_autocomplete_locationSelected_5_listener", "ctx_r20", "FlightComponent_div_34_Template_input_ngModelChange_8_listener", "FlightComponent_div_34_Template_button_click_10_listener", "ctx_r22", "removeSegment", "FlightComponent_div_35_Template_button_click_1_listener", "_r24", "ctx_r23", "addSegment", "option_r25", "label", "option_r27", "FlightComponent_span_114_option_2_Template", "ctx_r7", "calendarDays", "FlightComponent_div_126_Template_div_click_0_listener", "_r30", "search_r28", "ctx_r29", "loadLatestSearch", "ɵɵpipeBind2", "FlightComponent_div_130_Template_button_click_1_listener", "_r32", "ctx_r31", "clearLatestSearches", "showReturnDate", "showCalendar", "additionalSegments", "adultCount", "childCount", "infantCount", "selectedClass", "baggageOptions", "createForm", "latestSearches$", "setDefaultDates", "returnDate", "preferredAirline", "today", "tomorrow", "setDate", "getDate", "nextWeek", "formatDate", "toISOString", "split", "onTripTypeChange", "type", "setValidators", "clearValidators", "updateValueAndValidity", "newSegment", "splice", "onFromLocationSelected", "location", "displayText", "onToLocationSelected", "segmentIndex", "field", "updatePassengerCount", "increment", "currentValue", "newValue", "max", "getTotalPassengers", "onClassChange", "flightClass", "toggleCalendar", "swapLocations", "onSubmit", "saveLatestSearch", "searchObservable", "searchRoundTripFlights", "searchMultiCityFlights", "log", "results", "searchParams", "messages", "Object", "keys", "controls", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "search", "controlName", "control", "errors", "touched", "i4", "FlightComponent_Template", "FlightComponent_Template_form_ngSubmit_10_listener", "FlightComponent_Template_button_click_12_listener", "FlightComponent_Template_button_click_14_listener", "FlightComponent_Template_button_click_16_listener", "FlightComponent_Template_app_autocomplete_locationSelected_23_listener", "FlightComponent_div_24_Template", "FlightComponent_Template_app_autocomplete_locationSelected_26_listener", "FlightComponent_div_27_Template", "FlightComponent_div_32_Template", "FlightComponent_div_33_Template", "FlightComponent_div_34_Template", "FlightComponent_div_35_Template", "FlightComponent_Template_button_click_47_listener", "FlightComponent_Template_button_click_49_listener", "FlightComponent_Template_button_click_57_listener", "FlightComponent_Template_button_click_59_listener", "FlightComponent_Template_button_click_67_listener", "FlightComponent_Template_button_click_69_listener", "FlightComponent_Template_button_click_72_listener", "FlightComponent_option_106_Template", "FlightComponent_Template_input_change_112_listener", "FlightComponent_span_114_Template", "FlightComponent_span_117_Template", "FlightComponent_span_118_Template", "FlightComponent_div_126_Template", "FlightComponent_div_128_Template", "FlightComponent_div_130_Template", "tmp_10_0", "tmp_11_0", "tmp_12_0", "ɵɵpipeBind1", "tmp_20_0", "tmp_21_0", "EventEmitter", "forwardRef", "NG_VALUE_ACCESSOR", "Subject", "debounceTime", "distinctUntilChanged", "switchMap", "takeUntil", "ɵɵclassMap", "icon", "suggestion_r6", "country", "AutocompleteComponent_div_6_div_2_Template_div_click_0_listener", "_r11", "ctx_r10", "selectSuggestion", "AutocompleteComponent_div_6_div_2_Template_div_mouseenter_0_listener", "i_r7", "ctx_r12", "selectedIndex", "AutocompleteComponent_div_6_div_2_div_6_Template", "getLocationTypeIcon", "AutocompleteComponent_div_6_div_2_Template", "AutocompleteComponent_div_6_div_3_Template", "suggestions", "autocompleteService", "elementRef", "placeholder", "readonly", "locationSelected", "showSuggestions", "searchSubject", "destroy$", "onChange", "onTouched", "pipe", "query", "searchLocations", "document", "addEventListener", "onDocumentClick", "bind", "ngOnDestroy", "complete", "removeEventListener", "writeValue", "inputElement", "nativeElement", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "disabled", "onInput", "event", "target", "onFocus", "getPopularDestinations", "destinations", "onKeyDown", "preventDefault", "hideSuggestions", "emit", "contains", "AutocompleteService", "ElementRef", "viewQuery", "AutocompleteComponent_Query", "provide", "useExisting", "multi", "AutocompleteComponent_Template", "AutocompleteComponent_i_2_Template", "AutocompleteComponent_Template_input_input_3_listener", "AutocompleteComponent_Template_input_focus_3_listener", "AutocompleteComponent_Template_input_keydown_3_listener", "AutocompleteComponent_div_5_Template", "AutocompleteComponent_div_6_Template", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "SigninComponent_div_13_Template_button_click_8_listener", "_r9", "ctx_r8", "clearError", "errorMessage", "formBuilder", "showPassword", "signinForm", "agency", "<PERSON><PERSON><PERSON><PERSON>", "username", "password", "credentials", "trim", "login", "message", "markFormGroupTouched", "togglePasswordVisibility", "getFormControl", "<PERSON><PERSON><PERSON><PERSON>", "errorType", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "fieldNames", "SigninComponent_Template", "SigninComponent_div_13_Template", "SigninComponent_Template_form_ngSubmit_14_listener", "SigninComponent_div_23_Template", "SigninComponent_div_32_Template", "SigninComponent_Template_button_click_42_listener", "SigninComponent__svg_svg_43_Template", "SigninComponent__svg_svg_44_Template", "SigninComponent_div_45_Template", "SigninComponent_span_47_Template", "SigninComponent_span_48_Template", "ɵɵattribute", "invalid", "createUrlTree", "ɵɵinject", "factory", "ɵfac", "providedIn", "HttpHeaders", "BehaviorSubject", "throwError", "catchError", "environment", "http", "API_URL", "apiUrl", "authEndpoint", "TOKEN_KEY", "USER_KEY", "isAuthenticatedSubject", "hasToken", "isAuthenticated$", "asObservable", "currentUserSubject", "getCurrentUser", "authRequest", "Agency", "User", "Password", "headers", "post", "token", "setToken", "userData", "id", "email", "setUserData", "Error", "responseMessage", "status", "removeToken", "removeUserData", "isTokenExpired", "localStorage", "getItem", "JSON", "parse", "getToken", "setItem", "removeItem", "stringify", "getAuthHeaders", "HttpClient", "of", "mockLocations", "city", "airport", "locations", "filter", "toLowerCase", "slice", "mapApiResponseToLocations", "data", "item", "formatDisplayText", "Observable", "tap", "LATEST_SEARCHES_KEY", "MAX_LATEST_SEARCHES", "latestSearchesSubject", "getLatestSearches", "request", "buildOneWayRequest", "searchFlights", "buildRoundTripRequest", "buildMultiCityRequest", "ProductType", "ServiceTypes", "CheckIn", "DepartureLocations", "parseLocation", "ArrivalLocations", "Passengers", "buildPassengers", "showOnlyNonStopFlight", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightClasses", "getFlightClassCode", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "ReturnDate", "Night", "calculateNights", "locationString", "parts", "toUpperCase", "passengerArray", "count", "checkIn", "checkInDate", "returnDateObj", "timeDiff", "getTime", "ceil", "now", "toString", "searchDate", "searches", "unshift", "limitedSearches", "observer", "dest", "production", "flightEndpoint", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}