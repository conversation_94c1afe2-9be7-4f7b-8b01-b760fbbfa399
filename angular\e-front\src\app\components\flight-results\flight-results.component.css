/* Flight Results Component Styles - Paximum Design */
.flight-results-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background-color: #f8f9fa;
}

/* Search Summary */
.search-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-info h2 {
  margin: 0 0 5px 0;
  color: #333;
  font-size: 1.5rem;
}

.search-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Search Modification Panel */
.search-modification-panel {
  background: white;
  padding: 25px 30px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form .form-row {
  display: flex;
  gap: 20px;
  align-items: end;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  margin-bottom: 8px;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #4a90e2;
}

.date-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.date-input {
  flex: 1;
  padding-right: 60px;
}

.date-controls {
  position: absolute;
  right: 10px;
  display: flex;
  gap: 5px;
}

.date-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.date-btn:hover {
  background: #f0f0f0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-primary {
  background: #4a90e2;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #357abd;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.email-btn {
  background: #28a745;
}

.email-btn:hover {
  background: #218838;
}

/* Results Section */
.results-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Airline Filter Tabs */
.airline-filters {
  background: #f8f9fa;
  padding: 20px 30px;
  border-bottom: 1px solid #e0e0e0;
}

.filter-tabs {
  display: flex;
  gap: 15px;
  align-items: center;
}

.airline-tab {
  padding: 10px 15px;
  border: 2px solid #e0e0e0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.airline-tab:hover {
  border-color: #4a90e2;
}

.airline-tab.active {
  border-color: #4a90e2;
  background: #e3f2fd;
}

.airline-logo {
  height: 30px;
  width: auto;
  object-fit: contain;
}

.airline-logo-placeholder {
  width: 40px;
  height: 30px;
  background: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
}

.airline-code {
  font-size: 10px;
  font-weight: 600;
  color: #666;
}

/* Flight Results Grid */
.flight-results-grid,
.sample-price-grid {
  padding: 30px;
}

.grid-header {
  display: grid;
  grid-template-columns: 150px repeat(6, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.stop-category-header {
  /* Empty header cell */
}

.airline-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.airline-logo-small {
  height: 25px;
  width: auto;
  object-fit: contain;
}

.airline-logo-placeholder-small {
  width: 35px;
  height: 25px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
}

.airline-code-small {
  font-size: 9px;
  font-weight: 600;
  color: #666;
}

.grid-row {
  display: grid;
  grid-template-columns: 150px repeat(6, 1fr);
  gap: 15px;
  margin-bottom: 15px;
  align-items: center;
}

.stop-category {
  display: flex;
  align-items: center;
  padding: 15px 0;
}

.stop-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.price-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  min-height: 60px;
  background: white;
  transition: all 0.3s ease;
}

.price-cell:hover {
  border-color: #4a90e2;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
  cursor: pointer;
}

.price-info {
  text-align: center;
}

.price {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.currency {
  display: block;
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.no-flight {
  color: #999;
  font-size: 16px;
  font-weight: 500;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.no-results i {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 20px;
}

.no-results h3 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #333;
}

.no-results p {
  font-size: 16px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .grid-header,
  .grid-row {
    grid-template-columns: 120px repeat(6, 1fr);
    gap: 10px;
  }

  .airline-logo-small {
    height: 20px;
  }

  .price {
    font-size: 16px;
  }
}

@media (max-width: 992px) {
  .search-form .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 10px;
  }

  .grid-header,
  .grid-row {
    grid-template-columns: 100px repeat(3, 1fr);
  }

  /* Hide some airline columns on smaller screens */
  .grid-header > div:nth-child(n+5),
  .grid-row > div:nth-child(n+5) {
    display: none;
  }
}

@media (max-width: 768px) {
  .flight-results-container {
    padding: 15px;
  }

  .search-modification-panel,
  .results-section {
    padding: 20px 15px;
  }

  .search-title {
    font-size: 20px;
  }

  .filter-tabs {
    flex-wrap: wrap;
    gap: 10px;
  }

  .airline-logo {
    height: 25px;
  }

  .grid-header,
  .grid-row {
    grid-template-columns: 80px repeat(2, 1fr);
    gap: 8px;
  }

  /* Hide more airline columns on mobile */
  .grid-header > div:nth-child(n+4),
  .grid-row > div:nth-child(n+4) {
    display: none;
  }

  .price-cell {
    padding: 10px 5px;
    min-height: 50px;
  }

  .price {
    font-size: 14px;
  }

  .currency {
    font-size: 11px;
  }
}

/* Loading State */
.btn .fa-spinner {
  margin-right: 8px;
}

/* Hover Effects */
.price-cell:hover .price {
  color: #4a90e2;
}

.price-cell:hover {
  transform: translateY(-2px);
}

/* Focus States */
.form-control:focus {
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
}

/* Flight Cards - Paximum Style */
.flights-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.flight-card.paximum-style {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.flight-card.paximum-style:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Flight Header */
.flight-header {
  display: grid;
  grid-template-columns: 200px 1fr 150px;
  gap: 20px;
  padding: 20px;
  align-items: center;
}

/* Airline Section */
.airline-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.airline-logo {
  width: 60px;
  height: 60px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.flight-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.flight-number {
  font-weight: bold;
  font-size: 1.1rem;
  color: #333;
}

.aircraft-type {
  font-size: 0.9rem;
  color: #666;
}

.seat-info, .baggage-info {
  font-size: 0.8rem;
  color: #888;
  display: flex;
  align-items: center;
  gap: 5px;
}

.seat-info i, .baggage-info i {
  color: #007bff;
}

/* Route Section */
.route-section {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 20px;
  align-items: center;
}

.departure-info, .arrival-info {
  text-align: center;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.section-header i {
  color: #007bff;
}

.date-time {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
}

.time {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.airport {
  font-size: 0.9rem;
  color: #666;
}

/* Flight Duration */
.flight-duration {
  text-align: center;
  padding: 0 20px;
}

.duration {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.stops-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.stops-badge.direct {
  background-color: #d4edda;
  color: #155724;
}

.stops-badge.stops {
  background-color: #fff3cd;
  color: #856404;
}

.refund-status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.refund-status.refundable {
  background-color: #d4edda;
  color: #155724;
}

.refund-status.non-refundable {
  background-color: #f8d7da;
  color: #721c24;
}

/* Price Section */
.price-section {
  text-align: center;
  padding: 0 20px;
}

.total-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.price-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 5px;
}

.currency {
  font-size: 1.1rem;
  font-weight: bold;
  color: #333;
}

.amount {
  font-size: 1.8rem;
  font-weight: bold;
  color: #007bff;
}

.price-icon {
  color: #ffc107;
  font-size: 1.2rem;
}

.price-per-adult {
  font-size: 0.8rem;
  color: #666;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  position: relative;
}

.left-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.action-airline-logo {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.quote-section {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.quote-checkbox {
  margin-right: 8px;
}

.quote-note {
  font-size: 0.8rem;
  color: #666;
  font-style: italic;
}

.right-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.right-actions .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  background: white;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.right-actions .btn:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.select-btn {
  background-color: #28a745 !important;
  color: white !important;
  border-color: #28a745 !important;
  font-weight: bold;
}

.select-btn:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
}

.restriction-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background-color: #6c757d;
  color: white;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
}

/* Loading and Error States */
.loading-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert {
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* Spinner */
.spinner-border {
  width: 3rem;
  height: 3rem;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg);
  }
}

.text-primary {
  color: #007bff !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Button Overrides */
.btn-outline-primary {
  background: white;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn-outline-primary:hover {
  background: #007bff;
  color: white;
}

.btn-outline-secondary {
  background: white;
  color: #6c757d;
  border: 1px solid #6c757d;
}

.btn-outline-secondary:hover {
  background: #6c757d;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
  border: 1px solid #28a745;
}

.btn-success:hover {
  background: #218838;
  border-color: #1e7e34;
}
