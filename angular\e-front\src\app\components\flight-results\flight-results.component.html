<!-- Flight Results Component with Paximum-style design -->
<div class="flight-results-container">
  <!-- Search Summary -->
  <div class="search-summary">
    <div class="search-info">
      <h2>Flight Search Results</h2>
      <p>{{ searchSummary }}</p>
    </div>

    <!-- Modify Search Button -->
    <button class="btn btn-outline-primary" (click)="modifySearch()">
      <i class="fas fa-edit"></i> Modify Search
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p>Searching for flights...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error" class="alert alert-danger">
    <h4>Error</h4>
    <p>{{ error }}</p>
    <button class="btn btn-primary" (click)="retrySearch()">Try Again</button>
  </div>

  <!-- Results -->
  <div *ngIf="!isLoading && !error && flights.length > 0" class="results-container">
    <!-- Flight Results with Paximum-style design -->
    <div class="flights-list">
      <div *ngFor="let flight of flights; let i = index" class="flight-card paximum-style">
        <!-- Flight Header with airline info -->
        <div class="flight-header">
          <div class="airline-section">
            <img [src]="flight.airlineLogo" [alt]="flight.airline" class="airline-logo">
            <div class="flight-info">
              <div class="flight-number">{{ flight.flightNumber }}</div>
              <div class="aircraft-type">{{ flight.aircraftType || 'Economy Standard' }}</div>
              <div class="seat-info">
                <i class="fas fa-chair"></i> {{ flight.availableSeats || '32B' }}
              </div>
              <div class="baggage-info">
                <i class="fas fa-suitcase"></i> {{ flight.baggageAllowance || '2PC' }}
              </div>
            </div>
          </div>

          <!-- Flight Route Information -->
          <div class="route-section">
            <div class="departure-info">
              <div class="section-header">
                <i class="fas fa-plane-departure"></i>
                <span>Departure</span>
              </div>
              <div class="date-time">{{ formatDate(flight.departureDate) }}</div>
              <div class="time">{{ flight.departureTime }}</div>
              <div class="airport">{{ flight.departureAirport }}</div>
            </div>

            <div class="flight-duration">
              <div class="section-header">
                <i class="fas fa-clock"></i>
                <span>Total Time</span>
              </div>
              <div class="duration">{{ flight.duration }}</div>
              <div class="stops-badge" [ngClass]="{'direct': flight.stops === 0, 'stops': flight.stops > 0}">
                {{ flight.stops === 0 ? 'DIRECT' : flight.stops + ' STOP' }}
              </div>
              <div class="refund-status" [ngClass]="{'refundable': flight.refundable, 'non-refundable': !flight.refundable}">
                {{ flight.refundable ? 'REFUNDABLE' : 'NON REFUNDABLE' }}
              </div>
            </div>

            <div class="arrival-info">
              <div class="section-header">
                <i class="fas fa-plane-arrival"></i>
                <span>Arrival</span>
              </div>
              <div class="date-time">{{ formatDate(flight.arrivalDate) }}</div>
              <div class="time">{{ flight.arrivalTime }}</div>
              <div class="airport">{{ flight.arrivalAirport }}</div>
            </div>
          </div>

          <!-- Price Section -->
          <div class="price-section">
            <div class="total-label">TOTAL</div>
            <div class="price-display">
              <span class="currency">{{ flight.currency }}</span>
              <span class="amount">{{ flight.price }}</span>
              <i class="fas fa-money-bill-wave price-icon"></i>
            </div>
            <div class="price-per-adult">{{ flight.price }} {{ flight.currency }} / ADULT</div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <div class="left-actions">
            <img src="assets/images/airline-logo-small.png" alt="Airline" class="action-airline-logo">
            <div class="quote-section">
              <input type="checkbox" id="quote-{{ i }}" class="quote-checkbox">
              <label for="quote-{{ i }}">Send Quote.. ?</label>
              <div class="quote-note">Note that price may change before confirming</div>
            </div>
          </div>

          <div class="right-actions">
            <button class="btn btn-outline-secondary">AVAILABILITY</button>
            <button class="btn btn-outline-secondary">PRICE BREAKDOWN</button>
            <button class="btn btn-outline-secondary">FARE RULES</button>
            <button class="btn btn-outline-secondary">MORE DETAILS</button>
            <button class="btn btn-success select-btn">SELECT NOW</button>
          </div>

          <div class="restriction-badge" *ngIf="flight.restricted">
            RESTRICTED
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- No Results -->
  <div *ngIf="!isLoading && !error && flights.length === 0" class="no-results">
    <div class="text-center">
      <i class="fas fa-plane-slash fa-3x text-muted mb-3"></i>
      <h4>No flights found</h4>
      <p>Try adjusting your search criteria or dates.</p>
      <button class="btn btn-primary" (click)="modifySearch()">Modify Search</button>
    </div>
  </div>
</div>
