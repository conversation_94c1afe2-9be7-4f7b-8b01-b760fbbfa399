<div class="flight-container">
  <!-- Main Content -->
  <div class="flight-content">
    <!-- Left Panel - Flight Search -->
    <div class="flight-search-panel">
      <div class="search-header">
        <div class="search-title">
          <i class="fas fa-plane"></i>
          <h2>Search and Book Flights</h2>
        </div>
        <p class="search-subtitle">We're bringing you a new level of comfort</p>
      </div>

      <form [formGroup]="flightForm" (ngSubmit)="onSubmit()" class="flight-form">
        <!-- Trip Type Selection -->
        <div class="trip-type-selector">
          <button
            type="button"
            class="trip-type-btn"
            [class.active]="tripType === 'oneWay'"
            (click)="onTripTypeChange('oneWay')">
            One way
          </button>
          <button
            type="button"
            class="trip-type-btn"
            [class.active]="tripType === 'roundTrip'"
            (click)="onTripTypeChange('roundTrip')">
            Round Trip
          </button>
          <button
            type="button"
            class="trip-type-btn"
            [class.active]="tripType === 'multiCity'"
            (click)="onTripTypeChange('multiCity')">
            Multi-City/Stop-Overs
          </button>
        </div>

        <!-- Location and Date Selection -->
        <div class="location-date-section">
          <!-- Flight Segments -->
          <div class="flight-segments">
            <!-- First Segment (always visible) -->
            <div class="flight-segment">
              <div class="segment-row">
                <!-- From Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    formControlName="from"
                    placeholder="Leaving from (City, Country Or Specific Airport)"
                    icon="fas fa-plane-departure"
                    (locationSelected)="onFromLocationSelected($event)">
                  </app-autocomplete>
                  <div class="error-message" *ngIf="getErrorMessage('from')">
                    {{ getErrorMessage('from') }}
                  </div>
                </div>

                <!-- To Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    formControlName="to"
                    placeholder="Going to (City, Country Or Specific Airport)"
                    icon="fas fa-plane-arrival"
                    (locationSelected)="onToLocationSelected($event)">
                  </app-autocomplete>
                  <div class="error-message" *ngIf="getErrorMessage('to')">
                    {{ getErrorMessage('to') }}
                  </div>
                </div>

                <!-- Departure Date -->
                <div class="form-group date-group">
                  <div class="date-input-wrapper">
                    <input
                      type="date"
                      formControlName="departureDate"
                      placeholder="Choose A Date"
                      class="date-input">
                    <i class="fas fa-calendar-alt date-icon"></i>
                  </div>
                  <div class="error-message" *ngIf="getErrorMessage('departureDate')">
                    {{ getErrorMessage('departureDate') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Second Segment (for Round Trip) -->
            <div class="flight-segment" *ngIf="showReturnDate">
              <div class="segment-row">
                <!-- Return From Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    [ngModel]="flightForm.get('to')?.value"
                    placeholder="Leaving from (City, Country Or Specific Airport)"
                    icon="fas fa-plane-departure"
                    [readonly]="true">
                  </app-autocomplete>
                </div>

                <!-- Return To Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    [ngModel]="flightForm.get('from')?.value"
                    placeholder="Going to (City, Country Or Specific Airport)"
                    icon="fas fa-plane-arrival"
                    [readonly]="true">
                  </app-autocomplete>
                </div>

                <!-- Return Date -->
                <div class="form-group date-group">
                  <div class="date-input-wrapper">
                    <input
                      type="date"
                      formControlName="returnDate"
                      placeholder="Choose A Date"
                      class="date-input">
                    <i class="fas fa-calendar-alt date-icon"></i>
                  </div>
                  <div class="error-message" *ngIf="getErrorMessage('returnDate')">
                    {{ getErrorMessage('returnDate') }}
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Segments for Multi-City -->
            <div class="flight-segment" *ngFor="let segment of additionalSegments; let i = index">
              <div class="segment-row">
                <!-- From Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    [(ngModel)]="segment.from"
                    placeholder="Leaving from (City, Country Or Specific Airport)"
                    icon="fas fa-plane-departure"
                    (locationSelected)="onSegmentLocationSelected($event, i, 'from')">
                  </app-autocomplete>
                </div>

                <!-- To Location -->
                <div class="form-group location-group">
                  <app-autocomplete
                    [(ngModel)]="segment.to"
                    placeholder="Going to (City, Country Or Specific Airport)"
                    icon="fas fa-plane-arrival"
                    (locationSelected)="onSegmentLocationSelected($event, i, 'to')">
                  </app-autocomplete>
                </div>

                <!-- Date -->
                <div class="form-group date-group">
                  <div class="date-input-wrapper">
                    <input
                      type="date"
                      [(ngModel)]="segment.date"
                      placeholder="Choose A Date"
                      class="date-input">
                    <i class="fas fa-calendar-alt date-icon"></i>
                  </div>
                </div>

                <!-- Remove Segment Button -->
                <button type="button" class="remove-segment-btn" (click)="removeSegment(i)">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <!-- Add Sector Button -->
            <div class="add-sector-section" *ngIf="tripType === 'multiCity'">
              <button type="button" class="add-sector-btn" (click)="addSegment()">
                <i class="fas fa-plus"></i>
                Add Sector
              </button>
            </div>
          </div>
        </div>

        <!-- Passenger and Class Selection -->
        <div class="passenger-class-section">
          <!-- Passenger Count -->
          <div class="form-group passenger-group">
            <label>Passenger & Class of travel</label>
            <div class="passenger-controls">
              <!-- Adults -->
              <div class="passenger-type">
                <span class="passenger-icon"><i class="fas fa-user"></i></span>
                <span class="passenger-count">{{ flightForm.get('adults')?.value || 1 }}</span>
                <div class="counter-controls">
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('adults', false)">-</button>
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('adults', true)">+</button>
                </div>
              </div>

              <!-- Children -->
              <div class="passenger-type">
                <span class="passenger-icon"><i class="fas fa-child"></i></span>
                <span class="passenger-count">{{ flightForm.get('children')?.value || 0 }}</span>
                <div class="counter-controls">
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('children', false)">-</button>
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('children', true)">+</button>
                </div>
              </div>

              <!-- Infants -->
              <div class="passenger-type">
                <span class="passenger-icon"><i class="fas fa-baby"></i></span>
                <span class="passenger-count">{{ flightForm.get('infants')?.value || 0 }}</span>
                <div class="counter-controls">
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('infants', false)">-</button>
                  <button type="button" class="counter-btn" (click)="updatePassengerCount('infants', true)">+</button>
                </div>
              </div>

              <!-- Class Selection -->
              <div class="class-selection">
                <button
                  type="button"
                  class="class-btn"
                  [class.active]="selectedClass === 'economy'"
                  (click)="onClassChange('economy')">
                  <i class="fas fa-chair"></i> Economy
                </button>
              </div>
            </div>
          </div>

          <!-- Preferred Airline -->
          <div class="form-group airline-group">
            <label for="preferredAirline">Preferred Airline</label>
            <select id="preferredAirline" formControlName="preferredAirline" class="airline-select">
              <option value="">Preferred Airline</option>
              <option value="TK">Turkish Airlines</option>
              <option value="AF">Air France</option>
              <option value="LH">Lufthansa</option>
              <option value="EK">Emirates</option>
              <option value="QR">Qatar Airways</option>
            </select>
          </div>
        </div>

        <!-- Additional Options -->
        <div class="additional-options">
          <!-- Refundable Fares -->
          <div class="option-group">
            <label class="option-label">Refundable fares</label>
            <div class="option-controls">
              <select formControlName="refundableFares" class="option-select">
                <option value="false">--All--</option>
                <option value="true">Refundable Only</option>
              </select>
            </div>
          </div>

          <!-- Baggage -->
          <div class="option-group">
            <label class="option-label">Baggage</label>
            <div class="option-controls">
              <select formControlName="baggage" class="option-select">
                <option *ngFor="let option of baggageOptions" [value]="option.value">
                  {{ option.label }}
                </option>
              </select>
            </div>
          </div>

          <!-- Calendar -->
          <div class="option-group">
            <label class="option-label">Calendar</label>
            <div class="option-controls">
              <div class="calendar-toggle">
                <input
                  type="checkbox"
                  id="calendar"
                  formControlName="calendar"
                  (change)="toggleCalendar()">
                <label for="calendar" class="calendar-label">
                  <span class="calendar-days" *ngIf="showCalendar">
                    <select formControlName="calendarDays" class="calendar-select">
                      <option *ngFor="let option of calendarDays" [value]="option.value">
                        {{ option.label }}
                      </option>
                    </select>
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Button -->
        <div class="search-button-section">
          <button
            type="submit"
            class="search-btn"
            [disabled]="isLoading || !flightForm.valid">
            <span *ngIf="!isLoading">SEARCH NOW</span>
            <span *ngIf="isLoading">
              <i class="fas fa-spinner fa-spin"></i> Searching...
            </span>
          </button>
        </div>
      </form>
    </div>

    <!-- Right Panel - Latest Searches -->
    <div class="latest-searches-panel">
      <div class="searches-header">
        <h3>Latest Searches</h3>
        <p>We're bringing you a new level of comfort</p>
      </div>

      <div class="searches-list">
        <div
          *ngFor="let search of latestSearches$ | async"
          class="search-item"
          (click)="loadLatestSearch(search)">
          <div class="search-icon">
            <i class="fas fa-plane"></i>
          </div>
          <div class="search-details">
            <div class="search-route">
              Coming from <strong>{{ search.from }}</strong> - <strong>{{ search.to }}</strong> on {{ search.date | date:'MMM d, yyyy' }}
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div *ngIf="(latestSearches$ | async)?.length === 0" class="empty-searches">
          <i class="fas fa-search"></i>
          <p>No recent searches</p>
          <small>Your recent flight searches will appear here</small>
        </div>
      </div>

      <!-- Clear searches button -->
      <div class="searches-actions" *ngIf="(latestSearches$ | async)?.length! > 0">
        <button type="button" class="clear-btn" (click)="clearLatestSearches()">
          <i class="fas fa-trash"></i> Clear All
        </button>
      </div>
    </div>
  </div>
</div>
